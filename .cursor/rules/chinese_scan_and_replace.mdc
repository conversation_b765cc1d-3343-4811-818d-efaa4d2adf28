请帮我完成Java代码国际化处理的全部流程，包括：

1. 自动扫描提供的Java代码文件，检测所有中文字符串
2. 自动为每个中文字符串生成合适的I18NKeys常量名
3. 自动生成需要添加到I18NKeys类的常量定义代码块
4. 自动修改原代码，将所有中文替换为I18N.text(I18NKeys.XXX)形式

规则说明：
- 常量命名：根据中文含义生成全大写英文名，用下划线连接
- 常量格式：public static final String KEY_NAME = "fmcg.crm.fmcg.tpm.KEY_NAME";
- 替换格式：将"中文内容"替换为I18N.text(I18NKeys.KEY_NAME)
- 注释中的中文无需替换
- 保持代码逻辑不变

请提供三个部分的结果：
1. 修改后的Java代码（已完成所有中文替换）
2. 需要添加到I18NKeys类的常量定义代码块
3. 中文文本与国际化键的对照表，方便后续维护

请确保生成的常量名有意义且不重复，并尽可能简洁。