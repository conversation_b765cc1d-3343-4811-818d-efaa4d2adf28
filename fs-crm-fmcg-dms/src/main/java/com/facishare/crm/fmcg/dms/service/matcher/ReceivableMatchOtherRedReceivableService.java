package com.facishare.crm.fmcg.dms.service.matcher;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.ReceivableMatchService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FmcgSalesProxy;
import com.fmcg.framework.http.contract.sales.QueryRelatedOrder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class ReceivableMatchOtherRedReceivableService extends ReceivableMatchService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private FmcgSalesProxy fmcgSalesProxy;

    @Override
    protected BigDecimal calculateMaxMatchableAmount(String tenantId, String salesOrderId) {
        IObjectData order = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), salesOrderId, ApiNames.SALES_ORDER_OBJ);
        BigDecimal historicalDeductionAmount = order.get(SalesOrderObjFields.HIST_OFFSET_AMOUNT, BigDecimal.class);
        if (Objects.isNull(historicalDeductionAmount) || historicalDeductionAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        } else {
            BigDecimal matchedHistoricalDeductionAmount = calculateMatchedHistoricalDeductionAmount(tenantId, salesOrderId);
            return historicalDeductionAmount.subtract(matchedHistoricalDeductionAmount);
        }
    }

    private BigDecimal calculateMatchedHistoricalDeductionAmount(String tenantId, String salesOrderId) {
        List<IObjectData> blueReceivableDetails = queryBlueReceivableDetails(tenantId, salesOrderId);
        List<IObjectData> matchNoteDetails = queryMatchNoteDetails(tenantId, blueReceivableDetails);

        if (CollectionUtils.isEmpty(matchNoteDetails)) {
            return BigDecimal.ZERO;
        }

        List<IObjectData> relatedRedReceivableDetails = queryRedReceivableDetails(tenantId, salesOrderId);
        Set<String> relatedRedReceivableDetailIds = relatedRedReceivableDetails.stream().map(DBRecord::getId).collect(Collectors.toSet());

        BigDecimal matchedAmount = BigDecimal.ZERO;
        for (IObjectData matchNoteDetail : matchNoteDetails) {
            String creditDetailDataId = matchNoteDetail.get(MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID, String.class);
            if (!relatedRedReceivableDetailIds.contains(creditDetailDataId)) {
                BigDecimal matchAmount = matchNoteDetail.get(MatchNoteDetailFields.THIS_MATCH_AMOUNT, BigDecimal.class);
                matchedAmount = matchAmount.add(matchAmount);
            }
        }
        return matchedAmount;
    }

    private List<IObjectData> queryMatchNoteDetails(String tenantId, List<IObjectData> blueReceivables) {
        List<String> receivableIds = blueReceivables.stream().map(DBRecord::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(receivableIds)) {
            return Lists.newArrayList();
        }

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));

        IFilter idFilter = new Filter();
        idFilter.setFieldName(MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(receivableIds);

        IFilter creditApiNameFilter = new Filter();
        creditApiNameFilter.setFieldName(MatchNoteDetailFields.CREDIT_DETAIL_API_NAME);
        creditApiNameFilter.setOperator(Operator.EQ);
        creditApiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ));

        IFilter creditMatchAmount = new Filter();
        creditMatchAmount.setFieldName(MatchNoteDetailFields.CREDIT_MATCH_AMOUNT);
        creditMatchAmount.setOperator(Operator.LT);
        creditMatchAmount.setFieldValues(Lists.newArrayList("0"));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(apiNameFilter, idFilter, creditApiNameFilter, creditMatchAmount);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.MATCH_NOTE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        MatchNoteDetailFields.THIS_MATCH_AMOUNT,
                        MatchNoteDetailFields.CREDIT_MATCH_AMOUNT,
                        MatchNoteDetailFields.CREDIT_API_NAME,
                        MatchNoteDetailFields.CREDIT_DATA_ID,
                        MatchNoteDetailFields.DEBIT_API_NAME,
                        MatchNoteDetailFields.DEBIT_DATA_ID,
                        MatchNoteDetailFields.CREDIT_DETAIL_API_NAME,
                        MatchNoteDetailFields.CREDIT_DETAIL_DATA_ID,
                        MatchNoteDetailFields.DEBIT_DETAIL_API_NAME,
                        MatchNoteDetailFields.DEBIT_DETAIL_DATA_ID
                )
        );
    }

    private List<IObjectData> queryRedReceivableDetails(String tenantId, String salesOrderId) {
        List<String> returnedIds = queryReturnedIds(tenantId, salesOrderId);

        if (CollectionUtils.isEmpty(returnedIds)) {
            return Lists.newArrayList();
        }

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.RETURNED_GOODS_INVOICE_OBJ));

        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_DATA_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(returnedIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(apiNameFilter, idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        AccountsReceivableDetailFields.SOURCE_DATA_ID,
                        AccountsReceivableDetailFields.SOURCE_API_NAME
                )
        );
    }

    private List<IObjectData> queryBlueReceivableDetails(String tenantId, String salesOrderId) {
        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.SALES_ORDER_OBJ));

        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableDetailFields.SOURCE_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(salesOrderId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(apiNameFilter, idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        AccountsReceivableDetailFields.SOURCE_DATA_ID,
                        AccountsReceivableDetailFields.SOURCE_API_NAME
                )
        );
    }

    @Override
    protected List<IObjectData> queryRelatedData(String tenantId, String dimensionDataId) {
        IObjectData salesOrder = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), dimensionDataId, ApiNames.SALES_ORDER_OBJ);
        String accountId = salesOrder.get(SalesOrderObjFields.ACCOUNT_ID, String.class);

        IFilter accountIdFilter = new Filter();
        accountIdFilter.setFieldName(AccountsReceivableNoteFields.ACCOUNT_ID);
        accountIdFilter.setOperator(Operator.EQ);
        accountIdFilter.setFieldValues(Lists.newArrayList(accountId));

        IFilter amountFilter = new Filter();
        amountFilter.setFieldName(AccountsReceivableNoteFields.PRICE_TAX_TOTAL_AMOUNT);
        amountFilter.setOperator(Operator.LT);
        amountFilter.setFieldValues(Lists.newArrayList("0"));

        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                QueryDataUtil.minimumQuery(accountIdFilter, amountFilter),
                Lists.newArrayList(CommonFields.ID)
        );

        if (CollectionUtils.isEmpty(data)) {
            return Lists.newArrayList();
        }

        IFilter masterIdFilter = new Filter();
        masterIdFilter.setFieldName(AccountsReceivableDetailFields.AR_ID);
        masterIdFilter.setOperator(Operator.IN);
        masterIdFilter.setFieldValues(data.stream().map(DBRecord::getId).collect(Collectors.toList()));

        IFilter detailAmountFilter = new Filter();
        detailAmountFilter.setFieldName(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT);
        detailAmountFilter.setOperator(Operator.LT);
        detailAmountFilter.setFieldValues(Lists.newArrayList("0"));

        SearchTemplateQuery detailStq = QueryDataUtil.minimumQuery(masterIdFilter, detailAmountFilter);

        List<String> returnedIds = queryReturnedIds(tenantId, dimensionDataId);
        List<String> goodsReceivedNoteIds = queryGoodsReceivedNoteIds(tenantId, returnedIds);

        if (CollectionUtils.isNotEmpty(goodsReceivedNoteIds)) {
            if (!TPMGrayUtils.accountReceivableDetailUseWhatField(tenantId)) {
                IFilter goodsReceivedNoteIdFilter = new Filter();
                goodsReceivedNoteIdFilter.setFieldName(AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID);
                goodsReceivedNoteIdFilter.setOperator(Operator.NIN);
                goodsReceivedNoteIdFilter.setFieldValues(goodsReceivedNoteIds);

                detailStq.getFilters().add(goodsReceivedNoteIdFilter);
            } else {
                IFilter apiNameFilter = new Filter();
                apiNameFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME);
                apiNameFilter.setOperator(Operator.EQ);
                apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.GOODS_RECEIVED_NOTE_OBJ));
                detailStq.getFilters().add(apiNameFilter);

                IFilter goodsReceivedNoteIdFilter = new Filter();
                goodsReceivedNoteIdFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID);
                goodsReceivedNoteIdFilter.setOperator(Operator.NIN);
                goodsReceivedNoteIdFilter.setFieldValues(goodsReceivedNoteIds);

                detailStq.getFilters().add(goodsReceivedNoteIdFilter);
            }
        }

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                detailStq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        AccountsReceivableDetailFields.PRICE_TAX_AMOUNT,
                        AccountsReceivableDetailFields.AR_ID,
                        AccountsReceivableDetailFields.ORDER_ID,
                        AccountsReceivableDetailFields.ORDER_PRODUCT_ID,
                        AccountsReceivableDetailFields.DELIVERY_NOTE_ID,
                        AccountsReceivableDetailFields.DELIVERY_NOTE_PRODUCT_ID,
                        AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID,
                        AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_PRODUCT_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_DATA_ID,
                        AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DETAIL_API_NAME,
                        AccountsReceivableDetailFields.UNIT,
                        AccountsReceivableDetailFields.AR_QUANTITY,
                        AccountsReceivableDetailFields.SKU_ID,
                        AccountsReceivableDetailFields.TAX_PRICE
                )
        );
    }

    @Override
    protected List<IObjectData> filterRelatedData(String tenantId, List<IObjectData> relatedData) {

        List<IObjectData> filterRelatedData = super.filterRelatedData(tenantId, relatedData);
        List<String> accountsReceivableIds =
                filterRelatedData.stream()
                        .filter(v -> StringUtils.isNotEmpty(v.get(AccountsReceivableDetailFields.AR_ID, String.class)))
                        .map(data -> data.get(AccountsReceivableDetailFields.AR_ID, String.class)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(accountsReceivableIds)) {
            return filterRelatedData;
        }
        List<String> filter = queryNotOpeningBalanceReceivableIds(tenantId, new HashSet<>(accountsReceivableIds));

        return filterRelatedData.stream()
                .filter(detail -> filter.contains(detail.get(AccountsReceivableDetailFields.AR_ID, String.class)))
                .collect(Collectors.toList());
    }

    private List<String> queryGoodsReceivedNoteIds(String tenantId, List<String> returnedIds) {
        if (CollectionUtils.isEmpty(returnedIds)) {
            return Lists.newArrayList();
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteFields.RETURN_NOTE_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(returnedIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        ).stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryReturnedIds(String tenantId, String orderId) {
        int tenantIdInt = Integer.parseInt(tenantId);

        List<String> relatedReturnOrderIds = Lists.newArrayList();

        QueryRelatedOrder.Arg arg = new QueryRelatedOrder.Arg();
        arg.setTenantId(tenantIdInt);
        arg.setApiName(ApiNames.SALES_ORDER_OBJ);
        arg.setDataId(orderId);
        arg.setRelatedApiName(ApiNames.RETURNED_GOODS_INVOICE_OBJ);

        QueryRelatedOrder.Result relatedOrderResult = fmcgSalesProxy.queryRelatedOrder(tenantIdInt, -10000, arg);

        if (ApiNames.RETURNED_GOODS_INVOICE_OBJ.equals(relatedOrderResult.getRelatedApiName()) && CollectionUtils.isNotEmpty(relatedOrderResult.getDataIdList())) {
            relatedReturnOrderIds.addAll(relatedOrderResult.getDataIdList());
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName(ReturnedGoodsInvoiceFields.ORDER_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(orderId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        relatedReturnOrderIds.addAll(QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                stq,
                Lists.newArrayList(CommonFields.ID)
        ).stream().map(DBRecord::getId).collect(Collectors.toList()));

        IFilter detailIdFilter = new Filter();
        detailIdFilter.setFieldName(ReturnedGoodsInvoiceProductFields.ORDER_ID);
        detailIdFilter.setOperator(Operator.EQ);
        detailIdFilter.setFieldValues(Lists.newArrayList(orderId));

        SearchTemplateQuery detailStq = QueryDataUtil.minimumQuery(detailIdFilter);

        relatedReturnOrderIds.addAll(QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                detailStq,
                Lists.newArrayList(CommonFields.ID, ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID)
        ).stream().map(detail -> detail.get(ReturnedGoodsInvoiceProductFields.RETURNED_GOODS_INV_ID, String.class)).collect(Collectors.toList()));

        return relatedReturnOrderIds.stream().filter(id -> !Strings.isNullOrEmpty(id)).distinct().collect(Collectors.toList());
    }

    @Override
    protected BigDecimal calculateMatchableAmountOfRelatedDatum(String tenantId, IObjectData relatedDatum) {
        BigDecimal amount = relatedDatum.get(AccountsReceivableDetailFields.PRICE_TAX_AMOUNT, BigDecimal.class).abs();
        BigDecimal matchedAmount = calculateMatchedAmountOfReceivableDetail(tenantId, relatedDatum.getId());
        BigDecimal matchableAmount = amount.subtract(matchedAmount);

        log.info("tenant id : {}, api name : {}, id : {}, amount : {}, matched amount : {}, matchable amount : {}",
                tenantId,
                relatedDatum.getDescribeApiName(),
                relatedDatum.getId(),
                amount,
                matchedAmount,
                matchableAmount
        );

        return matchableAmount;
    }

    @Override
    protected IObjectData buildMatchNoteDetail(FinancialBill receivable, IObjectData receivableDetail, IObjectData relatedDatum, BigDecimal matchAmount) {
        return buildMatchNoteDetail(
                receivable,
                receivableDetail,
                matchAmount,
                matchAmount.negate(),
                ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ,
                relatedDatum.get(AccountsReceivableDetailFields.AR_ID, String.class),
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                relatedDatum.getId());
    }

    @Override
    protected String verificationMethod() {
        return MatchNoteFields.VERIFICATION_METHOD__AR_OFFSET_AR;
    }
}
