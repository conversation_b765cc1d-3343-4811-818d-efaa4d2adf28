package com.facishare.crm.fmcg.dms.service.matcher;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.PayableMatchService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PayableMatchRedPayableService extends PayableMatchService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    protected List<IObjectData> queryRelatedData(String tenantId, String dimensionDataId) {
        //采购订单查询入库单
        List<String> goodsReceivedNoteIds = goodsReceivedNoteIdsByPurchaseOrderId(tenantId, dimensionDataId);

        //入库单查询采购退货单
        List<String> purchaseReturnNoteIds = purchaseReturnNoteIdsByGoodsReceivedNoteIds(tenantId, goodsReceivedNoteIds);
        if (CollectionUtils.isEmpty(purchaseReturnNoteIds)) {
            return Lists.newArrayList();
        }

        //采购退货单查询所有红字应付
        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsPayableDetailFields.SOURCE_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(purchaseReturnNoteIds);

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsPayableDetailFields.SOURCE_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.PURCHASE_RETURN_NOTE_OBJ));

        IFilter redFilter = new Filter();
        redFilter.setFieldName(AccountsPayableDetailFields.PRICE);
        redFilter.setOperator(Operator.LT);
        redFilter.setFieldValues(Lists.newArrayList("0"));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ,
                QueryDataUtil.minimumQuery(idFilter, apiNameFilter, redFilter),
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        AccountsPayableDetailFields.PRICE,
                        AccountsPayableDetailFields.QUANTITY,
                        AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID

                )
        );
    }


    @Override
    protected List<IObjectData> filterRelatedData(String tenantId, List<IObjectData> relatedData) {
        List<IObjectData> filterRelatedData = super.filterRelatedData(tenantId, relatedData);
        List<String> accountPayableIds =
                filterRelatedData.stream()
                        .filter(v -> StringUtils.isNotEmpty(v.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)))
                        .map(v -> v.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountPayableIds)) {
            return filterRelatedData;
        }
        List<String> filter = queryNotOpeningBalancePayableIds(tenantId, new HashSet<>(accountPayableIds));


        return filterRelatedData.stream()
                .filter(accountsPayableDetail -> filter.contains(accountsPayableDetail.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    protected BigDecimal calculateMatchableAmountOfRelatedDatum(String tenantId, IObjectData relatedDatum) {

        BigDecimal amount = relatedDatum.get(AccountsPayableDetailFields.PRICE, BigDecimal.class).abs();
        BigDecimal matchedAmount = calculateMatchedAmount(tenantId, ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ, relatedDatum.getId());
        BigDecimal matchableAmount = amount.subtract(matchedAmount);

        log.info("tenant id : {}, api name : {}, id : {}, amount : {}, matched amount : {}, matchable amount : {}",
                tenantId,
                relatedDatum.getDescribeApiName(),
                relatedDatum.getId(),
                amount,
                matchedAmount,
                matchableAmount
        );

        return matchableAmount;
    }

    @Override
    protected IObjectData buildMatchNoteDetail(FinancialBill payable, IObjectData payableDetail, IObjectData relatedDatum, BigDecimal matchAmount) {

        return buildMatchNoteDetail(
                payable,
                payableDetail,
                matchAmount,
                matchAmount.negate(),
                ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ,
                relatedDatum.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class),
                ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ,
                relatedDatum.getId());
    }

    @Override
    protected String verificationMethod() {
        return MatchNoteFields.VERIFICATION_METHOD__AP_OFFSET_AP;
    }

    private List<String> purchaseReturnNoteIdsByGoodsReceivedNoteIds(String tenantId, List<String> goodsReceivedNoteIds) {
        if (CollectionUtils.isEmpty(goodsReceivedNoteIds)) {
            return Lists.newArrayList();
        }
        IFilter idFilter = new Filter();
        idFilter.setFieldName(PurchaseReturnNoteObjFields.GOODS_RECEIVED_NOTE_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(goodsReceivedNoteIds);


        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PURCHASE_RETURN_NOTE_OBJ,
                QueryDataUtil.minimumQuery(idFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return data.stream().map(DBRecord::getId).collect(Collectors.toList());
    }


    private List<String> goodsReceivedNoteIdsByPurchaseOrderId(String tenantId, String purchaseOrderId) {

        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteFields.PURCHASE_ORDER_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(purchaseOrderId));


        List<IObjectData> data = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_OBJ,
                QueryDataUtil.minimumQuery(idFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return data.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryNotOpeningBalancePayableIds(String tenantId, Set<String> accountsPayableNoteIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(Lists.newArrayList(accountsPayableNoteIds));

        IFilter openingBalanceFilter = new Filter();
        openingBalanceFilter.setFieldName(AccountsPayableNoteFields.BASE);
        openingBalanceFilter.setOperator(Operator.EQ);
        openingBalanceFilter.setFieldValues(Lists.newArrayList("false"));


        List<IObjectData> pays = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_PAYABLE_NOTE_OBJ,
                QueryDataUtil.minimumQuery(idFilter, openingBalanceFilter),
                Lists.newArrayList(
                        CommonFields.ID
                )
        );

        return pays.stream().map(DBRecord::getId).collect(Collectors.toList());
    }
}
