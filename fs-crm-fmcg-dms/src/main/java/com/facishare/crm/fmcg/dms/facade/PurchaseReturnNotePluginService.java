package com.facishare.crm.fmcg.dms.facade;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("purchase_return_note_action_plugin")
public class PurchaseReturnNotePluginService extends PluginBaseService {


    @ServiceMethod("invalid_after")
    public InvalidActionDomainPlugin.Result invalidAfter(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData data = arg.getObjectData().toObjectData();
        String purchaseReturnId = data.getId();
        log.info("purchase_return_note_action_plugin invalid_after :{}:{}", data.getName(), data.getId());
        List<IObjectData> payDetails = payDetails(serviceContext.getTenantId(), PayDetailFields.PURCHASE_RETURN_NOTE_ID, Lists.newArrayList(purchaseReturnId));
        List<String> outboundDeliveryNoteIds = outboundDeliveryNoteIds(serviceContext.getTenantId(), Lists.newArrayList(purchaseReturnId));
        List<IObjectData> accountsPayableDetails = accountsPayableDetails(serviceContext.getTenantId(), AccountsPayableDetailFields.OUTBOUND_DELIVERY_NOTE_ID, outboundDeliveryNoteIds);

        invalidPay(serviceContext, payDetails);
        invalidPayable(serviceContext, accountsPayableDetails);
        invalidMatchNoteWithPayableDetails(serviceContext, accountsPayableDetails);
        return new InvalidActionDomainPlugin.Result();
    }


    @ServiceMethod("bulk_invalid_after")
    public BulkInvalidActionDomainPlugin.Result bulkInvalidAfter(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        List<String> purchaseReturnIds = arg.getObjectDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        log.info("purchase_return_note_action_plugin bulk_invalid_after :{}:{}", purchaseReturnIds, JSON.toJSONString(arg.getObjectDataList().stream().map(v -> v.get(CommonFields.NAME)).collect(Collectors.toList())));

        List<String> outboundDeliveryNoteIds = outboundDeliveryNoteIds(serviceContext.getTenantId(), purchaseReturnIds);
        List<IObjectData> accountsPayableDetails = accountsPayableDetails(serviceContext.getTenantId(), AccountsPayableDetailFields.OUTBOUND_DELIVERY_NOTE_ID, outboundDeliveryNoteIds);

        List<IObjectData> payDetails = payDetails(serviceContext.getTenantId(), PayDetailFields.PURCHASE_RETURN_NOTE_ID, purchaseReturnIds);


        invalidPay(serviceContext, payDetails);
        invalidPayable(serviceContext, accountsPayableDetails);
        invalidMatchNoteWithPayableDetails(serviceContext, accountsPayableDetails);

        return new BulkInvalidActionDomainPlugin.Result();
    }



    private List<String> outboundDeliveryNoteIds(String tenantId, List<String> purchaseReturnIds) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(OutboundDeliveryNoteFields.PURCHASE_RETURN_NOTE_ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(purchaseReturnIds);

        query.setFilters(Lists.newArrayList(filter));

        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                ),
                true
        );
        return result.stream().map(DBRecord::getId).distinct().collect(Collectors.toList());
    }


}
