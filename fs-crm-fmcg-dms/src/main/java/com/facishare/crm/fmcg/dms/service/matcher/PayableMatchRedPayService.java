package com.facishare.crm.fmcg.dms.service.matcher;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.MatchNoteFields;
import com.facishare.crm.fmcg.common.apiname.PayDetailFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.PayableMatchService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PayableMatchRedPayService extends PayableMatchService {

    @Resource
    private ServiceFacade serviceFacade;


    @Override
    protected Map<String, List<IObjectData>> groupByDimensionDataId(FinancialBill payable) {
        return payable.getDetails().stream().filter(detail -> StringUtils.isNotEmpty(detail.get(PayableAutoMatchService.PURCHASE_RETURN_NOTE_ID, String.class)))
                .collect(Collectors.groupingBy(detail -> detail.get(PayableAutoMatchService.PURCHASE_RETURN_NOTE_ID, String.class)));
    }

    @Override
    protected List<IObjectData> queryRelatedData(String tenantId, String dimensionDataId) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(PayDetailFields.PURCHASE_RETURN_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(dimensionDataId));

        IFilter blueFilter = new Filter();
        blueFilter.setFieldName(PayDetailFields.PAY_AMOUNT);
        blueFilter.setOperator(Operator.LT);
        blueFilter.setFieldValues(Lists.newArrayList("0"));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAY_DETAIL_OBJ,
                QueryDataUtil.minimumQuery(idFilter, blueFilter),
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        PayDetailFields.PAY_ID,
                        PayDetailFields.PAY_AMOUNT,
                        PayDetailFields.PURCHASE_ORDER_ID,
                        PayDetailFields.PURCHASE_RETURN_NOTE_ID
                )
        );
    }

    @Override
    protected List<IObjectData> filterRelatedData(String tenantId, List<IObjectData> relatedData) {
        List<IObjectData> filterRelatedData = super.filterRelatedData(tenantId, relatedData);
        List<String> payIds =
                filterRelatedData.stream()
                        .filter(v -> StringUtils.isNotEmpty(v.get(PayDetailFields.PAY_ID, String.class)))
                        .map(v -> v.get(PayDetailFields.PAY_ID, String.class)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(payIds)) {
            return filterRelatedData;
        }
        List<String> filter = queryNotOpeningBalancePayIds(tenantId, new HashSet<>(payIds));


        return filterRelatedData.stream()
                .filter(payDetail -> filter.contains(payDetail.get(PayDetailFields.PAY_ID, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    protected BigDecimal calculateMatchableAmountOfRelatedDatum(String tenantId, IObjectData relatedDatum) {
        BigDecimal amount = relatedDatum.get(PayDetailFields.PAY_AMOUNT, BigDecimal.class).abs();
        BigDecimal matchedAmount = calculateMatchedAmount(tenantId, ApiNames.PAY_DETAIL_OBJ, relatedDatum.getId());
        BigDecimal matchableAmount = amount.subtract(matchedAmount);

        log.info("tenant id : {}, api name : {}, id : {}, amount : {}, matched amount : {}, matchable amount : {}",
                tenantId,
                relatedDatum.getDescribeApiName(),
                relatedDatum.getId(),
                amount,
                matchedAmount,
                matchableAmount
        );

        return matchableAmount;
    }

    @Override
    protected IObjectData buildMatchNoteDetail(FinancialBill receivable, IObjectData receivableDetail, IObjectData relatedDatum, BigDecimal matchAmount) {
        return buildMatchNoteDetail(
                receivable,
                receivableDetail,
                matchAmount.negate(),
                matchAmount.negate(),
                ApiNames.PAY_OBJ,
                relatedDatum.get(PayDetailFields.PAY_ID, String.class),
                ApiNames.PAY_DETAIL_OBJ,
                relatedDatum.getId());
    }

    @Override
    protected String verificationMethod() {
        return MatchNoteFields.VERIFICATION_METHOD__PAY_OFFSET_AP;
    }
}
