package com.facishare.crm.fmcg.dms.business.abstraction;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.dms.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.GetConfigValueByKey;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreate;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public abstract class BaseHandler {
    protected static final String ONLY_KX_ENABLE_PAID_AMOUNT_EXPRESSION_DISABLE_REBATE = "MAX($payment_amount$,$car_sales_deduction_amount$)-$coupon_amount$";
    protected static final String ONLY_KX_ENABLE_PAID_AMOUNT_EXPRESSION_ENABLE_REBATE = "MAX($payment_amount$,$car_sales_deduction_amount$)-$rebate_amount$-$coupon_amount$";

    protected static final String OTHER_PAID_AMOUNT_EXPRESSION_DISABLE_REBATE = "MAX($payment_amount$,$settled_amount$)-$coupon_amount$";
    protected static final String OTHER_PAID_AMOUNT_EXPRESSION_ENABLE_REBATE = "MAX($payment_amount$,$settled_amount$)-$rebate_amount$-$coupon_amount$";

    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    protected FieldBusiness fieldBusiness;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    private BizConfClient bizConfClient;

    protected boolean isOpenRebate(String tenantId) {
        GetConfigValueByKey.Arg getConfigArg = new GetConfigValueByKey.Arg();
        getConfigArg.setKey("rebate");
        GetConfigValueByKey.Result configValueByKey = paasDataProxy.getConfigValueByKey(Integer.parseInt(tenantId), -10000, getConfigArg);
        if (configValueByKey.getCode() != 0) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_BASE_HANDLER_0));
        }
        return Objects.equals(configValueByKey.getData().getValue(), "1");
    }


    protected PaasDescribeCreate.Arg buildDescribeArg(String apiName) {
        PaasDescribeCreate.Arg arg = new PaasDescribeCreate.Arg();
        arg.setActive(true);
        arg.setIncludeLayout(true);

        try {
            arg.setJsonData(describe(apiName));
            arg.setJsonLayout(layout(apiName));
            arg.setJsonListLayout(listLayout(apiName));
        } catch (IOException ex) {
            throw new MetaDataBusinessException("init describe error,", ex);
        }

        arg.setLayoutType("detail");
        return arg;
    }

    private String describe(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%s.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    protected String layout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sDetailLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    protected String layoutRule(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sLayoutRule.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    private String listLayout(String apiName) throws IOException {
        File mainJsonFile = ResourceUtils.getFile(String.format("classpath:dms/object/%sMobileLayout.json", apiName));
        return new String(Files.readAllBytes(mainJsonFile.toPath()));
    }

    protected List<Map<String, String>> addVerificationMethodOptions(List<Map<String, String>> options) {
        List<Map<String, String>> newOptions = Lists.newArrayList(options);
        newOptions.add(buildOption("付款冲应付", "PayOffsetAP"));//ignorei18n
        newOptions.add(buildOption("应付冲应付", "APOffsetAP"));//ignorei18n
        return newOptions;
    }

    protected List<Map<String, String>> addObjectReceivableOptions(List<Map<String, String>> options) {
        List<Map<String, String>> newOptions = Lists.newArrayList(options);
        newOptions.add(buildOption("出库单", "OutboundDeliveryNoteObj"));//ignorei18n
        return newOptions;
    }

    protected Map<String, String> buildOption(String label, String value) {
        Map<String, String> option = Maps.newHashMap();
        option.put("label", label);
        option.put("value", value);
        return option;
    }
}
