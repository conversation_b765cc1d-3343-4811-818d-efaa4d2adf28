package com.facishare.crm.fmcg.dms.service;

import com.fxiaoke.cloud.DataPersistor;

import java.util.HashMap;
import java.util.Map;


public class DMSBuryService {
    private static final String MODULE_V2 = "fs_crm_fmcg_service_tpm2_dms";
    private static final String SERVICE = "fs_crm_fmcg_service";

    private DMSBuryService() {

    }

    public static void asyncTpmLog(Integer enterpriseId, Integer employeeId, String subModule, String operation) {
        try {
            Map<String, Object> map = new HashMap<>();
            buildupSimpleLog(enterpriseId, employeeId, MODULE_V2, subModule, operation, null, map);
            DataPersistor.asyncLog(SERVICE, map);
        } catch (Exception ex) {
            //ignore
        }
    }

    private static void buildupSimpleLog(Integer enterpriseId, Integer employeeId, String module, String subModule, String operation, Double amount, Map<String, Object> data) {
        data.put("tenantId", enterpriseId);
        data.put("userId", employeeId);
        data.put("fullUserId", enterpriseId + "." + employeeId);
        data.put("module", module);
        data.put("subModule", subModule);
        data.put("operation", operation);
        if (amount != null) {
            data.put("amount", amount);
        }
        data.put("eventId", module + "_" + subModule + "_" + operation);
    }
}
