package com.facishare.crm.fmcg.dms.facade;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("delivery_note_accounts_period_validate")
public class DeliveryNoteAccountsPeriodPluginService extends PluginBaseService {

    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result addBefore(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        validateAccountPeriod(arg.getObjectData().toObjectData(), serviceContext, ApiNames.DELIVERY_NOTE_OBJ);

        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result editBefore(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        validateAccountPeriod(arg.getObjectData().toObjectData(), serviceContext, ApiNames.DELIVERY_NOTE_OBJ);

        return new EditActionDomainPlugin.Result();
    }

}
