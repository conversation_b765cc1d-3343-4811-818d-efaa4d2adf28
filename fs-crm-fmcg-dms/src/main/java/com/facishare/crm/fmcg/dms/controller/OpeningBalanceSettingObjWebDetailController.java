package com.facishare.crm.fmcg.dms.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@SuppressWarnings("all")
public class OpeningBalanceSettingObjWebDetailController extends StandardWebDetailController {


    @Override
    protected Result after(Arg arg, Result result) {

        buttonFilter(result);

        return super.after(arg, result);
    }


    private void buttonFilter(Result result) {
        List<String> removeButtonActionCode = Lists.newArrayList(ObjectAction.UPDATE.getActionCode(), ObjectAction.INVALID.getActionCode(), ObjectAction.BULK_INVALID.getActionCode());
        List components = (ArrayList) (result.getLayout().get("components"));

        for (Object component : components) {
            Map com = (Map) component;
            if ("head_info".equals(com.get("api_name"))) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    return removeButtonActionCode.contains(btn.get("action"));
                });
            }
        }
        if ("mobile".equals(arg.getLayoutAgentType())) {
            ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
            buttons.removeIf(button -> {
                Map btn = (Map) (button);
                return removeButtonActionCode.contains(btn.get("action"));
            });
        }
    }

}
