package com.facishare.crm.fmcg.dms.facade;

import com.facishare.crm.fmcg.common.apiname.AccountsReceivableNoteFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.RebateDetailFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("accounts_receivable_rel_rebate")
public class AccountsReceivableRelRebatePluginService extends PluginBaseService {


    @ServiceMethod("invalid_after")
    public InvalidActionDomainPlugin.Result invalidAfter(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        IObjectData data = arg.getObjectData().toObjectData();
        String objectReceivable = data.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, String.class);
        log.info("AccountsReceivablePluginService invalidAfter,dataId:{},objectReceivable:{}", data.getId(), objectReceivable);
        if (!ApiNames.REBATE_OBJ.equals(objectReceivable)) {
            return new InvalidActionDomainPlugin.Result();
        }
        disableRebateDetail(serviceContext, Lists.newArrayList(data));
        return new InvalidActionDomainPlugin.Result();
    }


    @ServiceMethod("bulk_invalid_after")
    public BulkInvalidActionDomainPlugin.Result bulkInvalidAfter(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        List<IObjectData> dataList = arg.getObjectDataList().stream().map(ObjectDataDocument::toObjectData)
                .filter(v -> ApiNames.REBATE_OBJ.equals(v.get(AccountsReceivableNoteFields.OBJECT_RECEIVABLE, String.class)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(dataList)) {
            return new BulkInvalidActionDomainPlugin.Result();
        }
        disableRebateDetail(serviceContext, dataList);
        return new BulkInvalidActionDomainPlugin.Result();
    }

    private void disableRebateDetail(ServiceContext serviceContext, List<IObjectData> accountsReceivables) {
        List<IObjectData> rebateDetails = rebateDetails(serviceContext.getUser(), accountsReceivables);

        rebateDetails.forEach(detail -> detail.set(RebateDetailFields.BIZ_STATUS, RebateDetailFields.BIZ_STATUS_NOT_EFFECTIVE));

        serviceFacade.batchUpdateByFields(serviceContext.getUser(), rebateDetails, Lists.newArrayList(RebateDetailFields.BIZ_STATUS));
    }

    private List<IObjectData> rebateDetails(User user, List<IObjectData> accountsReceivables) {


        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");


        IFilter idFilter = new Filter();
        idFilter.setFieldName(RebateDetailFields.OBJECT_DATA_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(accountsReceivables.stream().map(IObjectData::getId).collect(Collectors.toList()));

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(RebateDetailFields.OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ));

        query.setFilters(Lists.newArrayList(idFilter, apiNameFilter));

        return QueryDataUtil.find(
                serviceFacade,
                user.getTenantId(),
                ApiNames.REBATE_DETAIL_OBJ,
                query
        );
    }
}
