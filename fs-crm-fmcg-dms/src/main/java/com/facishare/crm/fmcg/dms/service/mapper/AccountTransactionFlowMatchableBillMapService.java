package com.facishare.crm.fmcg.dms.service.mapper;

import com.facishare.crm.fmcg.common.apiname.AccountTransactionFlowFields;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.MatchableBillMapService;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class AccountTransactionFlowMatchableBillMapService extends MatchableBillMapService {

    @Override
    protected void beforeMap(FinancialBill bill) {
        if (Objects.isNull(bill.getData())) {
            IObjectData data;
            try {
                data = serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.ACCOUNT_TRANSACTION_FLOW_OBJ);
            } catch (ObjectDataNotFoundException ex) {
                log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
                throw new AbandonActionException("data not found");
            } catch (MetaDataBusinessException ex) {
                if ("数据已作废或已删除".equals(ex.getMessage()) && ********* == ex.getErrorCode()) { //ignorei18n
                    log.info("data not found,tenantId:{},apiName:{},id:{}", bill.getTenantId(), bill.getApiName(), bill.getId());
                    throw new AbandonActionException("data not found");
                }
                log.error("find object error ", ex);
                throw ex;
            }
            bill.setData(data);
        }
    }

    @Override
    protected void validate(FinancialBill bill) {
        if (Strings.isNullOrEmpty(bill.getData().get(AccountTransactionFlowFields.CUSTOMER_ID, String.class))) {
            throw new AbandonActionException("account transaction flow bill customer id cat not be null or empty.");
        }
        if (Strings.isNullOrEmpty(bill.getData().get(AccountTransactionFlowFields.ORDER_ID, String.class))) {
            throw new AbandonActionException("account transaction flow bill order id cat not be null or empty.");
        }
    }

    @Override
    protected List<FinancialBill> mapToMatchableBills(FinancialBill bill) {
        String orderId = bill.getData().get(AccountTransactionFlowFields.ORDER_ID, String.class);
        return convertToAccountsReceivableNote(bill, findReceivablesBySalesOrderId(bill.getTenantId(), orderId));
    }
}