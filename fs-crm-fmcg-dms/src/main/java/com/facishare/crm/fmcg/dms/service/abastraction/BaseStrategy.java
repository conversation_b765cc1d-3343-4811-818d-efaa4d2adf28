package com.facishare.crm.fmcg.dms.service.abastraction;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.service.converter.OutboundDeliveryNoteAutoReceivableConvertToReceivableService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class BaseStrategy {

    @Resource
    protected ServiceFacade serviceFacade;


    protected List<IObjectData> queryOutboundDeliveryNoteDetails(String tenantId, String id) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(OutboundDeliveryNoteProductFields.OUTBOUND_DELIVERY_NOTE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.OUTBOUND_DELIVERY_NOTE_PRODUCT,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        OutboundDeliveryNoteProductFields.OUTBOUND_AMOUNT,
                        OutboundDeliveryNoteProductFields.OUTBOUND_DELIVERY_NOTE_ID,
                        OutboundDeliveryNoteProductFields.PRODUCT_ID
                )
        );
    }

    public Long calculateDueDate(String tenantId, Long date, String accountId, long now) {

        IObjectData account = serviceFacade.findObjectData(User.systemUser(tenantId), accountId, ApiNames.ACCOUNT_OBJ);
        Integer deadLineDay = account.get(AccountFields.DEADLINE_DAY, Integer.class);
        if (deadLineDay == null) {
            deadLineDay = 0;
        }
        String accountingPeriodType = account.get(AccountFields.ACCOUNTING_PERIOD_TYPE, String.class);
        Integer fixedAccountingPeriodDate = account.get(AccountFields.FIXED_PAYMENT_DATE, Integer.class);

        if (StringUtils.isEmpty(accountingPeriodType)) {
            return date;
        }
        switch (accountingPeriodType) {
            case AccountFields.ACCOUNTING_PERIOD_TYPE__CASH: {
                return date;
            }
            case AccountFields.ACCOUNTING_PERIOD_TYPE__MONTHLY: {

                return calculateDueDateWithMonthly(date, deadLineDay, fixedAccountingPeriodDate);
            }
            case AccountFields.ACCOUNTING_PERIOD_TYPE__DEADLINE: {
                return calculateDueDateWithDeadline(date, deadLineDay);
            }
            default:
                return now;
        }

    }

    /**
     * 若发货日期小于等于固定账期日，则到期日期=本月固定账期日+限期天数，若本月无固定账期日（例如3月无31日，而固定账期日是31），则向前提前一天计算到期日期。
     * 若发货日期大于固定账期日，则到期日期=下月固定账期日+限期天数，若下月无固定账期日（例如3月无31日，而固定账期日是31），则向前提前一天计算到期日期。
     */
    private Long calculateDueDateWithMonthly(Long deliveryDateTimestamp, Integer deadLineDay, Integer fixedAccountingPeriodDate) {
        if (Objects.isNull(fixedAccountingPeriodDate)) {
            return deliveryDateTimestamp;
        }
        int deliveryNoteDay = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayByTimestamp(deliveryDateTimestamp);

        int endDayOfMonth = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getEndDayOfMonthByTimestamp(deliveryDateTimestamp);


        if (deliveryNoteDay <= fixedAccountingPeriodDate) {

            long currentMonthFixedAccountingPeriodDateTime = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getTimestampByDayOfMonth(deliveryDateTimestamp, endDayOfMonth < fixedAccountingPeriodDate ? endDayOfMonth : fixedAccountingPeriodDate);

            return OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayBeginTimestamp(currentMonthFixedAccountingPeriodDateTime + (deadLineDay * OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.ONE_DAY));
        } else {
            long nextMonthFixedAccountingPeriodDateTime = OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getTimestampByDayOfNextMonth(deliveryDateTimestamp, endDayOfMonth < fixedAccountingPeriodDate ? endDayOfMonth : fixedAccountingPeriodDate);

            return OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayBeginTimestamp(nextMonthFixedAccountingPeriodDateTime + (deadLineDay * OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.ONE_DAY));
        }


    }

    /**
     * 发货日期+限期天数
     */
    private Long calculateDueDateWithDeadline(Long deliveryDate, Integer deadLineDay) {

        return OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.getDayBeginTimestamp(deliveryDate + (deadLineDay * OutboundDeliveryNoteAutoReceivableConvertToReceivableService.DateUtil.ONE_DAY));

    }


    protected boolean existsReceivableByUniqueId(String tenantId, String receivableObjectId, String receivableObjectApiName) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter idFilter = new Filter();
        idFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(receivableObjectId));

        IFilter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(receivableObjectApiName));

        query.setFilters(Lists.newArrayList(idFilter, apiNameFilter));

        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return CollectionUtils.isNotEmpty(result);
    }

    @Deprecated
    protected boolean existsReceivableByUniqueIdV1(String tenantId, String fieldName, String fieldValue) {

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(fieldValue));

        query.setFilters(Lists.newArrayList(filter));

        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ,
                query,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return CollectionUtils.isNotEmpty(result);
    }
}
