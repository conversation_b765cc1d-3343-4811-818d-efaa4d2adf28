package com.facishare.crm.fmcg.dms.service.mapper;

import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.MatchableBillMapService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PayMatchableBillMapService extends MatchableBillMapService {

    @Override
    protected void beforeMap(FinancialBill bill) {
        if (Objects.isNull(bill.getData())) {
            bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.PAY_OBJ));
        }
        if (CollectionUtils.isEmpty(bill.getDetails())) {
            bill.setDetails(queryPayDetails(bill.getTenantId(), bill.getData().getId()));
        }
    }

    @Override
    protected void validate(FinancialBill bill) {
        if (Objects.equals(Boolean.TRUE, bill.getData().get(PayFields.OPENING_BALANCE, Boolean.class))) {
            throw new AbandonActionException("opening balance pay abandon.");
        }
    }

    @Override
    protected List<FinancialBill> mapToMatchableBills(FinancialBill bill) {
        //查询所有应付单明细：
        //通过订单查询所有入库单、通过采购退货单号查询所有出库单
        List<String> purchaseOrderIds = bill.getDetails().stream().filter(v -> v.get(PayDetailFields.PURCHASE_ORDER_ID, String.class) != null)
                .map(v -> v.get(PayDetailFields.PURCHASE_ORDER_ID, String.class)).collect(Collectors.toList());
        List<String> purchaseReturnOrderIds = bill.getDetails().stream().filter(v -> v.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class) != null)
                .map(v -> v.get(PayDetailFields.PURCHASE_RETURN_NOTE_ID, String.class)).collect(Collectors.toList());

        Set<String> payableIds = Sets.newHashSet();
        if (!CollectionUtils.isEmpty(purchaseOrderIds)) {
            List<String> goodsReceivedNoteIds = queryGoodsReceivedIdsByPurchaseOrderIds(bill.getTenantId(), purchaseOrderIds);
            List<IObjectData> payableDetails = queryPayableDetails(bill.getTenantId(), AccountsPayableDetailFields.GOODS_RECEIVED_NOTE_ID, goodsReceivedNoteIds);
            payableIds.addAll(payableDetails.stream().map(v -> v.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(purchaseReturnOrderIds)) {
            List<String> outboundDeliveryIds = queryOutboundDeliveryIdsByPurchaseReturnOrderIds(bill.getTenantId(), purchaseReturnOrderIds);
            List<IObjectData> payableDetails = queryPayableDetails(bill.getTenantId(), AccountsPayableDetailFields.OUTBOUND_DELIVERY_NOTE_ID, outboundDeliveryIds);
            payableIds.addAll(payableDetails.stream().map(v -> v.get(AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID, String.class)).collect(Collectors.toList()));
        }


        return convertToAccountsPayableNote(bill, Lists.newArrayList(payableIds));
    }

    private List<String> queryOutboundDeliveryIdsByPurchaseReturnOrderIds(String tenantId, List<String> purchaseReturnOrderIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(OutboundDeliveryNoteFields.PURCHASE_RETURN_NOTE_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(purchaseReturnOrderIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);
        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return result.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<String> queryGoodsReceivedIdsByPurchaseOrderIds(String tenantId, List<String> purchaseOrderIds) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(GoodsReceivedNoteFields.PURCHASE_ORDER_ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(purchaseOrderIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);
        List<IObjectData> result = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.GOODS_RECEIVED_NOTE_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID
                )
        );
        return result.stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private List<IObjectData> queryPayableDetails(String tenantId, String fieldApiName, List<String> values) {
        if (CollectionUtils.isEmpty(values)) {
            return Lists.newArrayList();
        }
        IFilter idFilter = new Filter();
        idFilter.setFieldName(fieldApiName);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(values);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ACCOUNTS_PAYABLE_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        AccountsPayableDetailFields.ACCOUNTS_PAYABLE_NOTE_ID
                )
        );
    }

    private List<IObjectData> queryPayDetails(String tenantId, String payId) {
        IFilter idFilter = new Filter();
        idFilter.setFieldName(PayFields.PAY_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(payId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAY_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        PayDetailFields.PAY_AMOUNT,
                        PayDetailFields.PURCHASE_ORDER_ID,
                        PayDetailFields.PURCHASE_RETURN_NOTE_ID,
                        PayDetailFields.PAY_DATE,
                        PayDetailFields.SUPPLIER_ID
                )
        );
    }
}