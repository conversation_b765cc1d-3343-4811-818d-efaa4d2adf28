package com.facishare.crm.fmcg.dms.service.converter;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.errors.AbandonActionException;
import com.facishare.crm.fmcg.dms.model.EnterpriseFundAccountInfoDTO;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.model.PayableConvertResult;
import com.facishare.crm.fmcg.dms.service.abastraction.AutoPayableConvertService;
import com.facishare.crm.fmcg.dms.util.CastUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PurchaseOrderAutoPayableConvertToPayService extends AutoPayableConvertService {


    @Override
    protected void validate(FinancialBill bill) {
        idempotent(bill);
        String accountInfo = bill.getData().get(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT, String.class);
        if (StringUtils.isEmpty(accountInfo)) {
            throw new AbandonActionException("purchase order accountInfo is null.");
        }
        if (FinancialBill.ACTION_ENTERPRISE_FUND_ACCOUNT_INFO_UPDATE.equals(bill.getAction())) {
            String lifeStatus = bill.getData().get(CommonFields.LIFE_STATUS, String.class);
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
                throw new AbandonActionException("purchase life status in not normal.");
            }
        }
    }

    @Override
    protected PayableConvertResult convertData(FinancialBill bill) {
        List<PayableConvertResult.Payable> result = Lists.newArrayList();
        List<EnterpriseFundAccountInfoDTO> enterpriseFundAccountInfo = CastUtil.castToEnterpriseFundAccountInfoDTO(bill.getData().get(PurchaseOrderObjFields.ENTERPRISE_FUND_ACCOUNT, String.class));
        for (EnterpriseFundAccountInfoDTO enterpriseFundAccountInfoDTO : enterpriseFundAccountInfo) {
            result.add(PayableConvertResult.Payable.builder().data(convertToMaster(bill, enterpriseFundAccountInfoDTO)).details(convertToDetails(bill, enterpriseFundAccountInfoDTO)).build());
        }
        return PayableConvertResult.builder().result(result).build();
    }

    @Override
    protected void beforeConvert(FinancialBill bill) {
        bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.PURCHASE_ORDER_OBJ));

        bulkInvalid(bill);
    }

    protected IObjectData convertToMaster(FinancialBill bill, EnterpriseFundAccountInfoDTO enterpriseFundAccountInfoDTO) {
        IObjectData data = new ObjectData();
        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.PAY_OBJ);
        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

        data.set(PayFields.PAY_TYPE, PayFields.PAY_TYPE__BLUE);
        data.set(PayFields.PAY_DATE, System.currentTimeMillis());
        data.set(PayFields.SUPPLIER_ID, bill.getData().get(PurchaseOrderObjFields.SUPPLIER_ID));


        data.set(PayFields.PURPOSE, PayFields.PURPOSE__PURCHASE);
        data.set(PayFields.BASE, false);
        data.set(PayFields.CONTACT_OBJECT, PayFields.CONTACT_OBJECT__SUPPLIER);

        data.set(PayFields.PAY_AMOUNT, new BigDecimal(enterpriseFundAccountInfoDTO.getAmount()));
        data.set(PayFields.ENTERPRISE_FUND_ACCOUNT_ID, enterpriseFundAccountInfoDTO.getAccountId());
        return data;
    }


    protected List<IObjectData> convertToDetails(FinancialBill bill, EnterpriseFundAccountInfoDTO enterpriseFundAccountInfoDTO) {

        IObjectData data = new ObjectData();
        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.PAY_DETAIL_OBJ);

        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, CommonFields.RECORD_TYPE__DEFAULT);
        data.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);


        data.set(PayDetailFields.PURCHASE_ORDER_ID, bill.getData().getId());
        data.set(PayDetailFields.SUPPLIER_ID, bill.getData().get(PurchaseOrderObjFields.SUPPLIER_ID));


        data.set(PayDetailFields.PAY_AMOUNT, new BigDecimal(enterpriseFundAccountInfoDTO.getAmount()));
        return Lists.newArrayList(data);
    }

    private void idempotent(FinancialBill bill) {
        //采购订单id
        String purchaseOrderId = bill.getData().getId();
        boolean b = existsPayableByUniqueId(bill.getTenantId(), PayDetailFields.PURCHASE_ORDER_ID, purchaseOrderId, ApiNames.PAY_DETAIL_OBJ);
        if (b) {
            throw new AbandonActionException(String.format("purchaseOrderId:[%s] is already create AccountsPayable.", purchaseOrderId));
        }
    }

    private void bulkInvalid(FinancialBill bill) {
        if (FinancialBill.ACTION_ENTERPRISE_FUND_ACCOUNT_INFO_UPDATE.equals(bill.getAction())) {
            //查询付款明细
            List<IObjectData> payDetails = getPayDetailsByPurchaseOrderId(bill.getTenantId(), bill.getData().getId());
            //查询付款
            List<String> payIds = payDetails.stream().map(obj -> obj.get(PayDetailFields.PAY_ID, String.class)).collect(Collectors.toList());
            List<IObjectData> pays = serviceFacade.findObjectDataByIdsIgnoreAll(bill.getTenantId(), payIds, ApiNames.PAY_OBJ);

            //作废付款及付款明细
            serviceFacade.bulkInvalid(payDetails, User.systemUser(bill.getTenantId()));
            serviceFacade.bulkInvalid(pays, User.systemUser(bill.getTenantId()));
        }
    }

    protected List<IObjectData> getPayDetailsByPurchaseOrderId(String tenantId, String purchaseOrderId) {


        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(-1);
        query.setNeedReturnCountNum(false);
        query.setNeedReturnQuote(false);
        query.setSearchSource("db");

        IFilter filter = new Filter();
        filter.setFieldName(PayDetailFields.PURCHASE_ORDER_ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(purchaseOrderId));

        query.setFilters(Lists.newArrayList(filter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PAY_DETAIL_OBJ,
                query
        );
    }
}
