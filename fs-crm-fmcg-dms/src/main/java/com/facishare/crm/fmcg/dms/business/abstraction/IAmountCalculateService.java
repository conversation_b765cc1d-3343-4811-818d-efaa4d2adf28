package com.facishare.crm.fmcg.dms.business.abstraction;

import java.math.BigDecimal;

public interface IAmountCalculateService {
    BigDecimal calculateEnterpriseFundAccountObjPaymentTotalAmount(String tenantId, String accountId);

    BigDecimal calculateEnterpriseFundAccountObjPayTotalAmount(String tenantId, String accountId);
    BigDecimal calculateObjPayTotalAmount(String tenantId,String fieldName, String fieldValue);
    BigDecimal calculateObjPayMatchTotalAmount(String tenantId,String apiName,  String fieldValue);
    BigDecimal calculateTotalMoneyWithPurchaseOrderDetails(String tenantId, String purchaseOrderId);
    BigDecimal calculateTotalMoneyWithPurchaseReturnDetails(String tenantId, String returnNoteId);
    BigDecimal calculateTotalARAmountWithSourceDetailDataId(String tenantId, String sourceDetailDataId);
}
