@startuml 典型业务流程时序图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60

title TPM系统典型业务流程 - 奖励规则处理时序图

actor "客户端" as Client : 前端应用或移动端\n发起业务请求
participant "RewardController" as Controller : 奖励控制器\n处理HTTP请求\n参数校验和路由
participant "IActivityRewardRuleService" as Service : 活动奖励规则服务\n业务逻辑处理\n事务管理
participant "IRewardRuleManager" as Manager : 奖励规则管理器\n规则校验和管理\n业务规则引擎
participant "ActivityService" as Business : 活动业务服务\n活动状态管理\n业务规则执行
participant "ActivityRewardRuleDAO" as DAO : 活动奖励规则DAO\n数据持久化\nMongoDB操作封装
participant "MongoDB" as DB : MongoDB数据库\n主要数据存储\n支持事务和索引
participant "FmcgServiceProxy" as Proxy : FMCG服务代理\n外部服务调用\nHTTP客户端封装
participant "外部服务" as External : 第三方服务\n微信、支付宝等\nRESTful API

== 添加奖励规则流程 ==

note over Client, External
  <b>业务场景:</b> 管理员在后台管理系统中创建新的活动奖励规则
  包括设置奖励类型、金额、触发条件等
end note

Client -> Controller: POST /reward/add
note right: 提交奖励规则创建请求\n包含规则参数和配置
activate Controller

Controller -> Controller: 设置租户ID
note right: 从请求上下文中获取\n租户信息进行数据隔离
Controller -> Service: add(arg)
note right: 调用业务服务层\n处理规则创建逻辑
activate Service

Service -> Manager: validateRule(arg)
note right: 校验奖励规则的合法性\n包括参数格式和业务规则
activate Manager
Manager -> Business: checkActivityStatus(activityId)
note right: 检查关联活动的状态\n确保活动处于可编辑状态
activate Business
Business -> DAO: getActivityById(activityId)
note right: 查询活动的详细信息\n包括活动状态和配置
activate DAO
DAO -> DB: query activity
note right: 执行MongoDB查询\n使用索引提高性能
DB --> DAO: activity data
deactivate DAO
Business --> Manager: activity status
note left: 返回活动状态信息\n用于后续校验
deactivate Business
Manager --> Service: validation result
note left: 返回校验结果\n包含错误信息和建议
deactivate Manager

Service -> DAO: save(rewardRule)
note right: 保存奖励规则到数据库\n使用事务确保数据一致性
activate DAO
DAO -> DB: insert reward rule
note right: 执行插入操作\n生成唯一ID和时间戳
DB --> DAO: saved id
deactivate DAO

Service -> Proxy: notifyExternalSystem(ruleInfo)
note right: 通知外部系统\n同步规则信息
activate Proxy
Proxy -> External: HTTP request
note right: 发送HTTP请求\n包含规则数据和签名
External --> Proxy: response
note left: 返回处理结果\n确认同步成功
deactivate Proxy

Service --> Controller: AddActivityRewardRule.Result
note left: 返回创建结果\n包含规则ID和状态
deactivate Service
Controller --> Client: JSON response
note left: 返回HTTP响应\n包含成功标识和数据
deactivate Controller

== 扫码奖励流程 ==

note over Client, External
  <b>业务场景:</b> 消费者在微信小程序中扫描产品二维码
  系统根据奖励规则自动计算并发放奖励
end note

Client -> Controller: POST /reward/scanCode
note right: 提交扫码奖励请求\n包含二维码和用户信息
activate Controller

Controller -> Service: scanCodeReward(arg)
note right: 调用扫码奖励服务\n处理奖励发放逻辑
activate Service

Service -> Manager: validateScanCode(code)
note right: 校验二维码的有效性\n检查码的格式和状态
activate Manager
Manager -> Business: checkCodeValidity(code)
note right: 检查码的业务有效性\n包括过期和使用次数
activate Business
Business -> DAO: getRewardRuleByCode(code)
note right: 根据二维码查询\n对应的奖励规则
activate DAO
DAO -> DB: query reward rule
note right: 查询奖励规则配置\n包括奖励类型和金额
DB --> DAO: rule data
deactivate DAO
Business --> Manager: code validation
note left: 返回码校验结果\n包含规则信息
deactivate Business
Manager --> Service: validation result
note left: 返回最终校验结果\n确定是否可以发放奖励
deactivate Manager

Service -> Business: calculateReward(rule, user)
note right: 根据规则计算奖励\n考虑用户等级和历史
activate Business
Business -> Proxy: getUserInfo(userId)
note right: 获取用户详细信息\n包括等级和积分
activate Proxy
Proxy -> External: get user data
note right: 调用用户系统 API\n获取用户档案
External --> Proxy: user info
note left: 返回用户信息\n用于奖励计算
deactivate Proxy
Business --> Service: reward amount
note left: 返回计算后的奖励\n包括金额和类型
deactivate Business

Service -> DAO: saveRewardRecord(record)
note right: 保存奖励发放记录\n用于审计和统计
activate DAO
DAO -> DB: insert reward record
note right: 插入奖励记录\n包含用户和奖励信息
DB --> DAO: record id
deactivate DAO

Service -> Proxy: transferReward(amount, user)
note right: 执行奖励转账\n调用支付系统
activate Proxy
Proxy -> External: transfer money
note right: 调用第三方支付\n如微信红包或支付宝
External --> Proxy: transfer result
note left: 返回转账结果\n包含成功标识和流水号
deactivate Proxy

Service --> Controller: ScanCodeReward.Result
note left: 返回奖励结果\n包含奖励信息和状态
deactivate Service
Controller --> Client: reward result
note left: 返回给用户\n显示奖励成功信息
deactivate Controller

== 预算消费流程 ==

note over Client, External
  <b>业务场景:</b> 活动执行过程中需要消费预算资金
  系统需要确保预算充足并记录消费明细
end note

Client -> Controller: POST /budget/consume
note right: 提交预算消费请求\n包含预算ID和消费金额
activate Controller

Controller -> Service: consumeBudget(arg)
note right: 调用预算消费服务\n处理预算扣减逻辑
activate Service

Service -> Manager: validateBudgetConsume(arg)
note right: 校验预算消费请求\n检查参数和权限
activate Manager
Manager -> Business: checkBudgetAvailable(budgetId)
note right: 检查预算可用性\n确保余额充足
activate Business
Business -> DAO: getBudgetById(budgetId)
note right: 查询预算详细信息\n包括余额和状态
activate DAO
DAO -> DB: query budget
note right: 执行预算查询\n获取最新数据
DB --> DAO: budget data
deactivate DAO
Business --> Manager: budget status
note left: 返回预算状态\n包含可用余额
deactivate Business
Manager --> Service: validation result
note left: 返回校验结果\n确认可以执行消费
deactivate Manager

Service -> Business: lockBudget(budgetId)
note right: 获取预算分布式锁\n防止并发修改冲突
activate Business
note right: 使用Redis分布式锁\n设置超时时间防止死锁
Business --> Service: lock acquired
note left: 锁获取成功\n可以安全修改预算
deactivate Business

Service -> DAO: updateBudgetAmount(budgetId, amount)
note right: 更新预算余额\n执行原子性操作
activate DAO
DAO -> DB: update budget
note right: 执行数据库更新\n使用事务确保一致性
DB --> DAO: update result
deactivate DAO

Service -> DAO: saveBudgetDetail(detail)
note right: 保存预算消费明细\n记录消费轨迹
activate DAO
DAO -> DB: insert budget detail
note right: 插入消费记录\n包含时间和操作人
DB --> DAO: detail id
deactivate DAO

Service -> Business: unlockBudget(budgetId)
note right: 释放预算分布式锁\n允许其他操作继续
activate Business
Business --> Service: lock released
note left: 锁释放成功\n事务完成
deactivate Business

Service --> Controller: ConsumeBudget.Result
note left: 返回消费结果\n包含新余额和记录ID
deactivate Service
Controller --> Client: consume result
note left: 返回消费成功信息\n显示操作结果
deactivate Controller

note over Client, External
  <b>流程总结:</b>
  1. 所有操作都在事务中执行，确保数据一致性
  2. 使用分布式锁防止并发修改冲突
  3. 详细记录所有操作日志，便于审计和问题排查
end note

@enduml
