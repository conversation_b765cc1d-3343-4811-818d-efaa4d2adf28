{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "DisplayNameToJson",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.common.file.DisplayNameToJson",
            "projectName": "fs-crm-fmcg-common"
        },
        {
            "type": "java",
            "name": "MNTPMDataTotal",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.common.file.MNTPMDataTotal",
            "projectName": "fs-crm-fmcg-common"
        },
        {
            "type": "java",
            "name": "ValidateExceptionFinder",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.common.file.ValidateExceptionFinder",
            "projectName": "fs-crm-fmcg-common"
        },
        {
            "type": "java",
            "name": "QRCodeUtil",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.common.utils.QRCodeUtil",
            "projectName": "fs-crm-fmcg-common"
        },
        {
            "type": "java",
            "name": "AESUtil",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.fesco.utils.AESUtil",
            "projectName": "fs-crm-fmcg-fesco"
        },
        {
            "type": "java",
            "name": "CommonTest",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.tpm.CommonTest",
            "projectName": "fs-crm-fmcg-service"
        },
        {
            "type": "java",
            "name": "FormatUtil",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.tpm.utils.FormatUtil",
            "projectName": "fs-crm-fmcg-tpm"
        },
        {
            "type": "java",
            "name": "MengNiuCalculateUtils",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.tpm.utils.MengNiuCalculateUtils",
            "projectName": "fs-crm-fmcg-tpm"
        },
        {
            "type": "java",
            "name": "LocalAuthenticationCmd",
            "request": "launch",
            "mainClass": "com.facishare.crm.fmcg.tpm.web.service.LocalAuthenticationCmd",
            "projectName": "fs-crm-fmcg-tpm"
        }
    ]
}