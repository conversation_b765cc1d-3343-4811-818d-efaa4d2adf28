package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.SignInGoodsActivitySelector;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class SignInGoodsEventHandler extends CommonSalesEventHandler<SignInGoodsEventData> {

    @Resource
    private SignInGoodsActivitySelector signInGoodsActivitySelector;

    @Override
    protected String buildEventIdentityKey(SalesEvent<SignInGoodsEventData> event) {
        return String.format("%s.%s.%s", event.getTenantId(), event.getEventType(), event.getData().getDeliveryNoteId());
    }

    @Override
    protected List<RedPacketReward> calculateRewards(SalesEvent<SignInGoodsEventData> event) {
        MengNiuTenantInformation tenant = tenantHierarchyService.load(event.getTenantId());
        if (MengNiuTenantInformation.ROLE_OTHERS.equals(tenant.getRole())) {
            return Lists.newArrayList();
        }

        FullSignInGoodsEventInformation fullEvent = loadFullEventInformation(tenant, event);

        RedPacketReward storeOwnerReward = calculateStoreOwnerReward(tenant, event, fullEvent);
        RedPacketReward storeWarehouseOwnerReward = calculateStoreWarehouseOwnerReward(tenant, event, fullEvent);
        RedPacketReward mBossReward = calculateMBossReward(tenant, event, fullEvent);
        RedPacketReward salesmenReward = calculateSalesmenReward(tenant, event, fullEvent);

        List<RedPacketReward> rewards = Lists.newArrayList(mBossReward, storeWarehouseOwnerReward, storeOwnerReward, salesmenReward);
        validateActivityAmount(fullEvent.getActivity(), rewards);

        return rewards;
    }

    private RedPacketReward calculateStoreWarehouseOwnerReward(MengNiuTenantInformation tenant, SalesEvent<SignInGoodsEventData> event, FullSignInGoodsEventInformation fullEvent) {
        try {
            List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_SW_OWNER);
            PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_SW_OWNER);
            PaymentAccount to = loadWeChatPaymentAccountFromStoreWarehouse(tenant, fullEvent.getDeliveryNote().getTenantId(), fullEvent.getDeliveryNote().get("account_id", String.class));

            String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SW_OWNER.toLowerCase());
            String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
            // 取活动上的有效天数
            Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_SW_OWNER.toLowerCase());

            return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_SW_OWNER, from, to, details, publishMode, expirationDate);
        } catch (Exception ex) {
            log.error("store owner calculate error : ", ex);
            return null;
        }
    }


    private Long getExpirationDate(FullSignInGoodsEventInformation fullEvent, String publishMode, String role) {
        Long expirationDate = null;
        if (TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(fullEvent.getActivity().getTenantId())){
            try{
                String effectiveDaysKey = String.format("%s_effective_days__c", role);
                Integer effectiveDay = fullEvent.getActivity().get(effectiveDaysKey, Integer.class);
                expirationDate = RedPacketReward.PUBLISH_MODE_AUTO.equals(publishMode) ? null : getExpirationDate(effectiveDay);
            } catch (Exception exception){
                log.error("get expiration date error : ", exception);
            }
        }
        return expirationDate;
    }

    private RewardedData calculateRewardedData(MengNiuTenantInformation tenant, FullSignInGoodsEventInformation fullEvent, String role) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getActivity().getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordFields.RELATED_STORE_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStore().getId()));

        IFilter dataIdFilter = new Filter();
        dataIdFilter.setFieldName(RedPacketRecordFields.EVENT_OBJECT_DATA_ID);
        dataIdFilter.setOperator(Operator.NEQ);
        dataIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getSalesOrder().getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName(RedPacketRecordFields.ROLE);
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
          Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, dataIdFilter),
                Lists.newArrayList(order)
        );

        List<IObjectData> records = QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "red_packet_record__c",
                stq,
                Lists.newArrayList(
                        "_id"
                ));


        RewardedData data = new RewardedData();
        data.setTotalRecordCount(records.size());

        List<String> recordIds = records.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> recordDetails = queryRecordDetails(tenant, recordIds);

        data.setTotalCount(recordDetails.size());
        data.setSkuCount(Maps.newHashMap());

        for (IObjectData recordDetail : recordDetails) {
            String productId = recordDetail.get("product_id__c", String.class);
            if (!Strings.isNullOrEmpty(productId)) {
                if (data.getSkuCount().containsKey(productId)) {
                    data.getSkuCount().put(productId, data.getSkuCount().get(productId) + 1);
                } else {
                    data.getSkuCount().put(productId, 1);
                }
            }
        }

        return data;
    }

    private List<IObjectData> queryRecordDetails(MengNiuTenantInformation tenant, List<String> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return Lists.newArrayList();
        }

        IFilter masterRecordIdFilter = new Filter();
        masterRecordIdFilter.setFieldName("red_packet_record_id__c");
        masterRecordIdFilter.setOperator(Operator.IN);
        masterRecordIdFilter.setFieldValues(recordIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterRecordIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "red_packet_record_detail__c",
                stq,
                Lists.newArrayList(
                        "_id", "product_id__c"
                ));
    }

    private RewardLimitConfig loadRewardLimitConfig(MengNiuTenantInformation tenant, IObjectData activity, String channelId, String storeId) {
        IObjectData storeConfig = loadRewardLimitConfigByStore(tenant, activity.getId(), storeId);
        if (Objects.nonNull(storeConfig)) {
            return convertToRewardLimitConfig(tenant, activity, storeConfig);
        }

        IObjectData channelConfig = loadRewardLimitConfigByChannel(tenant, activity.getId(), channelId);
        if (Objects.nonNull(channelConfig)) {
            return convertToRewardLimitConfig(tenant, activity, channelConfig);
        }

        IObjectData activityConfig = loadRewardLimitConfigByActivity(tenant, activity.getId());
        if (Objects.nonNull(activityConfig)) {
            return convertToRewardLimitConfig(tenant, activity, activityConfig);
        }
        return convertToRewardLimitConfig(activity);
    }

    private RewardLimitConfig convertToRewardLimitConfig(IObjectData activity) {
        RewardLimitConfig config = new RewardLimitConfig();
        config.setLimitMode(activity.get("reward_limit_mode__c", String.class, RewardLimitConfig.MULTIPLE_ORDER));
        config.setTotalLimit(-1);
        config.setSkuLimit(Maps.newHashMap());
        return config;
    }

    private RewardLimitConfig convertToRewardLimitConfig(MengNiuTenantInformation tenant, IObjectData activity, IObjectData activityConfig) {
        RewardLimitConfig config = new RewardLimitConfig();

        config.setLimitMode(activity.get("reward_limit_mode__c", String.class, RewardLimitConfig.MULTIPLE_ORDER));
        config.setTotalLimit(activityConfig.get("total_limit__c", Integer.class, -1));
        config.setSkuLimit(Maps.newHashMap());

        List<IObjectData> skuLimitList = querySkuLimit(tenant, activityConfig.getId());

        for (IObjectData skuLimit : skuLimitList) {
            String skuId = skuLimit.get("sku_id__c", String.class);
            Integer limit = skuLimit.get("limit__c", Integer.class);

            if (!Strings.isNullOrEmpty(skuId) && !Objects.isNull(limit) && limit > -1) {
                config.getSkuLimit().put(skuId, limit);
            }
        }

        return config;
    }

    private List<IObjectData> querySkuLimit(MengNiuTenantInformation tenant, String masterConfigId) {
        IFilter masterIdFilter = new Filter();
        masterIdFilter.setFieldName("store_reward_limit_id__c");
        masterIdFilter.setOperator(Operator.EQ);
        masterIdFilter.setFieldValues(Lists.newArrayList(masterConfigId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_sku_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "sku_id__c", "limit__c", "store_reward_limit_id__c"
                ));
    }

    private IObjectData loadRewardLimitConfigByActivity(MengNiuTenantInformation tenant, String activityId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("store_id__c");
        storeIdFilter.setOperator(Operator.IS);
        storeIdFilter.setFieldValues(Lists.newArrayList());

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, storeIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "activity_id__c", "store_id__c", "total_limit__c"
                )).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByChannel(MengNiuTenantInformation tenant, String activityId, String channelId) {
        if (Strings.isNullOrEmpty(channelId)) {
            return null;
        }

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("channel_id__c");
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(channelId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(activityIdFilter, storeIdFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenant.getManufacturer().getTenantId(), "store_reward_limit__c", stq, Lists.newArrayList("_id", "activity_id__c", "store_id__c", "channel_id__c", "total_limit__c")).stream().findFirst().orElse(null);
    }

    private IObjectData loadRewardLimitConfigByStore(MengNiuTenantInformation tenant, String activityId, String storeId) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id__c");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityId));

        IFilter storeIdFilter = new Filter();
        storeIdFilter.setFieldName("store_id__c");
        storeIdFilter.setOperator(Operator.EQ);
        storeIdFilter.setFieldValues(Lists.newArrayList(storeId));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, storeIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                "store_reward_limit__c",
                stq,
                Lists.newArrayList(
                        "_id", "activity_id__c", "store_id__c", "total_limit__c"
                )).stream().findFirst().orElse(null);
    }

    private void validateActivityAmount(IObjectData activity, List<RedPacketReward> rewards) {
        Boolean allowOverLimitReward = activity.get("allow_over_limit_reward__c", Boolean.class);
        BigDecimal budget = activity.get(TPMActivityFields.ACTIVITY_AMOUNT, BigDecimal.class);

        BigDecimal incomingRewardAmount = calculateIncomingRewardAmount(rewards);
        BigDecimal rewardedAmount = calculateRewardedAmount(activity);

        log.info("after amount information : {} - {} - {}", budget, rewardedAmount, incomingRewardAmount);

        if (budget.compareTo(rewardedAmount.add(incomingRewardAmount)) <= 0) {
            if (Boolean.TRUE.equals(allowOverLimitReward)) {
                for (RedPacketReward reward : rewards) {
                    if (Objects.nonNull(reward)){
                        reward.setOverLimit(true);
                    }
                }
            } else {
                throw new EventAbandonException("insufficient balance.");
            }
        }
    }

    private FullSignInGoodsEventInformation loadFullEventInformation(MengNiuTenantInformation tenant, SalesEvent<SignInGoodsEventData> event) {
        IObjectData deliveryNote = serviceFacade.findObjectDataIgnoreAll(User.systemUser(event.getTenantId()), event.getData().getDeliveryNoteId(), ApiNames.DELIVERY_NOTE_OBJ);
        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(deliveryNote.getTenantId()), deliveryNote.get("account_id", String.class), ApiNames.ACCOUNT_OBJ);
        IObjectData salesOrder = serviceFacade.findObjectDataIgnoreAll(User.systemUser(event.getTenantId()), deliveryNote.get("sales_order_id", String.class), ApiNames.SALES_ORDER_OBJ);
        IObjectData activity = signInGoodsActivitySelector.invoke(SignInGoodsActivitySelector.Arg.builder().tenant(tenant).storeId(store.getId()).build());

        if (Objects.isNull(activity)) {
            throw new EventAbandonException("activity not found.");
        }

        log.info("activity : {}", activity);
        List<IObjectData> deliveryNoteDetails = queryDeliveryNoteDetails(event.getTenantId(), event.getData().getDeliveryNoteId());

        List<String> salesOrderDetailIds = deliveryNoteDetails.stream().map(m -> m.get("sales_order_product_id", String.class)).distinct().filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, IObjectData> salesOrderDetailMap = loadSalesOrderDetailMap(event.getTenantId(), salesOrderDetailIds);

        List<String> allSerialNumberIds = Lists.newArrayList();
        for (IObjectData deliveryNoteDetail : deliveryNoteDetails) {
            @SuppressWarnings("unchecked")
            List<String> serialNumberIds = deliveryNoteDetail.get("unique_product_code_combination", List.class);
            if (!CollectionUtils.isEmpty(serialNumberIds)) {
                allSerialNumberIds.addAll(serialNumberIds);
            }
        }
        allSerialNumberIds = filterRewardedSerialNumberIds(tenant.getTenantId(), allSerialNumberIds);

        Map<String, IObjectData> serialNumberMap = loadSerialNumberMap(event.getTenantId(), allSerialNumberIds);

        JSONObject productRangeJson = JSON.parseObject(activity.get(TPMActivityFields.PRODUCT_RANGE, String.class));
        String productRangeType = productRangeJson.getString("type");
        Set<String> skuIds = Sets.newHashSet();
        if ("FIXED".equalsIgnoreCase(productRangeType)) {
            List<IObjectData> range = queryActivityProductRange(activity);
            skuIds = range.stream().map(m -> m.get("product_id", String.class)).collect(Collectors.toSet());
            if (TPMGrayUtils.isMengNiuSignInGoodsFreshStandard(activity.getTenantId())){
                log.info("signInGoodsFreshStandard tenantId is {}", activity.getTenantId());
                handlerProductRangeByFreshStandard(activity, serialNumberMap, deliveryNoteDetails, range);
            }
        }

        List<FullDeliveryNoteDetailInformation> fullDetails = Lists.newArrayList();
        for (IObjectData deliveryNoteDetail : deliveryNoteDetails) {

            @SuppressWarnings("unchecked")
            List<String> serialNumberIds = deliveryNoteDetail.get("unique_product_code_combination", List.class);
            String salesOrderDetailId = deliveryNoteDetail.get("sales_order_product_id", String.class);

            if (!CollectionUtils.isEmpty(serialNumberIds)) {
                for (String serialNumberId : serialNumberIds) {
                    if (serialNumberMap.containsKey(serialNumberId)) {
                        String skuId = deliveryNoteDetail.get("product_id", String.class);

                        if ("ALL".equalsIgnoreCase(productRangeType) ||
                                ("FIXED".equalsIgnoreCase(productRangeType) && skuIds.contains(skuId))) {

                            FullDeliveryNoteDetailInformation fullDetail = new FullDeliveryNoteDetailInformation();
                            fullDetail.setId(deliveryNoteDetail.getId());
                            fullDetail.setSkuId(skuId);
                            fullDetail.setSnId(serialNumberId);
                            if (salesOrderDetailMap.containsKey(salesOrderDetailId)) {
                                fullDetail.setSalesOrderDetailId(salesOrderDetailMap.get(salesOrderDetailId).getId());
                                fullDetail.setSalesOrderDetailName(salesOrderDetailMap.get(salesOrderDetailId).getName());
                            }
                            fullDetail.setSnObj(serialNumberMap.get(serialNumberId));

                            fullDetails.add(fullDetail);
                        }
                    }
                }
            }
        }

        return FullSignInGoodsEventInformation.builder()
                .store(store)
                .activity(activity)
                .rewardLimitConfig(loadRewardLimitConfig(tenant, activity, store.get(AccountFields.CHANNEL, String.class), store.getId()))
                .salesOrder(salesOrder)
                .deliveryNote(deliveryNote)
                .deliveryNoteDetails(fullDetails)
                .tenantName(getTenantName(event.getTenantId()))
                .build();
    }

    private List<String> filterRewardedSerialNumberIds(String tenantId, List<String> allSerialNumberIds) {
        if (CollectionUtils.isEmpty(allSerialNumberIds)) {
            return Lists.newArrayList();
        }

        IFilter masterRecordIdFilter = new Filter();
        masterRecordIdFilter.setFieldName(RedPacketRecordDetailObjFields.SERIAL_NUMBER_ID);
        masterRecordIdFilter.setOperator(Operator.IN);
        masterRecordIdFilter.setFieldValues(allSerialNumberIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterRecordIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.RED_PACKET_RECORD_DETAIL_OBJ,
                stq,
                Lists.newArrayList("_id")).stream().map(DBRecord::getId).collect(Collectors.toList());
    }

    private void handlerProductRangeByFreshStandard(IObjectData activity,
                                                  Map<String, IObjectData> serialNumberMap,
                                                  List<IObjectData> deliveryNoteDetails,
                                                  List<IObjectData> range) {

        // no limit 不处理
        String type = activity.get(TPMActivityFields.PRODUCT_RANGE_FRESH_STANDARD, String.class);
        if (TPMActivityFields.ProductRangeFreshStandard.NO_LIMIT.equalsIgnoreCase(type)){
            return;
        }

        if (CollectionUtils.isEmpty(range)){
            return;
        }

        Map<String, IObjectData> productRangeMap = range.stream().collect(Collectors.toMap(v -> v.get("product_id", String.class), v -> v));
        Set<String> productIds = productRangeMap.keySet();
        Map<String, Integer> prodcutQuantityMap = new HashMap<>();
        if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equalsIgnoreCase(type)){
            prodcutQuantityMap = findProductQualityGuaranteePeriod(activity.getTenantId(), productIds);
        }
        log.info("prodcutQuantityMap size  is {}", prodcutQuantityMap.size());
        log.info("before serialNumberMap size is {}", serialNumberMap.size());
        // 产品保险度类型
        for (IObjectData deliveryNoteDetail : deliveryNoteDetails) {
            @SuppressWarnings("unchecked")
            List<String> serialNumberIds = deliveryNoteDetail.get("unique_product_code_combination", List.class);
            if (!CollectionUtils.isEmpty(serialNumberIds)) {
                for (String serialNumberId : serialNumberIds) {
                    IObjectData serialNumberObj = serialNumberMap.get(serialNumberId);
                    if (serialNumberObj != null){
                        String skuId = deliveryNoteDetail.get("product_id", String.class);
                        if (productRangeMap.containsKey(skuId)) {
                            IObjectData productRange = productRangeMap.get(skuId);
                            Long manufactureDate = serialNumberObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class);
                            if (TPMActivityFields.ProductRangeFreshStandard.BY_DATE_RANGE.equalsIgnoreCase(type) &&
                                    !filterProductRangeByDateRange(productRange, manufactureDate, skuId)) {
                                log.info("serialNumberId:{} 不满足按日期范围，排除", serialNumberId);
                                // 不满足按日期范围，排除
                                serialNumberMap.remove(serialNumberId);
                            }else if (TPMActivityFields.ProductRangeFreshStandard.BY_REMAINING_DAYS.equalsIgnoreCase(type) &&
                                    !filterProductRangeByRemainingDays(productRange, manufactureDate, prodcutQuantityMap, skuId)) {
                                log.info("serialNumberId:{} 不满足按剩余有效期，排除", serialNumberId);
                                //不满足按剩余有效期，排除
                                serialNumberMap.remove(serialNumberId);
                            }
                        }
                    }
                }
            }
        }
        log.info("after serialNumberMap size is {}", serialNumberMap.size());
    }

    private boolean filterProductRangeByRemainingDays(IObjectData productRange,
                                                      Long manufactureDate,
                                                      Map<String, Integer> prodcutQuantityMap,
                                                      String skuId) {
        Integer quantity = prodcutQuantityMap.get(skuId);
        if (quantity == null){
            log.info("productRange productId {} quantity is null", skuId);
            return false;
        }

        if (manufactureDate == null){
            log.info(" productId {} serialNumber manufactureDate is null", skuId);
            return false;
        }

        String matchMethod = productRange.get(TPMActivityProductRangeFields.MATCH_METHOD, String.class);
        Long expiredDays = productRange.get(TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS, Long.class);
        long remainingDays = (toDayStart(manufactureDate) + quantity * 24 * 60 * 60 * 1000L - toDayStart(System.currentTimeMillis())) / (24 * 60 * 60 * 1000);
        log.info("remainingDays is {}, expiredDays is {}", remainingDays, expiredDays);
        if (matchMethod.equals(TPMActivityProductRangeFields.MATCH_METHOD__NEW_GOODS)) {
            return remainingDays >= expiredDays;
        } else {
            return remainingDays <= expiredDays;
        }
    }

    private Map<String, Integer> findProductQualityGuaranteePeriod(String tenantId, Set<String> productIds) {
        // 查询产品
        IFilter idFilter = new Filter();
        idFilter.setFieldName(CommonFields.ID);
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(new ArrayList<>(productIds));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery rangeQuery = QueryDataUtil.minimumQuery(Lists.newArrayList(idFilter), Lists.newArrayList(order));

        List<IObjectData> product = QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.PRODUCT_OBJ,
                rangeQuery,
                Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.RECORD_TYPE, ProductFields.QUALITY_GUARANTEE_PERIOD)
        );
        return product.stream().filter(v -> v.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class) != null)
                .collect(Collectors.toMap(v -> v.get(CommonFields.ID, String.class), v -> v.get(ProductFields.QUALITY_GUARANTEE_PERIOD, Integer.class)));
    }

    private long toDayStart(long time) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return localDateTime.toLocalDate().atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    private boolean filterProductRangeByDateRange(IObjectData productRange, Long manufactureDate, String skuId) {
        if (manufactureDate == null){
            log.info(" productId {} serialNumber manufactureDate is null", skuId);
            return false;
        }

        Long start = productRange.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_START, Long.class, 0L);
        Long end = productRange.get(TPMActivityProductRangeFields.MANUFACTURE_DATE_END, Long.class, 0L);
        return manufactureDate >= start && manufactureDate <= end;
    }

    private Map<String, IObjectData> loadSalesOrderDetailMap(String tenantId, List<String> salesOrderDetailIds) {
        if (CollectionUtils.isEmpty(salesOrderDetailIds)) {
            return Maps.newHashMap();
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(salesOrderDetailIds);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(idFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.SALES_ORDER_PRODUCT_OBJ, stq, Lists.newArrayList(
                "_id", "name"
        )).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
    }

    private Map<String, IObjectData> loadSerialNumberMap(String tenantId, List<String> allSerialNumberIds) {
        if (CollectionUtils.isEmpty(allSerialNumberIds)) {
            return Maps.newHashMap();
        }

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.IN);
        idFilter.setFieldValues(allSerialNumberIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.ID);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(Lists.newArrayList(idFilter), Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenantId, "FMCGSerialNumberObj", stq, Lists.newArrayList(
                "_id", "name", "manufacture_date", "batch_code"
        )).stream().collect(Collectors.toMap(DBRecord::getId, v -> v));
    }

    private List<IObjectData> queryActivityProductRange(IObjectData activity) {
        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName("activity_id");
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(activity.getId()));

        SearchTemplateQuery rangeQuery = QueryDataUtil.minimumQuery(activityIdFilter);

        List<IObjectData> range = QueryDataUtil.find(
                serviceFacade,
                activity.getTenantId(),
                "TPMActivityProductRangeObj",
                rangeQuery,
                Lists.newArrayList("_id", "product_id", "store_owner_red_packet_amount__c", "m_boss_red_packet_amount__c",
                        TPMActivityProductRangeFields.MANUFACTURE_DATE_START, TPMActivityProductRangeFields.MANUFACTURE_DATE_END,
                        TPMActivityProductRangeFields.MATCH_METHOD, TPMActivityProductRangeFields.TO_BE_EXPIRED_DAYS)
        );

        if (CollectionUtils.isEmpty(range)) {
            return Lists.newArrayList();
        }
        return range;
    }

    private List<IObjectData> queryDeliveryNoteDetails(String tenantId, String deliveryNoteId) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName("delivery_note_id");
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(deliveryNoteId));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.DELIVERY_NOTE_PRODUCT_OBJ, stq, Lists.newArrayList(
                "_id",
                "unique_product_code_combination",
                "product_id",
                "sales_order_product_id"
        ));
    }

    private RedPacketReward calculateSalesmenReward(
            MengNiuTenantInformation tenant,
            SalesEvent<SignInGoodsEventData> event,
            FullSignInGoodsEventInformation fullEvent) {

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_SALESMEN);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_SALESMEN);
        String rewardPersonId = fullEvent.getDeliveryNote().getOwner().get(0);
        PaymentAccount to = PaymentAccount.of(
            ApiNames.PERSONNEL_OBJ,
            String.format("%s.%s", fullEvent.getDeliveryNote().getTenantId(), rewardPersonId),
            loadWeChatPaymentAccountFromEmployee(fullEvent.getDeliveryNote().getTenantId(), rewardPersonId));

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_SALESMEN.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_SALESMEN.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_SALESMEN, from, to, details, publishMode, expirationDate);
    }

    private RedPacketReward calculateStoreOwnerReward(
            MengNiuTenantInformation tenant,
            SalesEvent<SignInGoodsEventData> event,
            FullSignInGoodsEventInformation fullEvent) {

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_STORE_OWNER);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_STORE_OWNER);
        PaymentAccount to = loadWeChatPaymentAccountFromStore(fullEvent.getDeliveryNote().getTenantId(), fullEvent.getDeliveryNote().get("account_id", String.class));

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_STORE_OWNER.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_STORE_OWNER.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_STORE_OWNER, from, to, details, publishMode, expirationDate);
    }

    private static Long getExpirationDate(Integer effectiveDay) {
        Long expirationDate = null;
        if (effectiveDay != null){
            LocalDateTime localDateTime = LocalDateTime.now();
            expirationDate = localDateTime.plusDays(effectiveDay + 1).withHour(0).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() - 1;
        }
        return expirationDate;
    }

    private List<FullDeliveryNoteDetailInformation> calculateLimitedDetails(MengNiuTenantInformation tenant, FullSignInGoodsEventInformation fullEvent, String role) {
        RewardedData rewardedData;
        if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(tenant.getManufacturer().getTenantId(), fullEvent.getActivity().getRecordType())){
            rewardedData = calculateRewardedDataV2(tenant, fullEvent, role);
        }else{
            rewardedData = calculateRewardedData(tenant, fullEvent, role);
        }

        if (fullEvent.getRewardLimitConfig().getLimitMode().equals(RewardLimitConfig.SINGLE_ORDER) && rewardedData.getTotalRecordCount() > 0) {
            return Lists.newArrayList();
        }

        if (fullEvent.getRewardLimitConfig().getLimitMode().equals(RewardLimitConfig.MULTIPLE_ORDER) &&
                fullEvent.getRewardLimitConfig().getTotalLimit() == -1 && fullEvent.getRewardLimitConfig().getSkuLimit().isEmpty()
        ) {
            return fullEvent.getDeliveryNoteDetails();
        }

        if (fullEvent.getRewardLimitConfig().getTotalLimit() != -1 && rewardedData.getTotalCount() >= fullEvent.getRewardLimitConfig().getTotalLimit()) {
            return Lists.newArrayList();
        }

        List<FullDeliveryNoteDetailInformation> details = Lists.newArrayList();

        for (FullDeliveryNoteDetailInformation deliveryNoteDetail : fullEvent.getDeliveryNoteDetails()) {
            String skuId = deliveryNoteDetail.getSkuId();
            if (fullEvent.getRewardLimitConfig().getSkuLimit().containsKey(skuId)) {
                int limit = fullEvent.getRewardLimitConfig().getSkuLimit().get(skuId);
                int rewardedCount = rewardedData.getSkuCount().getOrDefault(skuId, 0);
                if (rewardedCount >= limit) {
                    continue;
                }
            }

            details.add(deliveryNoteDetail);

            if (rewardedData.getSkuCount().containsKey(skuId)) {
                rewardedData.getSkuCount().put(skuId, rewardedData.getSkuCount().get(skuId) + 1);
            } else {
                rewardedData.getSkuCount().put(skuId, 1);
            }
        }

        if (fullEvent.getRewardLimitConfig().getTotalLimit() != -1) {
            int count = fullEvent.getRewardLimitConfig().getTotalLimit() - rewardedData.getTotalCount();
            if (details.size() > count) {
                details = details.subList(0, count);
            }
        }

        return details;
    }

    private RewardedData calculateRewardedDataV2(MengNiuTenantInformation tenant, FullSignInGoodsEventInformation fullEvent, String role) {

        IFilter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(RedPacketRecordObjFields.ACTIVITY_ID);
        activityIdFilter.setOperator(Operator.EQ);
        activityIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getActivity().getId()));

        IFilter relatedStoreIdFilter = new Filter();
        relatedStoreIdFilter.setFieldName(RedPacketRecordObjFields.ACCOUNT_ID);
        relatedStoreIdFilter.setOperator(Operator.EQ);
        relatedStoreIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getStore().getId()));

        IFilter dataIdFilter = new Filter();
        dataIdFilter.setFieldName(RedPacketRecordObjFields.RELATED_OBJECT_DATA_ID);
        dataIdFilter.setOperator(Operator.NEQ);
        dataIdFilter.setFieldValues(Lists.newArrayList(fullEvent.getSalesOrder().getId()));

        IFilter roleFilter = new Filter();
        roleFilter.setFieldName(RedPacketRecordObjFields.ROLE);
        roleFilter.setOperator(Operator.EQ);
        roleFilter.setFieldValues(Lists.newArrayList(RewardConstants.ROLE_VALUE_MAP.get(role)));

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(activityIdFilter, relatedStoreIdFilter, roleFilter, dataIdFilter),
                Lists.newArrayList(order)
        );

        List<IObjectData> records = QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                ApiNames.RED_PACKET_RECORD_OBJ,
                stq,
                Lists.newArrayList(
                        "_id"
                ));


        RewardedData data = new RewardedData();
        data.setTotalRecordCount(records.size());

        List<String> recordIds = records.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<IObjectData> recordDetails = queryRecordDetailsV2(tenant, recordIds);

        data.setTotalCount(recordDetails.size());
        data.setSkuCount(Maps.newHashMap());

        for (IObjectData recordDetail : recordDetails) {
            String productId = recordDetail.get(RedPacketRecordDetailObjFields.PRODUCT_ID, String.class);
            if (!Strings.isNullOrEmpty(productId)) {
                if (data.getSkuCount().containsKey(productId)) {
                    data.getSkuCount().put(productId, data.getSkuCount().get(productId) + 1);
                } else {
                    data.getSkuCount().put(productId, 1);
                }
            }
        }

        return data;
    }

    private List<IObjectData> queryRecordDetailsV2(MengNiuTenantInformation tenant, List<String> recordIds) {
        if (CollectionUtils.isEmpty(recordIds)) {
            return Lists.newArrayList();
        }

        IFilter masterRecordIdFilter = new Filter();
        masterRecordIdFilter.setFieldName(RedPacketRecordDetailObjFields.RED_PACKET_RECORD_ID);
        masterRecordIdFilter.setOperator(Operator.IN);
        masterRecordIdFilter.setFieldValues(recordIds);

        OrderBy order = new OrderBy();
        order.setFieldName(CommonFields.CREATE_TIME);
        order.setIsAsc(true);

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(
                Lists.newArrayList(masterRecordIdFilter),
                Lists.newArrayList(order)
        );

        return QueryDataUtil.find(
                serviceFacade,
                tenant.getManufacturer().getTenantId(),
                ApiNames.RED_PACKET_RECORD_DETAIL_OBJ,
                stq,
                Lists.newArrayList(
                        "_id", RedPacketRecordDetailObjFields.PRODUCT_ID
                ));
    }

    private List<RedPacketRewardDetail> buildupRedPacketRewardDetails(
            MengNiuTenantInformation tenant,
            FullSignInGoodsEventInformation fullEvent,
            String role) {

        String defaultAmountKey = role.toLowerCase() + "_red_packet_amount__c";
        BigDecimal defaultAmount = fullEvent.getActivity().get(defaultAmountKey, BigDecimal.class);
        String storeId = fullEvent.getDeliveryNote().get("account_id", String.class);

        List<FullDeliveryNoteDetailInformation> limitedDetails = calculateLimitedDetails(tenant, fullEvent, role);

        List<RedPacketRewardDetail> details = Lists.newArrayList();
        for (FullDeliveryNoteDetailInformation detail : limitedDetails) {
            BigDecimal amount = defaultAmount;
            RewardAmountConfig amountConfig = rewardAmountConfigService.get(
                    tenant.getManufacturer().getTenantId(),
                    fullEvent.getActivity().getId(),
                    role,
                    storeId,
                    detail.getSkuId());
            if (!Objects.isNull(amountConfig)) {
                amount = amountConfig.getAmount();
            }

            RedPacketRewardDetail recordDetail = new RedPacketRewardDetail();
            recordDetail.setSerialNumberId(detail.getSnId());
            recordDetail.setProductId(detail.getSkuId());
            recordDetail.setSerialNumberName(detail.getSnObj().getName());
            recordDetail.setManufactureDate(detail.getSnObj().get("manufacture_date", Long.class));
            recordDetail.setBatchCode(detail.getSnObj().get("batch_code", String.class));
            recordDetail.setSalesOrderDetailId(detail.getSalesOrderDetailId());
            recordDetail.setSalesOrderDetailName(detail.getSalesOrderDetailName());
            recordDetail.setAmount(amount);
            recordDetail.setAmountConfigId(amountConfig == null ? null : amountConfig.getId());
            details.add(recordDetail);
        }
        return details;
    }

    private RedPacketReward calculateMBossReward(
            MengNiuTenantInformation tenant,
            SalesEvent<SignInGoodsEventData> event,
            FullSignInGoodsEventInformation fullEvent) {
        if (!tenant.getRole().equals(MengNiuTenantInformation.ROLE_M)) {
            return null;
        }

        List<RedPacketRewardDetail> details = buildupRedPacketRewardDetails(tenant, fullEvent, RewardConstants.ROLE_M_BOSS);
        PaymentAccount from = loadTenantCloudAccountByRole(tenant, fullEvent.getActivity(), RewardConstants.ROLE_M_BOSS);
        String mBossEmployeeId = findMBossEmployeeId(tenant.getM().getTenantId());
        WeChatPaymentAccount account;
        if (Strings.isNullOrEmpty(mBossEmployeeId)) {
            account = WeChatPaymentAccount.builder().build();
        } else {
            account = loadWeChatPaymentAccountFromEmployee(fullEvent.getDeliveryNote().getTenantId(), mBossEmployeeId);
        }
        PaymentAccount to = PaymentAccount.of(
            ApiNames.PERSONNEL_OBJ,
            String.format("%s.%s", fullEvent.getDeliveryNote().getTenantId(), mBossEmployeeId),
            account
        );

        String publishModeKey = String.format("%s_publish_mode__c", RewardConstants.ROLE_M_BOSS.toLowerCase());
        String publishMode = fullEvent.getActivity().get(publishModeKey, String.class, RedPacketReward.PUBLISH_MODE_AUTO);
        // 取活动上的有效天数
        Long expirationDate = getExpirationDate(fullEvent, publishMode, RewardConstants.ROLE_M_BOSS.toLowerCase());

        return RedPacketReward.of(tenant, event, fullEvent, RewardConstants.ROLE_M_BOSS, from, to, details, publishMode, expirationDate);
    }

    @Override
    protected void validateEventData(SignInGoodsEventData data) {
        if (Strings.isNullOrEmpty(data.getDeliveryNoteId())) {
            throw new EventAbandonException("delivery note id empty.");
        }
    }
}