package com.facishare.crm.fmcg.mengniu.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.mengniu.dto.ConsumerScanEventData;
import com.facishare.crm.fmcg.mengniu.dto.SalesEvent;
import com.facishare.crm.fmcg.mengniu.dto.SignInGoodsEventData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SalesEventDistributor {

    @Resource
    private SignInGoodsEventHandler signInGoodsEventHandler;
    @Resource
    private ConsumerScanEventHandler consumerScanEventHandler;

    public void process(JSONObject message) {
        String eventType = message.getString("event_type");
        switch (eventType) {
            case "SIGN_IN_GOODS":
                signInGoodsEventHandler.invoke(convertToSignInGoodsEvent(message));
                break;
            case "STORE_STOCK_CHECK":
                signInGoodsEventHandler.invoke(convertToSignInGoodsEvent(message));
                break;
            case "CONSUMER_SCAN":
                consumerScanEventHandler.invoke(convertToConsumerScanEvent(message));
                break;
            default:
                log.warn("unknown sales event type : {}", eventType);
                break;
        }
    }

    private SalesEvent<ConsumerScanEventData> convertToConsumerScanEvent(JSONObject message) {
        return message.toJavaObject(new TypeReference<SalesEvent<ConsumerScanEventData>>() {
        });
    }

    private SalesEvent<SignInGoodsEventData> convertToSignInGoodsEvent(JSONObject message) {
        return message.toJavaObject(new TypeReference<SalesEvent<SignInGoodsEventData>>() {
        });
    }
}
