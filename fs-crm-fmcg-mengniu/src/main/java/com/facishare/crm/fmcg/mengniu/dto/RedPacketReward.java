package com.facishare.crm.fmcg.mengniu.dto;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.tpm.api.MengNiuTenantInformation;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;

@Data
@ToString
@Builder
public class RedPacketReward implements Serializable {

    public static final String PUBLISH_MODE_ARTIFICIAL = "artificial";
    public static final String PUBLISH_MODE_AUTO = "auto";

    private String tenantId;

    private String identity;

    private String paymentIdentity;

    private String eventType;

    private String role;

    private String customerRewardLevel;

    private long eventTime;

    private String activityId;

    private String activityRecordType;

    private String eventObjectName;

    private String eventObjectTenantId;

    private String eventObjectTenantName;

    private String eventObjectApiName;

    private String eventObjectDataId;

    private String relatedStoreName;

    private String relatedStoreId;

    private PaymentAccount from;

    private PaymentAccount to;

    private String rewardPersonType;

    private String rewardPersonId;

    private Long expirationTime;

    private BigDecimal amount;

    private String remarks;

    private List<RedPacketRewardDetail> details;

    private String publishMode;

    private boolean overLimit = false;

    public static RedPacketReward of(
            SalesEvent<ConsumerScanEventData> event,
            PaymentAccount from,
            PaymentAccount to,
            List<RedPacketRewardDetail> details,
            RoleRewardAmount amount,
            String role,
            String publishMode
    ) {
        String remarksKey = String.format("%s_red_packet_remarks__c", role.toLowerCase());
        String remarks = event.getData().getActivity().get(remarksKey, String.class);

        if (!StringUtils.isEmpty(remarks)) {
            remarks = remarks.replace("${activity_name}", event.getData().getActivity().getName());
            remarks = remarks.replace("${related_data_name}", event.getData().getSn().getName());
            remarks = remarks.replace("${related_store_name}", event.getData().getStore().getName());
        }

        return RedPacketReward.builder()
                .tenantId(event.getTenantId())
                .identity(String.format("%s.%s.%s.%s", event.getTenantId(), event.getEventType(), role, event.getData().getSn().getId()).toUpperCase())
                .paymentIdentity(IdentityIdGenerator.formPaymentIdentityId())
                .eventType(event.getEventType())
                .eventTime(event.getEventTime())
                .activityId(event.getData().getActivity().getId())
                .eventObjectTenantId(event.getData().getRelatedBusinessObjectTenant().getTenantId())
                .eventObjectTenantName(event.getData().getRelatedBusinessObjectTenantName())
                .activityRecordType(event.getData().getActivity().getRecordType())
                .eventObjectApiName(ApiNames.FMCG_SERIAL_NUMBER_OBJ)
                .eventObjectDataId(event.getData().getSn().getId())
                .eventObjectName(event.getData().getSn().getName())
                .relatedStoreId(event.getData().getStore().getId())
                .relatedStoreName(event.getData().getStore().getName())
                .role(role)
                .from(from)
                .to(to)
                .rewardPersonId(Objects.isNull(to) ? null : to.getRewardPersonId())
                .rewardPersonType(Objects.isNull(to) ? null : to.getRewardPersonType())
                .details(details)
                .remarks(remarks)
                .amount(amount.getAmount())
                .customerRewardLevel(amount.getConsumerRewardLevel())
                .publishMode(publishMode)
                .build();
    }

    public static String buildIdentityForActionReward(String role, SalesEvent<ObjectActionEventData> event) {
        return String.format("%s.%s.%s.%s.%s.%s",
                event.getTenantId(),
                event.getEventType(),
                role,
                event.getData().getObjectData().getDescribeApiName(),
                event.getData().getObjectAction(),
                event.getData().getObjectData().getId()).toUpperCase();
    }

    public static RedPacketReward of(
            MengNiuTenantInformation tenant,
            String eventTenantName,
            SalesEvent<ObjectActionEventData> event,
            IObjectData activity,
            IObjectData store,
            String role,
            PaymentAccount from,
            PaymentAccount to,
            RoleRewardAmount amount,
            String publishMode
    ) {

        String identity = buildIdentityForActionReward(role, event);
        String paymentIdentity = IdentityIdGenerator.formPaymentIdentityId();

        String remarksKey = String.format("%s_red_packet_remarks__c", role.toLowerCase());
        String remarks = activity.get(remarksKey, String.class);

        if (!StringUtils.isEmpty(remarks)) {
            remarks = remarks.replace("${activity_name}", activity.getName());
            remarks = remarks.replace("${related_data_name}", event.getData().getObjectData().getName());
            remarks = remarks.replace("${related_store_name}", store.getName());
        }
        Long expireTime = null;
        Integer expireDays = activity.get(TPMActivityFields.MN_OBJECT_ACTION_RED_PACKET_EXPIRATION_DAYS, Integer.class);
        if (Objects.nonNull(expireDays)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(event.getEventTime()), TimeZone.getDefault().toZoneId());
            expireTime = localDateTime.plusDays(expireDays + 1).toLocalDate().atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli() - 1;
        }
        return RedPacketReward.builder()
                .tenantId(tenant.getManufacturer().getTenantId())
                .identity(identity)
                .paymentIdentity(paymentIdentity)
                .eventType(event.getEventType())
                .eventTime(event.getEventTime())
                .eventObjectTenantId(event.getTenantId())
                .eventObjectTenantName(eventTenantName)
                .eventObjectApiName(event.getData().getObjectData().getDescribeApiName())
                .eventObjectDataId(event.getData().getObjectData().getId())
                .eventObjectName(event.getData().getObjectData().getName())
                .relatedStoreName(store.getName())
                .relatedStoreId(store.getId())
                .activityId(activity.getId())
                .activityRecordType(activity.getRecordType())
                .role(role)
                .remarks(remarks)
                .from(from)
                .to(to)
                .rewardPersonId(Objects.isNull(to) ? null : to.getRewardPersonId())
                .rewardPersonType(Objects.isNull(to) ? null : to.getRewardPersonType())
                .amount(amount.getAmount())
                .details(Lists.newArrayList())
                .publishMode(publishMode)
                .expirationTime(expireTime)
                .build();
    }

    public static RedPacketReward of(
            MengNiuTenantInformation tenant,
            SalesEvent<SignInGoodsEventData> event,
            FullSignInGoodsEventInformation fullEvent,
            String role,
            PaymentAccount from,
            PaymentAccount to,
            List<RedPacketRewardDetail> details,
            String publishMode,
            Long expirationTime
    ) {

        String identity = String.format("%s.%s.%s.%s", event.getTenantId(), event.getEventType(), role, event.getData().getDeliveryNoteId()).toUpperCase();
        String paymentIdentity = IdentityIdGenerator.formPaymentIdentityId();

        String remarksKey = String.format("%s_red_packet_remarks__c", role.toLowerCase());
        String remarks = fullEvent.getActivity().get(remarksKey, String.class);

        if (!StringUtils.isEmpty(remarks)) {
            remarks = remarks.replace("${activity_name}", fullEvent.getActivity().getName());
            remarks = remarks.replace("${related_data_name}", fullEvent.getSalesOrder().getName());
            remarks = remarks.replace("${related_store_name}", fullEvent.getStore().getName());
        }

        return RedPacketReward.builder()
            .tenantId(tenant.getManufacturer().getTenantId())
            .identity(identity)
            .paymentIdentity(paymentIdentity)
            .eventType(event.getEventType())
            .eventTime(event.getEventTime())
            .eventObjectTenantId(event.getTenantId())
            .eventObjectTenantName(fullEvent.getTenantName())
            .eventObjectApiName(fullEvent.getSalesOrder().getDescribeApiName())
            .eventObjectDataId(fullEvent.getSalesOrder().getId())
            .eventObjectName(fullEvent.getSalesOrder().getName())
            .relatedStoreName(fullEvent.getStore().getName())
            .relatedStoreId(fullEvent.getStore().getId())
            .activityId(fullEvent.getActivity().getId())
            .activityRecordType(fullEvent.getActivity().getRecordType())
            .role(role)
            .remarks(remarks)
            .from(from)
            .to(to)
            .rewardPersonId(Objects.isNull(to) ? null : to.getRewardPersonId())
            .rewardPersonType(Objects.isNull(to) ? null : to.getRewardPersonType())
            .amount(total(details))
            .details(details)
            .publishMode(publishMode)
            .expirationTime(expirationTime)
            .build();
    }

    public static RedPacketReward of(
            MengNiuTenantInformation tenant,
            SalesEvent<StoreStockCheckEventData> event,
            FullStoreStockCheckEventInformation fullEvent,
            String role,
            PaymentAccount from,
            PaymentAccount to,
            List<RedPacketRewardDetail> details,
            String publishMode,
            Long expirationTime
    ) {

        String identity = String.format("%s.%s.%s.%s", event.getTenantId(), event.getEventType(), role, event.getData().getStoreStockCheckId()).toUpperCase();
        String paymentIdentity = IdentityIdGenerator.formPaymentIdentityId();

        String remarksKey = String.format("%s_red_packet_remarks__c", role.toLowerCase());
        String remarks = fullEvent.getActivity().get(remarksKey, String.class);

        if (!StringUtils.isEmpty(remarks)) {
            remarks = remarks.replace("${activity_name}", fullEvent.getActivity().getName());
            remarks = remarks.replace("${related_data_name}", fullEvent.getStoreStockCheck().getName());
            remarks = remarks.replace("${related_store_name}", fullEvent.getStore().getName());
        }

        return RedPacketReward.builder()
                .tenantId(tenant.getManufacturer().getTenantId())
                .identity(identity)
                .paymentIdentity(paymentIdentity)
                .eventType(event.getEventType())
                .eventTime(event.getEventTime())
                .eventObjectTenantId(event.getTenantId())
                .eventObjectTenantName(fullEvent.getTenantName())
                .eventObjectApiName(fullEvent.getStoreStockCheck().getDescribeApiName())
                .eventObjectDataId(fullEvent.getStoreStockCheck().getId())
                .eventObjectName(fullEvent.getStoreStockCheck().getName())
                .relatedStoreName(fullEvent.getStore().getName())
                .relatedStoreId(fullEvent.getStore().getId())
                .activityId(fullEvent.getActivity().getId())
                .activityRecordType(fullEvent.getActivity().getRecordType())
                .role(role)
                .remarks(remarks)
                .from(from)
                .to(to)
                .rewardPersonId(Objects.isNull(to) ? null : to.getRewardPersonId())
                .rewardPersonType(Objects.isNull(to) ? null : to.getRewardPersonType())
                .amount(total(details))
                .details(details)
                .publishMode(publishMode)
                .expirationTime(expirationTime)
                .build();
    }

    private static BigDecimal total(List<RedPacketRewardDetail> data) {
        BigDecimal total = new BigDecimal("0");
        for (RedPacketRewardDetail datum : data) {
            total = total.add(datum.getAmount());
        }
        return total;
    }
}