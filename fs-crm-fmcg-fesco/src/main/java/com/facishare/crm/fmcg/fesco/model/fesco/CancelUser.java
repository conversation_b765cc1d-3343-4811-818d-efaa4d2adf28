package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/27 11:07
 */
public interface CancelUser {

    @Data
    @ToString
    final class Arg implements Serializable {

        private String reason;

        private String phone;

        private String idCard;

        private String idCode;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    final class Result extends FescoCommonResult<String> {

    }
}
