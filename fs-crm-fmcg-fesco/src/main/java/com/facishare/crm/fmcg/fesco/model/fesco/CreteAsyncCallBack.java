package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27 11:07
 */
public interface CreteAsyncCallBack {

    @Data
    @ToString
    final class Arg implements Serializable {
        private int code;

        private boolean success;

        private String message;

        private TaskData data;
    }

    @Data
    @ToString
    final class Result {

    }

    @Data
    @ToString
    final class TaskData implements Serializable {

        private String taskNo;

        private int templateId;

        private String examineStatus;

        private String name;

        private String description;

        private String detail;

        private BigDecimal money;

        private BigDecimal serviceMoney;
        private BigDecimal totalMoney;

        private BigDecimal deductibleAmount;

        private BigDecimal paymentAmount;
        private BigDecimal paidAmount;
        private BigDecimal obligationAmount;
        private BigDecimal failureAmount;

        private String thirdTaskId;

        private List<CreateTask.ChildTaskData> childTaskList;
    }

    @Data
    @ToString
    final class ChildTaskData implements Serializable {

        private int subTaskId;

        private String idCard;

        private String name;

        private String cardNo;

        private BigDecimal money;

        private BigDecimal subServiceMoney;

        private String thirdSubId;

        private String state;

        private String message;
        private Date paymentTime;
        private String bankStreamNo;
        private String bankName;
    }


}
