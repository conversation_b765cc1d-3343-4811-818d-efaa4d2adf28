package com.facishare.crm.fmcg.common.apiname;

public abstract class DeliveryNoteProductFields {

    private DeliveryNoteProductFields() {
    }

    public static final String PRODUCT_ID = "product_id";
    public static final String SALES_PRICE = "sales_price";
    public static final String ORDER_PRODUCT_AMOUNT = "order_product_amount";
    public static final String AUXILIARY_DELIVERY_QUANTITY = "auxiliary_delivery_quantity";
    public static final String ACTUAL_UNIT = "actual_unit";
    public static final String DELIVERY_MONEY = "delivery_money";
    public static final String DELIVERY_NOTE_ID = "delivery_note_id";
    public static final String SALES_ORDER_ID = "sales_order_id";
    public static final String SALES_ORDER_PRODUCT_ID = "sales_order_product_id";
    public static final String DELIVERY_NUM = "delivery_num";
}
