package com.facishare.crm.fmcg.common.adapter.dto.pay;

/**
 * Author: linmj
 * Date: 2023/8/2 17:54
 */
public enum BatchTransferStatusEnum {

    INIT("INIT", "初始化"),
    FAIL("FAIL", "失败"),
    ACCEPTED("ACCEPTED", "已受理"),
    PROCESSING("PROCESSING", "转账中"),
    FINISHED("FINISHED", "已完成"),
    CLOSED("CLOSED", "已关闭");


    private String code;

    private String name;

    BatchTransferStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String code() {
        return this.code;
    }
}
