package com.facishare.crm.fmcg.common.apiname;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: ljs
 * Date: 2023/11/16 19:36
 */
public enum WithdrawPaymentStatusEnum {

    INIT("-1", "初始化"),
    PROCESSING("0", "处理中"),
    SUCCESS("1", "转账成功"),
    FAIL("2", "转账失败"),
    EXCEPT("3", "转账异常");

    private static final Map<String, WithdrawPaymentStatusEnum> MAP = Stream.of(values()).collect(Collectors.toMap(WithdrawPaymentStatusEnum::code, Function.identity()));

    private final String code;

    private final String desc;

    WithdrawPaymentStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WithdrawPaymentStatusEnum codeOf(String code) {
        return MAP.get(code);
    }

    public String code() {
        return code;
    }

    public static WithdrawPaymentStatusEnum getWithdrawFilterByStatus(String status) {
        if (WithdrawStatusQueryEnum.INIT.code().equals(status)) {
            return INIT;
        } else if (WithdrawStatusQueryEnum.PROCESSING.code().equals(status)) {
            return PROCESSING;
        } else if (WithdrawStatusQueryEnum.WITHDRAW_SUCCESS.code().equals(status)) {
            return SUCCESS;
        } else if (WithdrawStatusQueryEnum.WITHDRAW_FAIL.code().equals(status)) {
            return FAIL;
        } else {
            return EXCEPT;
        }
    }
}
