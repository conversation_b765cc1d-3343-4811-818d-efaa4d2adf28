package com.facishare.crm.fmcg.common.file;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年07月24日 16:45
 */
//IgnoreI18nFile
public class DisplayNameToJson {

    static List<String> objs = Lists.newArrayList(
            "TPMActivityItemObj",
            "TPMActivityItemCostStandardObj",
            "TPMActivityUnifiedCaseObj",
            "TPMActivityDealerScopeObj",
            "TPMActivityCashingProductScopeObj",
            "TPMActivityObj",
            "TPMActivityDetailObj",
            "TPMActivityStoreObj",
            "TPMActivityAgreementObj",
            "TPMActivityAgreementDetailObj",
            "TPMDealerActivityCostObj",
            "TPMStoreWriteOffObj",
            "TPMActivityProofObj",
            "TPMActivityProofDetailObj",
            "TPMActivityProofAuditObj",
            "TPMActivityProofAuditDetailObj",
            "TPMActivityCashingProductObj",
            "TPMActivityAgreementCashingProductObj",
            "TPMStoreWriteOffCashingProductObj",
            "TPMDealerActivityCashingProductObj",
            "TPMActivityUnifiedCaseProductRangeObj",
            "TPMActivityProductRangeObj",
            "TPMActivityVenueObj",
            "TPMActivityMaterialObj",

            "TPMBudgetBusinessSubjectObj",
            "TPMBudgetStatisticTableObj",
            "TPMBudgetAccountObj",
            "TPMBudgetAccountDetailObj",
            "TPMBudgetTransferDetailObj",
            "TPMBudgetOccupationDetailObj",
            "TPMBudgetCarryForwardObj",
            "TPMBudgetCarryForwardDetailObj",
            "TPMBudgetDisassemblyObj",
            "TPMBudgetDisassemblyNewDetailObj",
            "TPMBudgetDisassemblyExistsDetailObj",
            "TPMBudgetAccrualObj",
            "TPMBudgetAccrualDetailObj",

            "TPMActivityRewardDetailObj",
            "RedPacketRecordObj",
            "StorePromotionRecordObj",
            "RedPacketRecordDetailObj",
            "WithdrawRecordObj"
    );

    //todo fs-paas-filter-config-fmcg
    static String fsPaasFilterConfigFmcg = "{\"businessFilterConfig\":{\"TPMDealerActivityCostObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"AreaManageObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"mobile_list_program\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"TPMActivityCashingProductScopeObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"SalesAreaObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"mobile_list_program\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"PromoterObj\":{\"business\":[{\"tenants\":[784077,782910,84788],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"layout_rule_page\":1},\"group\":\"custom1\"}],\"filters\":{\"submodules\":{\"button_field_update\":[{\"data\":{\"label\":\"按钮入参字段\",\"rule_outside\":[{\"contain\":true,\"value_type\":\"define_type\",\"value\":[\"custom\"]},{\"contain\":true,\"value_type\":\"api_name\",\"value\":[\"promoter_type\",\"activity_id\",\"io_status\",\"review_status\",\"activity_status\"]}]},\"group\":\"default\"}]}}},\"ProjectStandardsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"WithdrawRecordObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityProofAuditObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"tile_mode\":1,\"table_mode\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityProofObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"tile_mode\":1,\"table_mode\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityBudgetObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"form_many\":1,\"is_master\":1,\"ui_event\":1,\"mobile_list_program\":1,\"summary_template_layout\":1,\"field_tag\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1},\"group\":\"default\"}]},\"TPMActivityBudgetDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1},\"group\":\"default\"}]},\"TPMBudgetAccrualDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"StockReportingDetailsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"TPMActivityBudgetAdjustObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1},\"group\":\"default\"}]},\"TPMActivityAgreementCashingProductObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityItemObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMDealerActivityObj\":{\"business\":[{\"tenants\":[],\"data\":{\"is_master\":1,\"summary_template_layout\":1,\"layout_rule_page\":1},\"group\":\"default\"}]},\"TPMBudgetCarryForwardObj\":{\"business\":[{\"tenants\":[],\"data\":{\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityProofDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityAgreementDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetAccrualObj\":{\"business\":[{\"tenants\":[],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityDealerScopeObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"ChannelObj\":{\"business\":[{\"tenants\":[],\"data\":{\"display_name\":1},\"group\":\"default\"}]},\"TPMActivityObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1,\"tile_mode\":1,\"table_mode\":1},\"group\":\"default\"}],\"filters\":{\"submodules\":{\"field_cascade\":[{\"tenants\":[],\"data\":{\"rule\":[{\"value_type\":\"api_name\",\"contain\":false,\"value\":[\"activity_type\"]}],\"label\":\"字段依赖关系\"},\"group\":\"default\"}]}}},\"MustDistributeProductsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"TPMBudgetAccountDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"StockReportingObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1},\"group\":\"default\"}]},\"RemakeMonitoringRecordObj\":{\"business\":[{\"tenants\":[],\"data\":{\"is_master\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"TPMDealerActivityCashingProductObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"ShelfReportDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"SalesStatementsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"mobile_list_layout\":1,\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1,\"scene_mobile_field\":1},\"group\":\"default\"}]},\"TPMBudgetDisassemblyExistsDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityProofAuditDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"PurchaseDetailsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"TPMBudgetCarryForwardDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"RedPacketRecordDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityItemCostStandardObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMStoreWriteOffObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"PointsGoodsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1},\"group\":\"default\"}],\"filters\":{\"submodules\":{\"button_field_update\":[{\"tenants\":[],\"data\":{\"label\":\"按钮字段变更的所需字段\",\"rule_outside\":[{\"value_type\":\"api_name\",\"contain\":true,\"value\":[\"state\",\"price\"]}]},\"group\":\"default\"}]}}},\"TPMActivityMaterialObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityStoreObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"ExpenseClaimFormDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"TPMActivityUnifiedCaseObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityRewardDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"AttendanceCorrectObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"layout_rule_page\":1},\"group\":\"default\"}]},\"AttendanceCorrectDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"layout_rule_page\":1},\"group\":\"default\"}]},\"ExpenseClaimFormObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"ProductCollectionObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"layout_rule_page\":1},\"group\":\"default\"}]},\"FMCGSerialNumberStatusObj\":{\"business\":[{\"tenants\":[],\"data\":{\"mobile_list_program\":1,\"mobile_list_layout\":1},\"group\":\"default\"}]},\"ShiftsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"layout_rule_page\":1},\"group\":\"default\"}],\"fieldConfig\":{\"ShiftsObj\":{\"owner\":[{\"data\":{\"ui_event\":1}}]}}},\"TPMActivityCashingProductObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetAccountObj\":{\"business\":[{\"tenants\":[\"84931\"],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"},{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"SuccessfulStoreRangeObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1},\"group\":\"default\"}]},\"PayObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"StorePromotionRecordObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"CheckinsObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1,\"mobile_list_layout_plugin\":1},\"group\":\"default\"}],\"filters\":{\"submodules\":{\"button_field_update\":[{\"tenants\":[\"85023\",\"83150\"],\"data\":{\"label\":\"按钮入参字段\",\"rule_outside\":[{\"value_type\":\"define_type\",\"contain\":true,\"value\":[\"system\"]},{\"value_type\":\"define_type\",\"contain\":true,\"value\":[\"package\"]}]},\"group\":\"default\"}]}}},\"TPMBudgetStatisticTableObj\":{\"business\":[{\"tenants\":[],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"ShelfReportObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1},\"group\":\"default\"}]},\"TPMActivityProductRangeObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"RedPacketRecordObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMActivityAgreementObj\":{\"business\":[{\"tenants\":[78612],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"},{\"tenants\":[],\"data\":{\"edit_layout\":1,\"is_master\":1,\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetTransferDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetDisassemblyObj\":{\"business\":[{\"tenants\":[],\"data\":{\"ui_event\":1,\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"PurchaseReportingObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"TPMActivityUnifiedCaseProductRangeObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetDisassemblyNewDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMBudgetOccupationDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"TPMStoreWriteOffCashingProductObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"AccountShiftDetailObj\":{\"business\":[{\"tenants\":[],\"data\":{\"display_name\":1},\"group\":\"default\"}]},\"AccountShiftObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"UserScheduleObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1},\"group\":\"default\"}]},\"TPMActivityVenueObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]},\"PointsExchangeRecordObj\":{\"business\":[{\"tenants\":[],\"data\":{\"edit_layout\":1,\"ui_event\":1,\"mobile_list_layout\":1,\"display_name\":1,\"layout_rule_page\":1},\"group\":\"default\"}],\"filters\":{\"submodules\":{\"button_field_update\":[{\"tenants\":[],\"data\":{\"label\":\"按钮字段变更的所需字段\",\"rule_outside\":[{\"value_type\":\"api_name\",\"contain\":true,\"value\":[\"order_state\"]}]},\"group\":\"default\"}]}}},\"TPMBudgetBusinessSubjectObj\":{\"business\":[{\"tenants\":[],\"data\":{\"summary_template_layout\":1,\"mobile_list_layout\":1,\"display_name\":1,\"new_scene\":1},\"group\":\"default\"}]}}}";

    public static void main(String[] args) {
        JSONObject filterJson = JSON.parseObject(fsPaasFilterConfigFmcg);
        JSONObject businessFilterConfig = filterJson.getJSONObject("businessFilterConfig");
        objs.forEach(s -> {
            if (businessFilterConfig.containsKey(s)) {
                JSONObject jsonObject = businessFilterConfig.getJSONObject(s);
                JSONArray business = jsonObject.getJSONArray("business");
                business.forEach(o -> {
                    JSONObject jsonObject1 = (JSONObject) o;
                    JSONObject data = jsonObject1.getJSONObject("data");
                    data.put("tile_mode", 1);
                    data.put("table_mode", 1);
                });
                businessFilterConfig.put(s, jsonObject);
            }else {
                String innerJSON = "{\"business\":[{\"tenants\":[],\"data\":{\"tile_mode\":1,\"table_mode\":1},\"group\":\"default\"}]}";
                businessFilterConfig.put(s, JSON.parseObject(innerJSON));
            }
        });
        filterJson.put("businessFilterConfig", businessFilterConfig);
        System.out.println(filterJson.toJSONString());
    }
}
