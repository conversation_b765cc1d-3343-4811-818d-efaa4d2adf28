package com.facishare.crm.fmcg.common.apiname;

/**
 * Author: linmj
 * Date: 2023/8/7 19:36
 */
public enum RedPacketAccountTypeEnum {

    WX("1", "微信账户"),
    CLOUD("2", "云账户"),
    BANK("3", "银行账户"),
    ALI("4", "支付宝账户"),
    ENTERPRISE_WX("5", "企业微信账户");

    private final String code;

    private final String desc;

    RedPacketAccountTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String code() {
        return code;
    }
}
