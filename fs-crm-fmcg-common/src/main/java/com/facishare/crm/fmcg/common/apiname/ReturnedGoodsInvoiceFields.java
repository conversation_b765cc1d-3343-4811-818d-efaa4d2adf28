package com.facishare.crm.fmcg.common.apiname;

public abstract class ReturnedGoodsInvoiceFields {

    private ReturnedGoodsInvoiceFields() {
    }

    public static final String ACCOUNT_ID = "account_id";
    public static final String ORDER_ID = "order_id";
    public static final String RETURN_MODE = "return_mode";
    public static final String REFUND_METHOD = "refund_method";
    public static final String RETURNED_GOODS_INV_AMOUNT = "returned_goods_inv_amount";
    public static final String CAR_SALES_RETURNED_AMOUNT = "car_sales_returned_amount";
    public static final String TOTAL_SETTLED_AMOUNT = "total_settled_amount";
    public static final String REFUND_AMOUNT = "refund_amount";
    public static final String PENDING_REFUND_AMOUNT = "pending_refund_amount";
    public static final String REFUND_AMOUNT_TO_BE_CONFIRMED = "refund_amount_to_be_confirmed";
    public static final String CAR_SALES_DEDUCTION_AMOUNT = "car_sales_deduction_amount";
    public static final String RETURNED_GOODS_TIME = "returned_goods_time";
    public static final String EXCHANGE_RECORD = "exchange__c";
    public static final String EXCHANGE_SETTLEMENT_AMOUNT = "exchange_settlement_amount";


}