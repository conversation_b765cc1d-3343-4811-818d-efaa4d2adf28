package com.facishare.crm.fmcg.common.apiname;

import com.google.common.collect.Lists;

import java.util.List;

public abstract class TPMProofTimePeriodDetailFields {

    private TPMProofTimePeriodDetailFields() {
    }

    public static final String PROOF_NUMBER = "proof_number";

    public static final String END_DATE = "end_date";

    public static final String PROOF_DISAGREE_NUMBER = "proof_disagree_number";

    public static final String PROOF_PERIOD = "proof_period";

    public static final String AGREEMENT_ID = "agreement_id";

    public static final String PROOF_STATUS = "proof_status";
    public static final String PROOF_STATUS__SCHEDULE = "schedule";
    public static final String PROOF_STATUS__NO_PROOF = "no_proof";
    public static final String PROOF_STATUS__PROOF = "proof";
    public static final String PROOF_STATUS__EXPECT = "expect";

    public static final String STAGE = "stage";

    public static final String BEGIN_DATE = "begin_date";

    public static final String ACTIVITY_ID = "activity_id";

    public static final String PROOF_AGREE_NUMBER = "proof_agree_number";

    public static final String ACHIEVEMENT_STATUS = "achievement_status";
    public static final String ACHIEVEMENT_STATUS__UNEXECUTED = "unexecuted";
    public static final String ACHIEVEMENT_STATUS__AGREE = "agree";
    public static final String ACHIEVEMENT_STATUS__DISAGREE = "disagree";
    public static final String ACHIEVEMENT_STATUS__SCHEDULE = "schedule";
    public static final String ACHIEVEMENT_STATUS__PARTIALLY = "partially";

    public static final String SOURCE_TYPE = "source_type";

    public static final List<String> ALL = Lists.newArrayList(END_DATE,
            PROOF_NUMBER,
            ACTIVITY_ID, PROOF_DISAGREE_NUMBER,
            AGREEMENT_ID,
            STAGE,
            BEGIN_DATE,
            PROOF_AGREE_NUMBER,
            SOURCE_TYPE,
            ACHIEVEMENT_STATUS,
            PROOF_STATUS,
            PROOF_PERIOD
    );
}