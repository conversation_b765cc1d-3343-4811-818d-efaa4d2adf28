package com.facishare.crm.fmcg.common.apiname;

public abstract class ReturnedGoodsInvoiceProductFields {

    private ReturnedGoodsInvoiceProductFields() {
    }

    /**
     * 退货单价
     */
    public static final String AUXILIARY_RETURNED_PRODUCT_PRICE = "auxiliary_returned_product_price";
    /**
     * 退货数量
     */
    public static final String AUXILIARY_QUANTITY = "auxiliary_quantity";

    /**
     * 退货单价 - 基准单位
     */
    public static final String RETURNED_PRODUCT_PRICE = "returned_product_price";
    /**
     * 退货数量 - 基准单位
     */
    public static final String QUANTITY = "quantity";

    public static final String RETURNED_GOODS_INV_ID = "returned_goods_inv_id";

    public static final String ORDER_PRODUCT_ID = "order_product_id";
    public static final String ORDER_ID = "order_id";

    public static final String SETTLED_AMOUNT = "settled_amount";
    public static final String PRODUCT_ID = "product_id";

    public static final String SWAP_OUT = "swap_out__c";
    public static final String SUBTOTAL = "subtotal";
}
