package com.facishare.crm.fmcg.common.apiname;

public abstract class AccountsReceivableDetailFields {

    private AccountsReceivableDetailFields() {
    }

    public static final String SKU_ID = "sku_id";
    public static final String TAX_PRICE = "tax_price";
    public static final String AR_QUANTITY = "ar_quantity";
    public static final String UNIT = "unit";
    public static final String PRICE_TAX_AMOUNT = "price_tax_amount";

    public static final String ORDER_ID = "order_id";
    public static final String ORDER_PRODUCT_ID = "orderproduct_id";

    public static final String DELIVERY_NOTE_ID = "delivery_note_id";
    public static final String DELIVERY_NOTE_PRODUCT_ID = "delivery_note_product_id";
    public static final String GOODS_RECEIVED_NOTE_ID = "goods_received_note_id";
    public static final String GOODS_RECEIVED_NOTE_PRODUCT_ID = "goods_received_note_product_id";

    public static final String SOURCE_API_NAME = "source_api_name";
    public static final String SOURCE_DATA_ID = "source_data_id";
    public static final String SOURCE_DETAIL_API_NAME = "source_detail_api_name";
    public static final String SOURCE_DETAIL_DATA_ID = "source_detail_data_id";

    public static final String AR_ID = "ar_id";
    public static final String NO_SETTLED_AMOUNT = "no_settled_amount";
    public static final String RECEIVABLE_OBJECT_DATA_ID = "receivable_object_data_id";
    public static final String RECEIVABLE_OBJECT_API_NAME = "receivable_object_api_name";
    public static final String RECEIVABLE_OBJECT_DETAIL_DATA_ID = "receivable_object_detail_data_id";
    public static final String RECEIVABLE_OBJECT_DETAIL_API_NAME = "receivable_object_detail_api_name";
}
