package com.facishare.crm.fmcg.common.apiname;

import com.google.common.collect.Lists;

import java.util.List;

public abstract class TPMActivityProofFields {

    private TPMActivityProofFields() {
    }

    public static final String STORE_ID = "store_id";
    public static final String PROOF_IMAGES = "proof_images";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String DEALER_ID = "dealer_id";
    public static final String DEALER_ACTIVITY = "dealer_activity";
    public static final String ACTIVITY_AGREEMENT_ID = "activity_agreement_id";
    public static final String AUDIT_STATUS = "audit_status";
    public static final String TOTAL = "total";
    public static final String ACTUAL_TOTAL = "actual_total";
    public static final String DEALER_ACTIVITY_COST_ID = "dealer_activity_cost_id";
    public static final String AUDIT_STATUS__SCHEDULE = "schedule";
    public static final String AUDIT_STATUS__PASS = "pass";
    public static final String AUDIT_STATUS__REJECT = "reject";
    public static final String AUDIT_STATUS__PARTIALLY = "partially";

    public static final String VISIT_ID = "visit_id";
    public static final String ACTION_ID = "action_id";
    public static final String REMARK = "remark";
    public static final String CREATE_TIME = "create_time";
    public static final String RANDOM_AUDIT_STATUS = "random_audit_status";
    public static final String RANDOM_AUDIT_STATUS__UNCHECKED = "unchecked";
    public static final String RANDOM_AUDIT_STATUS__CHECKED = "checked";
    public static final String AI_IDENTIFY_STATUS = "ai_identify_status";
    public static final String AI_IDENTIFY_STATUS_IDENTIFYING = "identifying";
    public static final String AI_IDENTIFY_STATUS_IDENTIFIED = "identified";
    public static final String AI_IDENTIFY_STATUS_IDENTIFY_FAILED = "identify_failed";
    public static final String OPEN_AI = "open_ai";
    public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";

    public static final String PASS_STATUS = "pass";
    public static final String FAIL_STATUS = "fail";
    public static final String PARTIAL_PASS_STATUS = "partial_pass";
    public static final String NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS = "not_display";
    public static final String PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS = "pending_approval";
    public static final String PROOF_TIME_PERIOD_DETAIL_ID = "proof_time_period_detail_id";


    //RIO
    public static final String IS_RECOGNIZED__C = "is_recognized__c";
    public static final List<String> ALL = Lists.newArrayList(STORE_ID,
            PROOF_IMAGES,
            ACTIVITY_ID, DEALER_ID,
            DEALER_ACTIVITY,
            ACTIVITY_AGREEMENT_ID,
            AUDIT_STATUS,
            TOTAL,
            ACTUAL_TOTAL,
            DEALER_ACTIVITY_COST_ID,
            VISIT_ID,
            ACTION_ID,
            CREATE_TIME,
            REMARK,
            RANDOM_AUDIT_STATUS,
            AI_IDENTIFY_STATUS,
            OPEN_AI
    );
}