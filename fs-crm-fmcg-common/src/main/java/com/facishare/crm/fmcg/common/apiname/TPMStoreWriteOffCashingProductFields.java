package com.facishare.crm.fmcg.common.apiname;

/**
 * author: wuyx
 * description:
 * createTime: 2022/12/9 14:20
 */
public abstract class TPMStoreWriteOffCashingProductFields {

    private TPMStoreWriteOffCashingProductFields() {
    }

    public static final String PRICE = "price";
    public static final String QUANTITY = "quantity";
    public static final String ACTIVITY_CASHING_PRODUCT_ID = "activity_cashing_product_id";
    public static final String AGREEMENT_CASHING_PRODUCT_ID = "agreement_cashing_product_id";
    public static final String AGREEMENT_CASHING_PRODUCT_ID_R = "agreement_cashing_product_id__r";
    public static final String AGREEMENT_PRICE = "agreement_price";
    public static final String AGREEMENT_QUANTITY = "agreement_quantity";
    public static final String TOTAL_PRICE = "total_price";
    public static final String STORE_WRITE_OFF = "store_write_off_id";
    public static final String UNIT = "unit";
}
