package com.facishare.crm.fmcg.common.apiname;

import com.google.common.collect.Lists;

import java.util.List;

public abstract class TPMActivityProofDetailFields {

    private TPMActivityProofDetailFields() {
    }

    public static final String AMOUNT = "amount";
    public static final String SUBTOTAL = "subtotal";
    public static final String ACTIVITY_ITEM_ID = "activity_item_id";
    public static final String TYPE = "type";
    public static final String ACTIVITY_DETAIL_ID = "activity_detail_id";
    public static final String ACTIVITY_AGREEMENT_DETAIL_ID = "activity_agreement_detail_id";
    public static final String ACTIVITY_PROOF_ID = "activity_proof_id";
    public static final String PROOF_DETAIL_AMOUNT_STANDARD = "proof_detail_amount_standard";
    public static final String PROOF_DETAIL_COST_STANDARD = "proof_detail_cost_standard";
    public static final String CALCULATE_PATTERN = "calculate_pattern";
    public static final String CALCULATE_PATTERN__R = "calculate_pattern__r";
    public static final String CALCULATE_PATTERN__V = "calculate_pattern__v";
    public static final String ACTIVITY_ITEM_COST_STANDARD_ID = "activity_item_cost_standard_id";
    public static final String AMOUNT_STANDARD_CHECK = "amount_standard_check";
    public static final String AMOUNT_STANDARD_CHECK__V = "amount_standard_check__v";
    public static final String ACTIVITY_ITEM_NAME = "activity_item_name";
    public static final String PROOF_ITEM = "proof_item";
    public static final String DISPLAY_FORM_ID = "display_form_id";
    public static final String AI_NUMBER = "ai_number";
    public static final String AI_FACE_NUMBER = "ai_face_number";
    public static final String AI_SKU_NUMBER = "ai_sku_number";
    public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";
    public static final String PRODUCT_DISPLAY_STATUS = "product_display_status";
    public static final String MATERIAL_DISPLAY_STATUS = "material_display_status";

    //单选
    public static final String PASS_STATUS = "pass";
    public static final String FAIL_STATUS = "fail";
    public static final String PARTIAL_PASS_STATUS = "partial_pass";
    public static final String NOT_DISPLAY_SYSTEM_JUDGMENT_STATUS = "not_display";
    public static final String PENDING_APPROVAL_SYSTEM_JUDGMENT_STATUS = "pending_approval";

    //RIO特殊字段
    public static final String AI_VISIBLE_NUMBER__C = "ai_visible_number__c";



    public static final List<String> ALL = Lists.newArrayList(CommonFields.ID,AMOUNT,
            SUBTOTAL,
            ACTIVITY_ITEM_ID,
            TYPE,
            ACTIVITY_DETAIL_ID,
            ACTIVITY_AGREEMENT_DETAIL_ID,
            ACTIVITY_PROOF_ID,
            PROOF_DETAIL_AMOUNT_STANDARD,
            PROOF_DETAIL_COST_STANDARD,
            CALCULATE_PATTERN,
            ACTIVITY_ITEM_COST_STANDARD_ID,
            AMOUNT_STANDARD_CHECK,
            PROOF_ITEM,
            DISPLAY_FORM_ID);
}