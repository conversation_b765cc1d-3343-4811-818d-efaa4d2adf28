package com.facishare.crm.fmcg.common.apiname;

public abstract class TPMBudgetTransferDetailFields {

    private TPMBudgetTransferDetailFields() {
    }

    public static final String AMOUNT = "amount";
    public static final String AMOUNT_AFTER_TRANSFER_IN = "amount_after_transfer_in";
    public static final String AMOUNT_BEFORE_TRANSFER_IN = "amount_before_transfer_in";
    public static final String AMOUNT_BEFORE_TRANSFER_OUT = "amount_before_transfer_out";
    public static final String AMOUNT_AFTER_TRANSFER_OUT = "amount_after_transfer_out";
    public static final String TRANSFER_TYPE = "transfer_type";
    public static final String OPERATION_STATUS = "operation_status";
    public static final String OPERATION_STATUS_FROZEN = "frozen";
    public static final String OPERATION_STATUS_SUCCESS = "success";
    public static final String OPERATION_STATUS_FAILED = "failed";
    public static final String OPERATION_STATUS_INEFFECTIVE = "ineffective";
    public static final String REMARKS = "remarks";
    public static final String TRANSFER_OUT_BUDGET_ACCOUNT_ID = "transfer_out_budget_account_id";
    public static final String TRANSFER_IN_BUDGET_ACCOUNT_ID = "transfer_in_budget_account_id";
    public static final String OPERATE_TIME = "operate_time";
    public static final String RECORD_TYPE_DEFAULT = "default__c";
    public static final String RECORD_TYPE_BUDGET_DEDUCT = "budget_deduct__c";
    public static final String RECORD_TYPE_BUDGET_ADD = "budget_add__c";
    public static final String BUDGET_CHANGE_DETAIL = "budget_change_detail__c";
}