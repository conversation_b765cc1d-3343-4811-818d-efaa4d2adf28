package com.facishare.crm.fmcg.common.apiname;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:13 PM
 */
public abstract class CommonFields {
    private CommonFields() {
    }

    public static final String OWNER = "owner";
    public static final String CREATE_BY = "created_by";
    public static final String NAME = "name";
    public static final String IS_DELETED = "is_deleted";
    public static final String TENANT_ID = "tenant_id";
    public static final String ID = "_id";
    public static final String RECORD_TYPE = "record_type";
    public static final String RECORD_TYPE__DEFAULT = "default__c";
    public static final String CREATE_TIME = "create_time";
    public static final String LIFE_STATUS = "life_status";
    public static final String LOCK_STATUS = "lock_status";
    public static final String LOCK_STATUS__LOCK = "1";
    public static final String OUT_OWNER = "out_owner";
    public static final String OBJECT_DESCRIBE_API_NAME = "object_describe_api_name";
    public static final String LIFE_STATUS__NORMAL = "normal";
    public static final String LIFE_STATUS__INEFFECTIVE = "ineffective";
    public static final String LIFE_STATUS__INVALID = "invalid";
    public static final String LIFE_STATUS__UNDER_REVIEW = "under_review";
    public static final String LIFE_STATUS__IN_CHANGE = "in_change";
    public static final String LAST_MODIFY_TIME = "last_modified_time";
    public static final String LAST_MODIFIED_BY = "last_modified_by";
    public static final String BUDGET_CHANGE_DETAIL = "budget_change_detail__c";
    public static final String DATA_OWN_DEPARTMENT = "data_own_department";

    public static final String OUT_TENANT_ID = "out_tenant_id";

    public static final String ENTER_INTO_ACCOUNT = "enter_into_account";

    public static final String SELF_DEFINE_OUTER_USER_ID = "outer_user_id_1";

}
