package com.facishare.crm.fmcg.common.apiname;

public abstract class TPMActivityProofAuditFields {

    private TPMActivityProofAuditFields(){}

    public static final String AUDIT_TIME = "audit_time";
    public static final String STORE_ID = "store_id";
    public static final String DEALER_ID = "dealer_id";
    public static final String TOTAL = "total";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String PROOF_IMAGES = "proof_images";
    public static final String DEALER_ACTIVITY = "dealer_activity";
    public static final String AUDITOR = "auditor";
    public static final String INSPECTOR = "inspector";
    public static final String ACTIVITY_AGREEMENT_ID = "activity_agreement_id";
    public static final String AUDIT_STATUS = "audit_status";
    public static final String AUDIT_TOTAL = "audit_total";
    public static final String AUDIT_STATUS__PASS = "pass";
    public static final String AUDIT_STATUS__REJECT = "reject";
    public static final String DEALER_ACTIVITY_COST_ID = "dealer_activity_cost_id";
    public static final String VISIT_ID = "visit_id";
    public static final String ACTION_ID = "action_id";
    public static final String ACTIVITY_PROOF_ID = "activity_proof_id";
    public static final String OPINION = "opinion";
    public static final String CREATE_TIME = "create_time";
    public static final String RANDOM_AUDIT_STATUS = "random_audit_status";
    public static final String RANDOM_AUDIT_STATUS__UNCHECKED = "unchecked";
    public static final String RANDOM_AUDIT_STATUS__CHECKED = "checked";
    public static final String AUDIT_IMAGES = "audit_images";
    public static final String STORE_WRITE_OFF_ID = "store_write_off_id";
    public static final String CALLBACK_FLAG_IS_RANDOM_AUDIT = "is_random_audit";
}