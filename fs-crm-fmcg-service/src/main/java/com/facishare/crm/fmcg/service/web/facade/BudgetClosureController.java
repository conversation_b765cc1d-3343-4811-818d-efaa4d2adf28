package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.Close;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetClosureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/13 11:33
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/BudgetClosure", produces = "application/json")
public class BudgetClosureController {

    @Resource
    private IBudgetClosureService budgetClosureService;

    @PostMapping(value = "Close")
    public Close.Result close(@RequestBody Close.Arg arg) {
        return budgetClosureService.close(arg);
    }
}
