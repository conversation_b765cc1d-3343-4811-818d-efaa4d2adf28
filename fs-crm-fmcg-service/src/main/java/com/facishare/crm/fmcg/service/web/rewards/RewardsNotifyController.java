package com.facishare.crm.fmcg.service.web.rewards;

import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotify;
import com.facishare.crm.fmcg.tpm.reward.outernotify.IRewardsNotifyService;
import com.fxiaoke.api.IdGenerator;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/{environment}/v1/3rd")
public class RewardsNotifyController {

    @Resource
    private IRewardsNotifyService rewardsNotifyService;

    @PostMapping("/rewards/notify")
    public RewardNotify.Result rewardsNotify(
            @PathVariable String environment,
            @RequestHeader("Authorization") String auth,
            @RequestBody RewardNotify.Arg arg) {
        log.info("reward notify arg: {}", arg.getData().toJSONString());

        switch (arg.getMessageId()) {
            case "success":
                return RewardNotify.Result.success(environment + ".MOCK_SUCCESS_ID");
            case "authorization_failed":
                return RewardNotify.Result.fail(401_001, "authorization failed");
            case "duplicate_message":
                return RewardNotify.Result.fail(403_001, "duplicate message");
            case "unknown_exception":
                return RewardNotify.Result.fail(500_001, "unknown exception");
            case "business_exception":
                return RewardNotify.Result.fail(500_002, "business exception");
            default: {
                return rewardsNotifyService.rewardsNotify(environment, auth, arg);
            }
        }
    }
}