package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.SalesOrderUpdateField;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.ISalesOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2022/6/2 16:57
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/salesOrder", produces = "application/json")
public class SalesOrderController {

    @Resource
    private ISalesOrderService salesOrderService;

    @PostMapping(value = "updateField")
    public SalesOrderUpdateField.Result updateField(@RequestBody SalesOrderUpdateField.Arg arg) {
        return salesOrderService.updateField(arg);
    }

}