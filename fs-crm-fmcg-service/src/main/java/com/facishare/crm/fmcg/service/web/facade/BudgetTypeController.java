package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IBudgetTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/7/13 11:33
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/BudgetType", produces = "application/json")
public class BudgetTypeController {

    @Resource
    private IBudgetTypeService budgetTypeService;

    @PostMapping(value = "Add")
    public AddBudgetType.Result add(@RequestBody AddBudgetType.Arg arg) {
        return budgetTypeService.add(arg);
    }

    @PostMapping(value = "SetStatus")
    public SetBudgetTypeStatus.Result setStatus(@RequestBody SetBudgetTypeStatus.Arg arg) {
        return budgetTypeService.setStatus(arg);
    }

    @PostMapping(value = "Edit")
    public EditBudgetType.Result edit(@RequestBody EditBudgetType.Arg arg) {
        return budgetTypeService.edit(arg);
    }

    @PostMapping(value = "Get")
    public GetBudgetType.Result get(@RequestBody GetBudgetType.Arg arg) {
        return budgetTypeService.get(arg);
    }

    @PostMapping(value = "Delete")
    public DeleteBudgetType.Result delete(@RequestBody DeleteBudgetType.Arg arg) {
        return budgetTypeService.delete(arg);
    }

    @PostMapping(value = "ForceDelete")
    public ForceDeleteBudgetType.Result forceDelete(@RequestBody ForceDeleteBudgetType.Arg arg) {
        return budgetTypeService.forceDelete(arg);
    }

    @PostMapping(value = "List")
    public ListBudgetType.Result list(@RequestBody ListBudgetType.Arg arg) {
        return budgetTypeService.list(arg);
    }

    @PostMapping(value = "MaximumDepartmentLevel")
    public MaximumDepartmentLevel.Result maximumDepartmentLevel(@RequestBody MaximumDepartmentLevel.Arg arg) {
        return budgetTypeService.maximumDepartmentLevel(arg);
    }
}
