package com.facishare.crm.fmcg.service.web.inner;


import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.tpm.web.contract.QueryActivityTypeByModelAndRule;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityTypeService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/TPM/ActivityType", produces = "application/json")
public class InnerActivityTypeController {

    @Resource
    private IActivityTypeService activityTypeService;


    @PostMapping(value = "queryByModelAndRule")
    public InnerApiResult<QueryActivityTypeByModelAndRule.Result> queryByModelAndRule(@RequestBody QueryActivityTypeByModelAndRule.Arg arg) {
        return InnerApiResult.apply(activityTypeService::queryByModelAndRule, arg);
    }

}
