package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.model.TPMPreDisplayReport;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMProofPeriodTime;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/DisplayReport", produces = "application/json")
public class TPMDisplayReportController {

    @Resource
    private ITPMDisplayReportService tpmDisplayReportService;

    @PostMapping(value = "/validateDisplayReport")
    public void validateDisplayReport(@RequestBody BaseObjectSaveAction.Arg arg) {
        tpmDisplayReportService.validateDisplayReport(arg);
    }

    @PostMapping(value = "/addProofPeriodTime")
    public void addProofPeriodTime(@RequestParam("userId") String userId,
                                   @RequestBody BaseObjectSaveAction.Result result) {
        tpmDisplayReportService.addProofPeriodTime(userId, result);
    }

    @PostMapping(value = "/editProofPeriodTime")
    public void editProofPeriodTime(@RequestParam("userId") String userId,
                                    @RequestBody BaseObjectSaveAction.Result result) {
        tpmDisplayReportService.editProofPeriodTime(userId, result);
    }

    @PostMapping(value = "/queryAllProofPeriodTimeData")
    public TPMProofPeriodTime.Result queryAllProofPeriodTimeData(@RequestBody TPMProofPeriodTime.Arg arg) {
        return tpmDisplayReportService.queryAllProofPeriodTimeData(arg);
    }

    @PostMapping(value = "/queryRangeProofPeriodTime")
    public TPMProofPeriodTime.ProofResult queryRangeProofPeriodTime(@RequestBody TPMProofPeriodTime.Arg arg) {
        return tpmDisplayReportService.queryRangeProofPeriodTime(arg);
    }

    @PostMapping(value = "/preDisplayReport")
    public TPMPreDisplayReport.Result preDisplayReport(@RequestBody TPMPreDisplayReport.Arg arg) {
        return tpmDisplayReportService.preDisplayReport(arg);
    }


    @PostMapping(value = "/testAsyncProcessProofDisplayImgAi")
    public String testAsyncProcessProofDisplayImgAi(@RequestParam String activityTypeId, @RequestParam String proofId) {
        tpmDisplayReportService.testAsyncProcessProofDisplayImgAi(activityTypeId, proofId);
        return "success";
    }
}