package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/6 16:21
 */
@Data
@ToString
public class ActivityReportDataVO implements Serializable {

    private String id;

    private String name;

    @JSONField(name = "template_name")
    @JsonProperty(value = "template_name")
    @SerializedName("template_name")
    private String templateName;

    @JSONField(name = "report_data")
    @JsonProperty(value = "report_data")
    @SerializedName("report_data")
    private List<ActivityReportDatumVO> reportData;
}
