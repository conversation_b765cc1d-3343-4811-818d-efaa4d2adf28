package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityDisplayImgVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

/**
 * 活动展示图片持久化对象
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(value = "activity_display_img", noClassnameStored = true)
@Indexes({
        @Index(fields = {
                @Field(MongoPO.F_TENANT_ID),
                @Field(MongoPO.F_UNIQUE_ID)
        }, options = @IndexOptions(unique = true)),
        @Index(fields = {
                @Field(MongoPO.F_TENANT_ID),
                @Field(ActivityDisplayImgPO.F_ACTIVITY_DISPLAY_IMG_ID)
        }),
        @Index(fields = {
                @Field(MongoPO.F_TENANT_ID),
                @Field(ActivityDisplayImgPO.F_PROOF_ID)
        }),
        @Index(fields = {
                @Field(MongoPO.F_TENANT_ID),
                @Field(ActivityDisplayImgPO.F_VISIT_ID)
        })
})
@SuppressWarnings("Duplicates")
public class ActivityDisplayImgPO extends MongoPO {

    public static final String F_ACTIVITY_DISPLAY_IMG_ID = "activity_display_img_id";
    public static final String F_N_PATH = "n_path";
    public static final String F_PROOF_ID = "proof_id";
    public static final String F_VISIT_ID = "visit_id";
    public static final String F_STATUS = "status";
    public static final String F_ERROR_MESSAGE = "error_message";

    @Property(F_N_PATH)
    private String nPath;

    @Property(F_PROOF_ID)
    private String proofId;

    @Property(F_VISIT_ID)
    private String visitId;

    @Property(F_ACTIVITY_DISPLAY_IMG_ID)
    private String activityDisplayImgId;

    @Property(F_STATUS)
    private String status = StatusType.NORMAL.value();

    @Property(F_ERROR_MESSAGE)
    private String errorMessage;

    public static ActivityDisplayImgPO fromVO(ActivityDisplayImgVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityDisplayImgPO po = new ActivityDisplayImgPO();
        if (vo.getId() != null) {
            po.setUniqueId(vo.getId());
        }
        po.setNPath(vo.getNPath());
        po.setProofId(vo.getProofId());
        po.setVisitId(vo.getVisitId());
        po.setActivityDisplayImgId(vo.getActivityDisplayImgId());
        po.setStatus(vo.getStatus());
        po.setTenantId(vo.getTenantId());
        po.setErrorMessage(vo.getErrorMessage());
        return po;
    }
} 