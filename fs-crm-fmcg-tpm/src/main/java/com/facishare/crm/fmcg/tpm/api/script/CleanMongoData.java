package com.facishare.crm.fmcg.tpm.api.script;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 下午5:32
 */
public interface CleanMongoData {

    @Data
    @ToString
    class Arg implements Serializable {
        private String tenantId;
        private String db;
        private Integer pageSize ;
        private Integer maxSize;
    }


    @Data
    @ToString
    class Result implements Serializable {

    }
}
