package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityUnifiedCaseFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityTemplateMapper;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityPlanReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeReportDatumVO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityPlanTemplateManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class ActivityPlanTemplateManager implements IActivityPlanTemplateManager {

    @Resource
    private ActivityTemplateMapper activityTemplateMapper;

    @Override
    public ActivityTypeReportDatumVO loadActivityTypeReport(String tenantId, String activityTypeId, ActivityNodeEntity node, Map<String, String> displayNameMap, ActivityTypePO activityType) {
        Map<String, Long> activityPlanReportData = activityTemplateMapper.setTenantId(tenantId).queryActivityUnifiedCaseTypeStatisticsData(tenantId, activityTypeId)
                .stream()
                .collect(Collectors.toMap(ActivityTypeStatisticsDatumPO::getStatus, ActivityTypeStatisticsDatumPO::getCount));

        return ActivityTypeReportDatumVO.builder()
                .templateId(node.getTemplateId())
                .type(node.getType())
                .nodeDisplayName(node.getName())
                .objectApiName(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ)
                .objectDisplayName(displayNameMap.get(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ))
                .count(activityPlanReportData.getOrDefault(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS, 0L))
                .totalCount(activityPlanReportData.values().stream().mapToLong(v -> v).sum())
                .scheduleCount(activityPlanReportData.getOrDefault(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__SCHEDULE, 0L))
                .inProgressCount(activityPlanReportData.getOrDefault(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__IN_PROGRESS, 0L))
                .endedCount(activityPlanReportData.getOrDefault(TPMActivityUnifiedCaseFields.ACTIVITY_STATUS__END, 0L))
                .build();
    }

    @Override
    public ActivityPlanReportDatumVO loadActivityPlanReport(String tenantId, String activityPlanId) {
        return ActivityPlanReportDatumVO.builder()
                .count(0)
                .build();
    }
}
