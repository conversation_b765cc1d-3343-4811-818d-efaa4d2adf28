package com.facishare.crm.fmcg.tpm.dao.mongo.po;


import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:13
 */
public enum ProofFrequencyType {

    ACTIVITY("活动周期", "activity", null),
    DAY("天", "day", null),
    WEEK("周", "week", null),
    MONTH("月", "month", null),
    TWO_MONTH("两月", "two_month", null);

    private static final Map<String, ProofFrequencyType> allTypeMap = Arrays.stream(ProofFrequencyType.values()).collect(Collectors.toMap(ProofFrequencyType::value, v -> v, (oldOne, newOne) -> oldOne));

    ProofFrequencyType(String label, String value, String i18nKey) {
        this.label = label;
        this.value = value;
        this.i18nKey = i18nKey;
    }

    private final String value;

    private final String label;

    private final String i18nKey;

    public String value() {
        return this.value;
    }

    public String label() {
        return this.label;
    }

    public String i18nKey() {
        return this.i18nKey;
    }

    public static boolean contains(String type) {
        return allTypeMap.containsKey(type);
    }

    public static ProofFrequencyType of(String value) {
        return allTypeMap.get(value);
    }

}
