package com.facishare.crm.fmcg.tpm.web.manager.abstraction;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityDisplayImgVO;

import java.util.List;

/**
 * 活动展示图片管理接口
 */
public interface IActivityDisplayImgManager {

    /**
     * 判断是否可以删除
     * 
     * @param tenantId 租户ID
     * @param id 活动展示图片ID
     * @return 是否可以删除
     */
    boolean deleteAble(String tenantId, String id);

    /**
     * 判断是否可以编辑
     * 
     * @param tenantId 租户ID
     * @param id 活动展示图片ID
     * @return 是否可以编辑
     */
    boolean editAble(String tenantId, String id);

    /**
     * 根据ID查找活动展示图片
     * 
     * @param tenantId 租户ID
     * @param id 活动展示图片ID
     * @return 活动展示图片
     */
    ActivityDisplayImgPO find(String tenantId, String id);

    /**
     * 更新活动展示图片
     * 
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param id 活动展示图片ID
     * @param data 活动展示图片数据
     */
    void update(String tenantId, Integer employeeId, String id, ActivityDisplayImgPO data);

    /**
     * 根据多个ID查找活动展示图片
     * 
     * @param tenantId 租户ID
     * @param ids 活动展示图片ID列表
     * @return 活动展示图片列表
     */
    List<ActivityDisplayImgPO> findByIds(String tenantId, List<String> ids);

    /**
     * 根据举证ID查找活动展示图片
     * 
     * @param tenantId 租户ID
     * @param proofId 举证ID
     * @return 活动展示图片列表
     */
    List<ActivityDisplayImgPO> findByProofId(String tenantId, String proofId);

    /**
     * 根据拜访ID查找活动展示图片
     * 
     * @param tenantId 租户ID
     * @param visitId 拜访ID
     * @return 活动展示图片列表
     */
    List<ActivityDisplayImgPO> findByVisitId(String tenantId, String visitId);

    /**
     * 添加活动展示图片
     * 
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param data 活动展示图片数据
     * @return 活动展示图片ID
     */
    String add(String tenantId, Integer employeeId, ActivityDisplayImgPO data);

    /**
     * 分页查询活动展示图片
     * 
     * @param tenantId 租户ID
     * @param keyword 搜索关键字
     * @param limit 限制条数
     * @param offset 偏移量
     * @return 活动展示图片列表
     */
    List<ActivityDisplayImgVO> list(String tenantId, String keyword, int limit, int offset);

    /**
     * 统计活动展示图片数量
     * 
     * @param tenantId 租户ID
     * @param keyword 搜索关键字
     * @return 数量
     */
    long count(String tenantId, String keyword);

    /**
     * 设置活动展示图片状态
     * 
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param id 活动展示图片ID
     * @param status 状态
     */
    void setStatus(String tenantId, Integer employeeId, String id, String status);

    /**
     * 删除活动展示图片
     * 
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param id 活动展示图片ID
     */
    void delete(String tenantId, Integer employeeId, String id);
} 