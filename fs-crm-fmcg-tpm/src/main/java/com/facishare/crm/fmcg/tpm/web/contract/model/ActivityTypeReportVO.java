package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/16 14:47
 */
@Data
@ToString
@Builder
public class ActivityTypeReportVO implements Serializable {

    @JSONField(name = "activity_plan_report")
    @JsonProperty(value = "activity_plan_report")
    @SerializedName("activity_plan_report")
    private ActivityPlanReportVO activityPlanReport;

    @JSONField(name = "activity_agreement_report")
    @JsonProperty(value = "activity_agreement_report")
    @SerializedName("activity_agreement_report")
    private ActivityAgreementReportVO activityAgreementReport;

    @JSONField(name = "activity_proof_report")
    @JsonProperty(value = "activity_proof_report")
    @SerializedName("activity_proof_report")
    private ActivityProofReportVO activityProofReport;

    @JSONField(name = "activity_proof_audit_report")
    @JsonProperty(value = "activity_proof_audit_report")
    @SerializedName("activity_proof_audit_report")
    private ActivityProofAuditReportVO activityProofAuditReport;

    @JSONField(name = "activity_write_off_report")
    @JsonProperty(value = "activity_write_off_report")
    @SerializedName("activity_write_off_report")
    private ActivityWriteOffReportVO activityWriteOffReport;
}
