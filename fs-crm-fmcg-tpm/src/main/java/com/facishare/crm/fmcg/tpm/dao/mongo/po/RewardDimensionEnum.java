package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.I18NUtils;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/9/14 17:03
 */
public enum RewardDimensionEnum {

    BASE("0", "本企业", Type.MASTER.code, 0, "fmcg.reward_rule.dimension.base"),
    FIRST_LEVEL("1", "一级下游企业", Type.DOWNSTREAM.code, 1, "fmcg.reward_rule.dimension.first_level"),
    SECOND_LEVEL("2", "二级下游企业", Type.DOWNSTREAM.code, 2, "fmcg.reward_rule.dimension.second_level"),
    THIRD_LEVEL("3", "三级下游企业", Type.DOWNSTREAM.code, 3, "fmcg.reward_rule.dimension.third_level"),
    FOURTH_LEVEL("4", "四级下游企业", Type.DOWNSTREAM.code, 4, "fmcg.reward_rule.dimension.fourth_level"),
    FIFTH_LEVEL("5", "五级下游企业", Type.DOWNSTREAM.code, 5, "fmcg.reward_rule.dimension.fifth_level"),
    STORE("store", "门店", Type.STORE.code, 9, "fmcg.reward_rule.dimension.store"),
    CONSUMER("consumer", "消费者", Type.CONSUMER.code, 15, "fmcg.reward_rule.dimension.consumer");

    RewardDimensionEnum(String code, String description, String type, int order, String i18nKey) {
        this.code = code;
        this.description = description;
        this.type = type;
        this.order = order;
        this.i18nKey = i18nKey;
    }

    public static final Map<String, RewardDimensionEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(RewardDimensionEnum::code, e -> e));

    private String code;

    private String description;

    private String type;

    private int order;

    private String i18nKey;

    public String code() {
        return this.code;
    }

    public String description() {
        return this.description;
    }

    public String type() {
        return this.type;
    }

    public int order() {
        return this.order;
    }

    public String i18nDescription() {
        String text;
        if (this.i18nKey == null) {
            text = this.description;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.description;
            }
        }
        return text;
    }

    public static RewardDimensionEnum get(String code) {
        return CODE_MAP.get(code);
    }


    public enum Type {
        MASTER("master"),
        DOWNSTREAM("downstream"),
        STORE("store"),
        CONSUMER("consumer");

        Type(String code) {
            this.code = code;
        }

        private String code;

        public String code() {
            return code;
        }

    }

}
