package com.facishare.crm.fmcg.tpm.web.contract.poc;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.ToString;

import java.util.List;

public interface POCBase {
    @Data
    @ToString
    class Arg {
        private IObjectData masterObj;
        private List<IObjectData> details;
    }

    @Data
    @ToString
    class Result {
        private IObjectData masterObj;
        private List<IObjectData> details;
    }

}
