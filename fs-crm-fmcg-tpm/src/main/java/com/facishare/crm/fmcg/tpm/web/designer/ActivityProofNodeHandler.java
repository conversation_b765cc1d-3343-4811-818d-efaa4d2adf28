package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ProofCalculateType;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ProofFrequencyType;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/30 12:14
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@Component("activityProofNodeHandler")
public class ActivityProofNodeHandler extends BaseSystemNodeHandler {

    @Resource
    private ActivityTypeManager activityTypeManager;
    @Override
    public String getNodeType() {
        return NodeType.PROOF.value();
    }

    @Override
    public void validation(String tenantId, int index, List<ActivityNodeVO> nodes, IActivityType activityType) {
        super.validation(tenantId, index, nodes, activityType);
        ActivityNodeVO node = nodes.get(index);

        frequencyConfigValidation(node.getActivityProofConfig());
        costCalculateConfigValidation(node.getActivityProofConfig().getCostCalculateConfig());
        aiConfigValidation(tenantId,node.getActivityProofConfig().getAiConfig());
    }

    private void frequencyConfigValidation(ActivityProofConfigVO config) {
        if (Objects.isNull(config)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_0));
        }
        if (Objects.isNull(config.getFrequencyConfig())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_1));
        }
        if (!validationLimitSpans(config.getFrequencyConfig().getLimitDays())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_2));
        }
        if (!validationLimitSpans(config.getFrequencyConfig().getLimitHours())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_3));
        }
        if (!ProofFrequencyType.contains(config.getFrequencyConfig().getFrequencyType())) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_4), I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_5)));
        }
        if (Strings.isNullOrEmpty(config.getFrequencyConfig().getFrequencyLimit())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_6));
        }
    }

    private void costCalculateConfigValidation(ActivityProofCostCalculateConfigVO costCalculateConfig) {
        if (!ProofCalculateType.contains(costCalculateConfig.getCalculateType())) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_7));
        }
        if (ProofCalculateType.BY_SESSIONS.value().equals(costCalculateConfig.getCalculateType())) {
            if (Strings.isNullOrEmpty(costCalculateConfig.getRatio())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_10));
            }
        }
    }

    private void aiConfigValidation(String tenantId, ActivityProofAiConfigVO aiConfig) {
        if (Objects.nonNull(aiConfig) && aiConfig.getEnableAiDisplayRecognition()) {
            boolean enableAi = activityTypeManager.getEnableAi(tenantId);
            if (!enableAi) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_8));
            }
            //TODO:JS ai配置校验，规则字段映射关系校验，需确认是否校验
            // displayRecognitionModel 和 adaptationRule 不能为空
            if (Strings.isNullOrEmpty(aiConfig.getDisplayRecognitionModel())) {
                throw new ValidateException("Display recognition model is not empty!");
            }
            if (Strings.isNullOrEmpty(aiConfig.getAdaptationRule())) {
                throw new ValidateException("Adaptation rules is not empty!");
            }
        }
    }

    private boolean validationLimitSpans(List<LimitSpanVO> limits) {
        if (CollectionUtils.isEmpty(limits)) {
            return true;
        }
        List<LimitSpanVO> sortLimitSpans = limits.stream().sorted(Comparator.comparingInt(a -> Integer.parseInt(a.getFrom()))).collect(Collectors.toList());
        for (int i = 0; i < sortLimitSpans.size(); i++) {
            LimitSpanVO limitSpanVO = sortLimitSpans.get(i);
            try {
                if (Integer.parseInt(limitSpanVO.getFrom()) > Integer.parseInt(limitSpanVO.getTo())) {
                    return false;
                }
                if (i > 0) {
                    LimitSpanVO beforeLimitSpanVO = sortLimitSpans.get(i - 1);
                    int beforeTo = Integer.parseInt(beforeLimitSpanVO.getTo());
                    int currentFrom = Integer.parseInt(limitSpanVO.getFrom());
                    if (beforeTo >= currentFrom) {
                        return false;
                    }
                }
            } catch (NumberFormatException ex) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_NODE_HANDLER_11));
            }
        }
        return true;
    }

    @Override
    protected List<String> queryPostSystemNodeTypes() {
        return Lists.newArrayList(
                NodeType.PROOF.value(),
                NodeType.AUDIT.value(),
                NodeType.STORE_WRITE_OFF.value(),
                NodeType.COST_ASSIGN.value(),
                NodeType.WRITE_OFF.value()
        );
    }
}
