package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 16:30
 */
@Data
@ToString
public class CommonVO implements Serializable {

    @JSONField(name = "_id")
    @JsonProperty(value = "_id")
    @SerializedName("_id")
    private String id;

    @JSONField(name = "tenant_id")
    @JsonProperty(value = "tenant_id")
    @SerializedName("tenant_id")
    private String tenantId;

    @JSONField(name = "creator")
    @JsonProperty(value = "creator")
    @SerializedName("creator")
    private Integer creator;

    @JSONField(name = "create_time")
    @JsonProperty(value = "create_time")
    @SerializedName("create_time")
    private Long createTime;

    @J<PERSON><PERSON>ield(name = "last_updater")
    @JsonProperty(value = "last_updater")
    @SerializedName("last_updater")
    private Integer lastUpdater;

    @JSONField(name = "last_update_time")
    @JsonProperty(value = "last_update_time")
    @SerializedName("last_update_time")
    private Long lastUpdateTime;

    @JSONField(name = "is_deleted")
    @JsonProperty(value = "is_deleted")
    @SerializedName("is_deleted")
    private boolean isDeleted;

    @JSONField(name = "delete_by")
    @JsonProperty(value = "delete_by")
    @SerializedName("delete_by")
    private Integer deleteBy;

    @JSONField(name = "delete_time")
    @JsonProperty(value = "delete_time")
    @SerializedName("delete_time")
    private Long deleteTime;
}
