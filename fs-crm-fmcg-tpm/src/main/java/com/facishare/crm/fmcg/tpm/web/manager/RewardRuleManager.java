package com.facishare.crm.fmcg.tpm.web.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberStatusFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityRewardDetailFields;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.api.rule.*;
import com.facishare.crm.fmcg.tpm.business.abstraction.IFmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.RewardTransferAccountDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NEnums;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fmcg.framework.http.FMCGSnProxy;
import com.fmcg.framework.http.contract.sales.QuerySnAction;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2023/9/15 15:22
 */
@Slf4j
@Component
public class RewardRuleManager implements IRewardRuleManager {

    private static final Pattern REMARK_PATTERN = Pattern.compile("['\"@%\\(\\)\\-;#+/<>￥\\\\\\.]+");

    private static Map<String, List<String>> ALLOW_TRIGGER_MAP = new HashMap<>();

    @Resource
    private ServiceFacade serviceFacade;


    @Resource
    private FMCGSnProxy fmcgSnProxy;


    @Resource
    private IFmcgSerialNumberService fmcgSerialNumberService;

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private RewardTransferAccountDAO rewardTransferAccountDAO;

    private static Map<String, List<String>> TENANT_MODULES_MAP = new HashMap<>();

    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            loadTenantModules(config);
            loadTriggerRewardAction(config);
        });
    }

    public static void loadTenantModules(IConfig config) {
        String json = config.get("reward_tenant_modules_map");
        if (!Strings.isNullOrEmpty(json)) {
            TENANT_MODULES_MAP = JSON.parseObject(json, new TypeReference<Map<String, List<String>>>() {
            });
        }
        if (!TENANT_MODULES_MAP.containsKey("default")) {
            TENANT_MODULES_MAP.put("default", Lists.newArrayList("STORE_SALES", "STORE_SIGN", "SALES_OUT_OF_WAREHOUSE"));
        }
    }

    public static void loadTriggerRewardAction(IConfig config) {
        String json = config.get("allow_trigger_reward_action_map");
        if (!Strings.isNullOrEmpty(json)) {
            ALLOW_TRIGGER_MAP = JSON.parseObject(json, new TypeReference<Map<String, List<String>>>() {
            });
        }
        if (!ALLOW_TRIGGER_MAP.containsKey("default")) {
            ALLOW_TRIGGER_MAP.put("default", Lists.newArrayList("STORE_SALES", "STORE_SIGN", "SALES_OUT_OF_WAREHOUSE"));
        }
    }


    @Override
    public RewardDescribeDTO getRewardDescribe(String tenantId, GetRuleDescribe.Arg arg) {
        RewardDescribeDTO rewardDescribeDTO = new RewardDescribeDTO();
        rewardDescribeDTO.setTenantId(tenantId);
        rewardDescribeDTO.setMaxRewardLevel(Integer.parseInt(ConfigFactory.getConfig("fs-fmcg-tpm-config").get("maxRewardLevel", "5")));
        Map<String, RewardFieldDescribeDTO> fieldMap = new HashMap<>();
        rewardDescribeDTO.setFieldDescribeMap(fieldMap);
        rewardDescribeDTO.setModules(queryModules(tenantId));

        fieldMap.put(RewardNodeEntity.F_REWARD_DIMENSION, formSelectOneField(RewardNodeEntity.F_REWARD_DIMENSION, Arrays.stream(RewardDimensionEnum.values())
                .filter(dimension -> {
                    if (ScanCodeActionConstants.STOCK_UP_REWARD_TEMPLATE_ID.equals(arg.getTemplateId())) {
                        return dimension.order() <= RewardDimensionEnum.STORE.order();
                    }
                    return true;
                }).map(v -> SimpleDTO.builder().value(v.code()).name(v.i18nDescription()).order(v.order()).type(v.type()).build()).collect(Collectors.toList())));
        fieldMap.put(RewardNodeEntity.F_REWARD_TYPE, formSelectOneField(RewardNodeEntity.F_REWARD_TYPE, Arrays.stream(RewardTypeEnum.values()).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        fieldMap.put(RewardNodeEntity.F_REWARD_ACTION, formSelectOneField(RewardNodeEntity.F_REWARD_ACTION, getRewardActionList(tenantId)));
        fieldMap.put(RewardNodeEntity.F_REWARD_TARGET, formSelectOneField(RewardNodeEntity.F_REWARD_TARGET, Arrays.stream(RewardTargetEnum.values()).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        //屏蔽返利单
        fieldMap.put(RewardStrategyEntity.F_REWARD_METHOD, formSelectOneField(RewardStrategyEntity.F_REWARD_METHOD, Arrays.stream(RewardMethodEnum.values()).filter(method -> !method.equals(RewardMethodEnum.REBATE)).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        fieldMap.put(RewardPaymentEntity.F_PAY_ACCOUNT + "." + RewardAccountEntity.F_ACCOUNT_TYPE, formSelectOneField(RewardAccountEntity.F_ACCOUNT_TYPE, Arrays.stream(PayAccountTypeEnum.values()).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        fieldMap.put(RewardPaymentEntity.F_RECEIVE_ACCOUNT + "." + RewardAccountEntity.F_ACCOUNT_TYPE, formSelectOneField(RewardAccountEntity.F_ACCOUNT_TYPE, Arrays.stream(ReceiveAccountTypeEnum.values()).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        fieldMap.put(RewardPaymentEntity.F_PAY_ACCOUNT + "." + RewardAccountEntity.F_ACCOUNT_DIMENSION, formSelectOneField(RewardAccountEntity.F_ACCOUNT_DIMENSION, Arrays.stream(RewardDimensionEnum.values()).filter(v -> "downstream".equals(v.type())).map(v -> SimpleDTO.builder().value(v.code()).name(v.i18nDescription()).order(v.order()).type(v.type()).build()).collect(Collectors.toList())));
        fieldMap.put(RewardPaymentEntity.F_PAY_ACCOUNT + "." + RewardAccountEntity.F_DEPARTMENT_LEVEL, formDepartmentLevelField(tenantId, RewardAccountEntity.F_DEPARTMENT_LEVEL, Arrays.stream(DepartmentLevelEnum.values()).map(v -> new SimpleDTO(v.i18nDescribe(), v.code())).collect(Collectors.toList())));
        fieldMap.put(ActivityRewardRulePO.F_TRIGGER_TYPE, fieldMap.get(RewardNodeEntity.F_REWARD_ACTION));
        //屏蔽返现
        fieldMap.put(RewardStrategyEntity.F_REWARD_METHOD_TYPE, formSelectOneField(RewardStrategyEntity.F_REWARD_METHOD_TYPE, Arrays.stream(RewardMethodTypeEnum.values()).filter(method -> !method.equals(RewardMethodTypeEnum.RETURN_BY_MONEY)).map(v -> SimpleDTO.builder().value(v.code()).name(v.i18nDescribe()).belongTo(Lists.newArrayList(v.rewardMethod())).build()).collect(Collectors.toList())));
        fieldMap.put(RewardStrategyEntity.F_DISTRIBUTE_METHOD, formSelectOneField(RewardStrategyEntity.F_DISTRIBUTE_METHOD, Arrays.stream(RewardDistributeMethodEnum.values()).map(v -> SimpleDTO.builder().value(v.code()).name(v.i18nDescribe()).belongTo(Lists.newArrayList(v.rewardMethod())).build()).collect(Collectors.toList())));
        fieldMap.put(RewardStrategyEntity.F_REWARD_GET_METHOD, formSelectManyField(RewardStrategyEntity.F_REWARD_GET_METHOD, Arrays.stream(RewardGetMethodEnum.values()).map(v -> SimpleDTO.builder().value(v.code()).name(v.i18nDescribe()).build()).collect(Collectors.toList())));
        if (rewardDescribeDTO.getModules().contains(ScanCodeActionConstants.REWARD_FIXED_ACCOUNT_MODULE)) {
            fieldMap.put(RewardPaymentEntity.F_PAY_ACCOUNT + "." + RewardAccountEntity.F_FIXED_ACCOUNT_ID, formSelectOneField(RewardAccountEntity.F_FIXED_ACCOUNT_ID, formFixedAccountList(tenantId)));
        }
        fieldMap.put(RewardStrategyEntity.F_EXCEPTION_STRATEGY, formSelectOneField(RewardStrategyEntity.F_EXCEPTION_STRATEGY, Arrays.stream(ExceptionStrategyEnum.values()).map(v -> new SimpleDTO(v.i18nDescription(), v.code())).collect(Collectors.toList())));
        fieldMap.put(RewardStrategyEntity.F_EXCEPTION_TYPES, formSelectManyField(RewardStrategyEntity.F_EXCEPTION_TYPES, getExceptionTypeList(tenantId)));
        fieldMap.put(RewardStrategyEntity.F_REBATE_USE_TYPE, formSelectOneField(RewardStrategyEntity.F_REBATE_USE_TYPE, Arrays.stream(RebateUseTypeEnum.values()).map(v -> SimpleDTO.builder().value(v.code()).belongTo(Lists.newArrayList(v.rebateType())).name(v.i18nDescribe()).build()).collect(Collectors.toList())));

        return rewardDescribeDTO;
    }

    private List<String> queryModules(String tenantId) {
        return new ArrayList<>(TENANT_MODULES_MAP.getOrDefault(tenantId, new ArrayList<>()));
    }

    private List<SimpleDTO> getExceptionTypeList(String tenantId) {
        List<SimpleDTO> exceptionTypes = new ArrayList<>();
        DescribeResult describeResult = serviceFacade.findDescribeAndLayout(User.systemUser(tenantId), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, false, "");
        IObjectDescribe describe = describeResult.getObjectDescribe();
        if (describe.containsField(FMCGSerialNumberStatusFields.EXCEPTION_TYPE)) {
            IFieldDescribe field = describe.getFieldDescribe(FMCGSerialNumberStatusFields.EXCEPTION_TYPE);
            if (field instanceof SelectOneFieldDescribe) {
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) field;
                selectOneFieldDescribe.getSelectOptions().stream().filter(option -> !"other".equals(option.getValue())).forEach(option -> exceptionTypes.add(new SimpleDTO(option.getLabel(), option.getValue())));
            }
        }
        return exceptionTypes;
    }

    private List<SimpleDTO> formFixedAccountList(String tenantId) {
        List<SimpleDTO> simpleDTOList = new ArrayList<>();
        List<RewardTransferAccountPO> accounts = rewardTransferAccountDAO.queryAccountListByTenantId(tenantId);
        accounts.forEach(account -> simpleDTOList.add(SimpleDTO.builder().value(account.getCode()).name(account.getName()).type(account.getPlatform()).build()));
        simpleDTOList.sort(Comparator.comparing(SimpleDTO::getValue));
        return simpleDTOList;
    }

    @Override
    public void validateRule(RewardRuleDTO rewardRuleDTO) {
        //validate rule node
        if (CollectionUtils.isEmpty(rewardRuleDTO.getRewardDetails())) {
            throw new RewardFmcgException("10005", I18N.text(I18NKeys.REWARD_RULE_NODE_IS_EMPTY));
        }
        if (Strings.isNullOrEmpty(rewardRuleDTO.getTriggerType())) {
            throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_MANAGER_0));
        }
        //Map<String, String> actionChannelMap = getRewardActionList(rewardRuleDTO.getTenantId()).stream().collect(Collectors.toMap())
        RewardDimensionEnum rewardDimensionEnum = null;
        for (RewardDetailDTO rewardDetail : rewardRuleDTO.getRewardDetails()) {
            RewardDimensionEnum tempEnum = RewardDimensionEnum.get(rewardDetail.getRewardNode().getRewardDimension());
            if (rewardDimensionEnum == null) {
                rewardDimensionEnum = tempEnum;
            } else {
                if ((rewardRuleDTO.getRuleType().equals(ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID) || rewardRuleDTO.getRuleType().equals(ScanCodeActionConstants.CONSUMER_SCAN_INNER_CODE_ACTIVITY_TYPE_TEMPLATE_ID))
                        && tempEnum.order() < rewardDimensionEnum.order()) {
                    throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_node_level_judgement"));
                }
                rewardDimensionEnum = tempEnum;
            }
            RewardTargetEnum rewardTargetEnum = RewardTargetEnum.get(rewardDetail.getRewardNode().getRewardTarget());
            if (RewardTargetEnum.STORE_BOSS == rewardTargetEnum && rewardDimensionEnum != RewardDimensionEnum.STORE) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_2));
            }
            String rewardType = rewardDetail.getRewardNode().getRewardType();
            if (RewardTypeEnum.BY_DESIGNEE.code().equals(rewardType) && (rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.MASTER.code()) || rewardDimensionEnum.type().equals(RewardDimensionEnum.Type.DOWNSTREAM.code())) && RewardTargetEnum.ENTERPRISE_BOSS != rewardTargetEnum) {
                throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_solid_person_select_enterprise_boss"));
            }
            RewardPaymentDTO paymentDTO = rewardDetail.getRewardPayment();
            if (paymentDTO != null && paymentDTO.getPayAccount() != null) {
                if (PayAccountTypeEnum.DOWNSTREAM_ACCOUNT == PayAccountTypeEnum.get(paymentDTO.getPayAccount().getAccountType())
                        && RewardDimensionEnum.get(paymentDTO.getPayAccount().getAccountDimension()).order() > rewardDimensionEnum.order()) {
                    throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_account_level_must_higher_than_reward_level"));
                }
            }
            RewardStrategyDTO rewardStrategy = rewardDetail.getRewardStrategy();
            if (rewardStrategy != null && rewardStrategy.getRewardQuantity() != null && rewardDimensionEnum != RewardDimensionEnum.CONSUMER) {
                if (rewardStrategy.getRewardQuantity().doubleValue() <= 0) {
                    throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_amount_gt_0"));
                }
                rewardStrategy.setRewardQuantity(rewardStrategy.getRewardQuantity().setScale(2, RoundingMode.DOWN));
            }
            //激励策略校验
            validateStrategy(rewardDetail);
        }

        if (rewardRuleDTO.getRuleType().equals(ScanCodeActionConstants.SELF_DEFINE_REWARD_TEMPLATE_ID) && Strings.isNullOrEmpty(rewardRuleDTO.getTriggerDimension())) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RULE_MANAGER_0));
        }

        //大日期校验
        validateBigDate(rewardRuleDTO);

        //消费者扫码校验
        validateConsumerRedPacket(rewardRuleDTO);

        List<String> rewardNodeStr = rewardRuleDTO.getRewardDetails().stream().map(node -> {
            RewardNodeDTO rewardNode = node.getRewardNode();
            return rewardNode.getRewardDimension() +
                    "." + rewardNode.getRewardType() +
                    "." + (Objects.isNull(rewardNode.getRewardAction()) ? "" : rewardNode.getRewardAction()) +
                    "." + rewardNode.getRewardTarget();
        }).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(rewardNodeStr) && rewardNodeStr.size() < rewardRuleDTO.getRewardDetails().size()) {
            throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_node_repeat"));
        }
    }

    private void validateStrategy(RewardDetailDTO rewardDetail) {
        RewardStrategyDTO rewardStrategyDTO = rewardDetail.getRewardStrategy();
        if (rewardStrategyDTO == null) {
            return;
        }
        if (!Strings.isNullOrEmpty(rewardStrategyDTO.getRewardRemark())) {
            if (rewardStrategyDTO.getRewardRemark().length() > 40 || REMARK_PATTERN.matcher(rewardStrategyDTO.getRewardRemark()).find()) {
                throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_remark_limit_words"));
            }
        }
        if (RewardDistributeMethodEnum.WITHDRAW.code().equals(rewardStrategyDTO.getDistributeMethod())) {
            if (rewardStrategyDTO.getExpiredDays() != null && rewardStrategyDTO.getExpiredDays() <= 0) {
                throw new RewardFmcgException("10010", I18N.text("fmcg.crm.fmcg.tpm.ACTIVITY_reward_withdraw_day_gt_0"));
            }
            if (rewardDetail.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.CONSUMER.code())) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_7));
            }
        }
        if (RewardMethodTypeEnum.RANDOM_RED_PACKET.code().equals(rewardStrategyDTO.getRewardMethodType())) {
            if (CollectionUtils.isEmpty(rewardStrategyDTO.getRandomRewardLevels())) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_8));
            }
            for (int i = 0; i < rewardStrategyDTO.getRandomRewardLevels().size(); i++) {
                RandomRewardLevelDTO randomRewardLevelDTO = rewardStrategyDTO.getRandomRewardLevels().get(i);
                if (randomRewardLevelDTO.getRewardAmount() == null || randomRewardLevelDTO.getRewardAmount() <= 0) {
                    throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_9));
                }
                    /*
                    BigDecimal probabilitySum = new BigDecimal("0.00");
                    if (randomRewardLevelDTO.getRewardProbability() == null || randomRewardLevelDTO.getRewardProbability() <= 0) {
                        throw new RewardFmcgException(10010, I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_10));
                    }
                    probabilitySum = probabilitySum.add(new BigDecimal(randomRewardLevelDTO.getRewardProbability().toString()).setScale(2, RoundingMode.DOWN));*/
            }
                /*if (probabilitySum.compareTo(new BigDecimal("100")) != 0) {
                    throw new RewardFmcgException(10010, I18N.text(I18NKeys.REWARD_REWARD_RULE_MANAGER_11));
                }*/
        }
        if (ExceptionStrategyEnum.NO_REWARD.code().equals(rewardStrategyDTO.getExceptionStrategy())) {
            if (CollectionUtils.isEmpty(rewardStrategyDTO.getExceptionTypes())) {
                throw new RewardFmcgException("10010", I18N.text(I18NEnums.REWARD_EXCEPTION_TYPES_IS_NOT_EMPTY.getCode()));
            }
        } else {
            if (CollectionUtils.isNotEmpty(rewardStrategyDTO.getExceptionTypes())) {
                throw new RewardFmcgException("10010", I18N.text(I18NEnums.REWARD_EXCEPTION_TYPES_IS_EMPTY.getCode()));
            }
        }
        RewardTargetEnum rewardTargetEnum = RewardTargetEnum.get(rewardDetail.getRewardNode().getRewardTarget());
        if (RewardMethodEnum.REBATE.code().equals(rewardStrategyDTO.getRewardMethod())) {
            if (RewardTargetEnum.STORE_BOSS != rewardTargetEnum) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_MANAGER_ONLY_SUPPORT_STORE_LEVEL));
            }
            if (rewardStrategyDTO.getExpiredDays() == null || rewardStrategyDTO.getExpiredDays() <= 0) {
                throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD_MANAGER_EXPIRED_TIME_MUST_BIGGER_THAN_ZERO));
            }
        }

    }

    private void validateConsumerRedPacket(RewardRuleDTO rewardRuleDTO) {
        RewardDetailDTO rewardDetailDTO = rewardRuleDTO.getRewardDetails().get(rewardRuleDTO.getRewardDetails().size() - 1);
        RewardDimensionEnum lastRewardDimension = RewardDimensionEnum.get(rewardDetailDTO.getRewardNode().getRewardDimension());
        String actionCode = fmcgSerialNumberService.getActionUniqueIdByActionId(rewardRuleDTO.getTenantId(), rewardDetailDTO.getRewardNode().getRewardAction());


        if (ScanCodeActionConstants.CONSUMER_SCAN_INNER_CODE_ACTIVITY_TYPE_TEMPLATE_ID.equals(rewardRuleDTO.getRuleType())
                || ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE.equals(actionCode)
                || ScanCodeActionConstants.OUTER_CONSUMER_GET_RED_PACKAGE.equals(actionCode)) {

            if (RewardDimensionEnum.CONSUMER != lastRewardDimension) {
                throw new ValidateException(I18N.text(I18NEnums.LAST_REWARD_NODE_MUST_BE_CONSUMER.getCode()));
            }

            long consumerCount = rewardRuleDTO.getRewardDetails().stream().map(v -> v.getRewardNode().getRewardDimension()).filter(v -> v.equals(RewardDimensionEnum.CONSUMER.code())).count();
            if (consumerCount > 1) {
                throw new ValidateException(I18N.text(I18NEnums.JUST_ONLY_ONE_CONSUMER_NODE.getCode()));
            }

            if (ScanCodeActionConstants.OUTER_CONSUMER_GET_RED_PACKAGE.equals(actionCode)) {
                validateOuterConsumerNode(rewardDetailDTO);
            } else {
                validateGetRedPacketNode(rewardRuleDTO, rewardDetailDTO);
            }
        }
    }

    private void validateOuterConsumerNode(RewardDetailDTO rewardDetailDTO) {
        if (Objects.isNull(rewardDetailDTO.getOuterConsumerRewardStrategy())) {
            throw new ValidateException("outer consumer reward strategy can not be null");
        }
        if (CollectionUtils.isEmpty(rewardDetailDTO.getOuterConsumerRewardStrategy().getOuterConsumerRewardRules())) {
            throw new ValidateException("outer consumer reward rule can not be null or empty");
        }
    }

    private void validateGetRedPacketNode(RewardRuleDTO rewardRuleDTO, RewardDetailDTO rewardDetailDTO) {
        String actionId = rewardDetailDTO.getRewardNode().getRewardAction();
        if (Strings.isNullOrEmpty(actionId)) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RULE_MANAGER_1));
        } else {
            QuerySnAction.Arg querySnArg = new QuerySnAction.Arg();
            querySnArg.setUniqueId(ScanCodeActionConstants.CONSUMER_GET_RED_PACKAGE);
            QuerySnAction.Result querySnResult = fmcgSnProxy.querySnAction(Integer.valueOf(rewardRuleDTO.getTenantId()), -10000, querySnArg);
            if (CollectionUtils.isNotEmpty(querySnResult.getResult().getActions()) && !querySnResult.getResult().getActions().get(0).getId().equals(actionId)) {
                throw new ValidateException(I18N.text(I18NEnums.CONSUMER_REWARD_ACTION_MUST_BE_CONSUMER_SCAN_REWARD.getCode()));
            }
        }
        RewardStrategyDTO rewardStrategyDTO = rewardDetailDTO.getRewardStrategy();
        if (rewardStrategyDTO.getRewardMethod().equals(RewardMethodEnum.NONE.code())) {
            return;
        }
        if (rewardStrategyDTO.getIndividualRewardLimit() != null && rewardStrategyDTO.getIndividualRewardLimit() < 0) {
            throw new ValidateException(I18N.text(I18NEnums.PERSONAL_GET_COUNT_MUST_BIGGER_THAN_ZERO.getCode()));
        }
        if (rewardStrategyDTO.getDailyRewardLimit() != null && rewardStrategyDTO.getDailyRewardLimit() < 0) {
            throw new ValidateException(I18N.text(I18NEnums.PERSONAL_DAILY_GET_COUNT_MUST_BIGGER_THAN_ZERO.getCode()));
        }
        if (RewardMethodEnum.PHYSICAL_ITEM.code().equals(rewardStrategyDTO.getRewardMethod())) {
            if (CollectionUtils.isEmpty(rewardStrategyDTO.getRewardGetMethod())) {
                throw new ValidateException(I18N.text(I18NKeys.VALIDATE_REWARD_RULE_MANAGER_2));
            }
        } else {
            if (rewardStrategyDTO.getRewardMethodType().equals(RewardMethodTypeEnum.RANDOM_RED_PACKET.code())) {
                for (RandomRewardLevelDTO levelDTO : rewardStrategyDTO.getRandomRewardLevels()) {
                    if (levelDTO.getRewardCount() == null) {
                        throw new ValidateException(I18N.text(I18NEnums.RANDOM_RED_PACKET_NUMBER_CAN_NOT_BE_EMPTY.getCode()));
                    }
                }
            } else if (rewardStrategyDTO.getRewardMethodType().equals(RewardMethodTypeEnum.SOLID_RED_PACKET.code())) {
                if (rewardStrategyDTO.getRewardCount() == null) {
                    throw new ValidateException(I18N.text(I18NEnums.RED_PACKET_NUMBER_CAN_NOT_BE_EMPTY.getCode()));
                }
            }
        }
    }

    private void validateBigDate(RewardRuleDTO rewardRuleDTO) {
        RewardDetailDTO rewardDetailDTO = rewardRuleDTO.getRewardDetails().get(rewardRuleDTO.getRewardDetails().size() - 1);
        RewardDimensionEnum lastRewardDimension = RewardDimensionEnum.get(rewardDetailDTO.getRewardNode().getRewardDimension());
        String actionCode = fmcgSerialNumberService.getActionUniqueIdByActionId(rewardRuleDTO.getTenantId(), rewardDetailDTO.getRewardNode().getRewardAction());
        if (ScanCodeActionConstants.BIG_DATE_ACTIVITY_TYPE_TEMPLATE_ID.equals(rewardRuleDTO.getRuleType()) || ScanCodeActionConstants.CONSUMER_SCAN.equals(actionCode)) {
            if (RewardDimensionEnum.CONSUMER != lastRewardDimension) {
                throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_MANAGER_1));
            }

            deductPayValidate(rewardRuleDTO, rewardDetailDTO);
        }
    }

    private void deductPayValidate(RewardRuleDTO rewardRuleDTO, RewardDetailDTO rewardDetailDTO) {
        if (!RewardMethodEnum.REDUCED_PAYMENT.code().equals(rewardDetailDTO.getRewardStrategy().getRewardMethod())) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD2_REWARD_RULE_MANAGER_0));
        }
        String actionId = rewardDetailDTO.getRewardNode().getRewardAction();
        if (Strings.isNullOrEmpty(actionId)) {
            throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_MANAGER_2));
        } else {
            QuerySnAction.Arg querySnArg = new QuerySnAction.Arg();
            querySnArg.setUniqueId(ScanCodeActionConstants.CONSUMER_SCAN);
            QuerySnAction.Result querySnResult = fmcgSnProxy.querySnAction(Integer.valueOf(rewardRuleDTO.getTenantId()), -10000, querySnArg);
            if (CollectionUtils.isNotEmpty(querySnResult.getResult().getActions()) && !querySnResult.getResult().getActions().get(0).getId().equals(actionId)) {
                throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_MANAGER_3));
            }
        }
    }

    @Override
    public String getConsumerReceiverAccount(String tenantId, String codeId, ActivityRewardRulePO rewardRulePO) {
        //仅有一个消费者节点。
        RewardDetailEntity rewardDetail = rewardRulePO.getRewardDetails().stream().filter(v -> v.getRewardNode().getRewardDimension().equals(RewardDimensionEnum.CONSUMER.code())).findFirst().orElse(null);
        if (rewardDetail == null) {
            throw new RewardFmcgException("10010", I18N.text(I18NKeys.REWARD2_REWARD_RULE_MANAGER_1));
        }
        RewardAccountEntity rewardAccountEntity = rewardDetail.getRewardPayment().getReceiveAccount();
        if (ReceiveAccountTypeEnum.MAIN_ACCOUNT.code().equals(rewardAccountEntity.getAccountType())) {
            return tenantId;
        } else if (ReceiveAccountTypeEnum.STORE_DEALER.code().equals(rewardAccountEntity.getAccountType())) {
            IObjectData storeSignSnCode = fmcgSerialNumberService.getStoreSignSerialNumberStatusObj(tenantId, codeId, false);
            return storeSignSnCode.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        }
        return null;
    }

    @Override
    public void validateContainsFailRewardDetail(String tenantId, String uniqueId) {
        ActivityRewardRulePO po = activityRewardRuleDAO.getById(tenantId, uniqueId);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityRewardDetailFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(po.getRelatedObjectId()));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityRewardDetailFields.STATUS);
        statusFilter.setOperator(Operator.IN);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityRewardDetailFields.Status.UNDO, TPMActivityRewardDetailFields.Status.ERROR));

        query.setFilters(Lists.newArrayList(activityFilter, statusFilter));

        List<IObjectData> dataList = CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
        if (CollectionUtils.isNotEmpty(dataList)) {
            throw new ValidateException(I18N.text(I18NKeys.REWARD_RULE_MANAGER_4));
        }
    }

    @Override
    public boolean checkHasEdit(ActivityRewardRulePO editPO) {
        ActivityRewardRulePO dbPo = activityRewardRuleDAO.get(editPO.getTenantId(), editPO.getUniqueId());

        return !dbPo.equals(editPO);
    }

    @Override
    public boolean checkHasEdit(ActivityRewardRulePO oldOne, ActivityRewardRulePO newOne) {
        return !oldOne.equals(newOne);
    }

    private RewardFieldDescribeDTO formDepartmentLevelField(String tenantId, String
            fieldName, List<SimpleDTO> options) {
        RewardFieldDescribeDTO rewardFieldDescribeDTO = new RewardFieldDescribeDTO();
        rewardFieldDescribeDTO.setFieldName(fieldName);
        rewardFieldDescribeDTO.setFieldType("select_one");

        //todo: query up to two department which was located on level ${option.val}  and  fill it`s name in option.containItems
        List<DepartmentDto> departments = organizationService.queryAllDepartment(Integer.parseInt(tenantId));

        for (SimpleDTO option : options) {
            List<DepartmentDto> departmentDtos = departments.stream().filter(department -> department.getAncestors().size() == Integer.parseInt(option.getValue())).collect(Collectors.toList());
            if (departmentDtos.size() > 2) {
                departmentDtos = departmentDtos.subList(0, 2);
            }
            option.setContainItems(departmentDtos.stream().map(DepartmentDto::getName).collect(Collectors.toList()));
        }

        rewardFieldDescribeDTO.setOptions(options);
        return rewardFieldDescribeDTO;
    }


    private RewardFieldDescribeDTO formSelectOneField(String fieldName, List<SimpleDTO> options) {
        RewardFieldDescribeDTO rewardFieldDescribeDTO = new RewardFieldDescribeDTO();
        rewardFieldDescribeDTO.setFieldName(fieldName);
        rewardFieldDescribeDTO.setFieldType("select_one");
        rewardFieldDescribeDTO.setOptions(options);
        return rewardFieldDescribeDTO;
    }

    private RewardFieldDescribeDTO formSelectManyField(String fieldName, List<SimpleDTO> options) {
        RewardFieldDescribeDTO rewardFieldDescribeDTO = new RewardFieldDescribeDTO();
        rewardFieldDescribeDTO.setFieldName(fieldName);
        rewardFieldDescribeDTO.setFieldType("select_many");
        rewardFieldDescribeDTO.setOptions(options);
        return rewardFieldDescribeDTO;
    }

    private List<SimpleDTO> getRewardActionList(String tenantId) {

        List<SimpleDTO> simpleDTOList = new ArrayList<>();

        QuerySnAction.Result queryResult = fmcgSnProxy.querySnAction(Integer.valueOf(tenantId), -10000, new QuerySnAction.Arg());
        if (queryResult.getErrCode() != 0) {
            throw new RewardFmcgException("1011", queryResult.getErrMessage());
        }


        if (CollectionUtils.isNotEmpty(queryResult.getResult().getActions())) {
            queryResult.getResult().getActions().forEach(fmcgSerialNumberActionEntity -> {
                if (fmcgSerialNumberActionEntity.getActionStatus() == 0 || fmcgSerialNumberActionEntity.getUniqueId().equals("CONSUMER_RED_PACKET")) {
                    return;
                }
                SimpleDTO simpleDTO = new SimpleDTO();
                simpleDTO.setName(fmcgSerialNumberActionEntity.getActionName());
                simpleDTO.setValue(fmcgSerialNumberActionEntity.getId());
                Set<String> belongs = new HashSet<>();
                for (Integer type : fmcgSerialNumberActionEntity.getChannelType()) {
                    if (type == 1 || type == 0 || type == 4) {
                        belongs.add(RewardDimensionEnum.Type.MASTER.code());
                        belongs.add(RewardDimensionEnum.Type.DOWNSTREAM.code());
                    } else if (type == 2 || type == 5) {
                        belongs.add(RewardDimensionEnum.Type.STORE.code());
                    } else {
                        belongs.add(RewardDimensionEnum.Type.CONSUMER.code());
                    }
                }
                simpleDTO.setBelongTo(new ArrayList<>(belongs));
                simpleDTO.setType(fmcgSerialNumberActionEntity.getUniqueId());
                simpleDTO.setAllowTrigger(isAllowTrigger(tenantId, fmcgSerialNumberActionEntity));
                simpleDTOList.add(simpleDTO);
            });
        }


        return simpleDTOList;
    }

    private boolean isAllowTrigger(String tenantId, QuerySnAction.FMCGSerialNumberActionEntity entity) {
        List<String> allowTriggerActions = ALLOW_TRIGGER_MAP.getOrDefault(tenantId, ALLOW_TRIGGER_MAP.get("default"));
        return !Strings.isNullOrEmpty(entity.getAccountField()) && allowTriggerActions.contains(entity.getUniqueId());
    }

    public Set<String> getAllowTriggerActions(String tenantId) {
        Set<String> allowTriggerActions = new HashSet<>(ALLOW_TRIGGER_MAP.getOrDefault(tenantId, ALLOW_TRIGGER_MAP.get("default")));
        allowTriggerActions.addAll(ALLOW_TRIGGER_MAP.get("default"));
        allowTriggerActions.add("STORE_CHECK_WRITE_OFFS");
        allowTriggerActions.add("STORE_STOCK_CHECK");
        return allowTriggerActions;
    }

    @Override
    public Map<String, Object> getConsumerRedPacketSettingInfo(RewardDetailDTO rewardDetailDTO) {
        Map<String, Object> updateMap = new HashMap<>();
        if (RewardMethodTypeEnum.RANDOM_RED_PACKET.code().equals(rewardDetailDTO.getRewardStrategy().getRewardMethodType())) {
            rewardDetailDTO.getRewardStrategy().getRandomRewardLevels().forEach(level -> {
                updateMap.put(String.format("l%s_red_packet_count__c", level.getLevel()), level.getRewardCount());
                updateMap.put(String.format("l%s_red_packet_amount__c", level.getLevel()), level.getRewardAmount());
            });
            int size = rewardDetailDTO.getRewardStrategy().getRandomRewardLevels().size() + 1;
            if (size < 5) {
                while (size <= 5) {
                    updateMap.put(String.format("l%s_red_packet_count__c", size), null);
                    updateMap.put(String.format("l%s_red_packet_amount__c", size++), null);
                }
            }
        } else if (RewardMethodTypeEnum.SOLID_RED_PACKET.code().equals(rewardDetailDTO.getRewardStrategy().getRewardMethodType())) {
            updateMap.put(String.format("l%s_red_packet_count__c", 1), rewardDetailDTO.getRewardStrategy().getRewardCount());
            updateMap.put(String.format("l%s_red_packet_amount__c", 1), rewardDetailDTO.getRewardStrategy().getRewardQuantity());
            for (int i = 2; i <= 5; i++) {
                updateMap.put(String.format("l%s_red_packet_count__c", i), null);
                updateMap.put(String.format("l%s_red_packet_amount__c", i), null);
            }
        }
        return updateMap;
    }
}
