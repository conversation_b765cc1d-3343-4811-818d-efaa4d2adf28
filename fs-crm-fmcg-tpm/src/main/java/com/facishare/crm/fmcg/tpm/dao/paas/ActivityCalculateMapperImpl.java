package com.facishare.crm.fmcg.tpm.dao.paas;

import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.fxiaoke.common.SqlEscaper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/22 上午11:28
 */
@Component
public class ActivityCalculateMapperImpl implements ActivityCalculateMapper {

    @Resource
    private SpecialTableMapper specialTableMapper;


    @Override
    public Map<String, BigDecimal> statisticMoneyByUnifiedActivityId(String tenantId, String unifiedActivityId) {
        String sqlTmp = "select\n" +
                "\toccupy_money,\n" +
                "\twrite_off_money,\n" +
                "\toccupy_money + write_off_money used_money\n" +
                "from\n" +
                "\t(\n" +
                "\tselect\n" +
                "\t\tsum(case closed_status when 'closed' then 0 else coalesce(activity_amount , 0) - coalesce(activity_actual_amount, 0) end ) occupy_money,\n" +
                "\t\tsum(coalesce(activity_actual_amount, 0)) write_off_money\n" +
                "\tfrom\n" +
                "\t\tfmcg_tpm_activity fta\n" +
                "\twhere\n" +
                "\t\tis_deleted = 0\n" +
                "\t\tand activity_unified_case_id = '#{unified_activity_id}'\n" +
                "\t\tand tenant_id = '#{tenant_id}'\n" +
                "\t\tand life_status = 'normal') a";

        String realSql = sqlTmp.replace("#{tenant_id}", SqlEscaper.pg_escape(tenantId)).replace("#{unified_activity_id}", SqlEscaper.pg_escape(unifiedActivityId));
        List<Map> list = specialTableMapper.setTenantId(tenantId).findBySql(realSql).stream().filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, BigDecimal> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(map -> {
                result.put("occupy_money", map.get("occupy_money") == null ? null : new BigDecimal(String.valueOf(map.get("occupy_money"))));
                result.put("write_off_money", map.get("write_off_money") == null ? null : new BigDecimal(String.valueOf(map.get("write_off_money"))));
                result.put("used_money", map.get("used_money") == null ? null : new BigDecimal(String.valueOf(map.get("used_money"))));
            });
        }
        return result;
    }
}
