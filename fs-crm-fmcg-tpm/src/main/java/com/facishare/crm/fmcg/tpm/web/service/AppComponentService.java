package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypePO;
import com.facishare.crm.fmcg.tpm.dao.pg.ActivityMapper;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityPlanGroupByTypeDatumPO;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.ActivitySimpleList;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTypeReport;
import com.facishare.crm.fmcg.tpm.web.contract.model.*;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityReportLoaderFactory;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IAppComponentService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.PrivilegeResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/16 14:39
 */
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class AppComponentService extends BaseService implements IAppComponentService, InitializingBean {

    protected static final List<String> SYSTEM_TPM_OBJECT_API_NAMES = Lists.newArrayList();
    protected static final List<String> BUDGET_OBJECT_API_NAMES = Lists.newArrayList();
    protected static List<String> iconList = Lists.newArrayList();
    protected static final Set<String> SKIP_ACTIVITY_REFERENCE_CHECK_API_NAMES = Sets.newHashSet();


    static {
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_DEALER_ACTIVITY_COST);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
        SYSTEM_TPM_OBJECT_API_NAMES.add(ApiNames.TPM_STORE_WRITE_OFF_OBJ);

        BUDGET_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET);
        BUDGET_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET_ADJUST_OBJ);
        BUDGET_OBJECT_API_NAMES.add(ApiNames.TPM_ACTIVITY_BUDGET_DETAIL_OBJ);

        SKIP_ACTIVITY_REFERENCE_CHECK_API_NAMES.add(ApiNames.TPM_ACTIVITY_OBJ);
        SKIP_ACTIVITY_REFERENCE_CHECK_API_NAMES.add(ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ);
    }

    private Cache<String, List<ActivityTypeReportDatumVO>> activityTypeReportCache;
    private static final Lock ACTIVITY_TYPE_LOAD_LOCK = new ReentrantLock();

    @PostConstruct
    public void init() {
        ConfigFactory.getConfig("fs-crm-icon-path",
                conf -> {
                    iconList.clear();
                    String json = conf.get("icon_list");
                    JSONArray arr = JSON.parseObject(json).getJSONArray("icon_path");
                    for (int i = 0; i < arr.size(); i++) {
                        iconList.add(arr.getString(i));
                    }
                });
    }

    @Resource
    private ActivityTypeDAO activityTypeDAO;
    @Resource
    private IActivityTypeManager activityTypeManager;
    @Resource
    private ActivityMapper activityMapper;

    @Override
    public ActivitySimpleList.Result activityTypeSimpleList(ActivitySimpleList.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        List<ActivityTypePO> types = activityTypeDAO.all(context.getTenantId(), true, false);
        List<String> apiNames = SYSTEM_TPM_OBJECT_API_NAMES;

        for (ActivityTypePO type : types) {
            for (ActivityNodeEntity node : type.getActivityNodes()) {
                apiNames.add(node.getObjectApiName());
            }
        }

        List<ActivityTypeSimpleVO> voData = types.stream().map(ActivityTypeSimpleVO::fromPO).collect(Collectors.toList());
        this.fillActivityPlanStatisticsData(context.getTenantId(), voData);

        List<String> distinctApiNames = apiNames.stream().distinct().collect(Collectors.toList());

        List<String> functionCodeApiNames = distinctApiNames.stream().filter(s -> !s.equals(ApiNames.TPM_ACTIVITY_PROOF_OBJ) && !s.equals(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ)).collect(Collectors.toList());
        Map<String, Map<String, Boolean>> functionCodeMap = queryFunctionCodeMap(context.getTenantId(), context.getEmployeeId().toString(), functionCodeApiNames, Lists.newArrayList("List"));
        Map<String, Boolean> functionBooleanMap = functionCodeMap.keySet().stream().collect(Collectors.toMap(k -> k, v -> functionCodeMap.get(v).get("List")));
        functionBooleanMap.put(ApiNames.TPM_ACTIVITY_PROOF_OBJ, Boolean.TRUE);
        functionBooleanMap.put(ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, Boolean.TRUE);

        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(context.getTenantId(), distinctApiNames);
        List<GroupVO<ObjectSimpleVO>> groups = Lists.newArrayList();
        groups.add(new GroupVO<>(
                        "budget_management",
                        I18N.text(I18NKeys.FMCG_TPM_GROUP_BUDGET),
                        describeMap.values().stream().filter(describe -> BUDGET_OBJECT_API_NAMES.contains(describe.getApiName())
                                && functionBooleanMap.containsKey(describe.getApiName())
                                && functionBooleanMap.get(describe.getApiName()))
                                .map(m -> ObjectSimpleVO.fromDescribe(m, iconList))
                                .collect(Collectors.toList())
                )
        );
        groups.add(new GroupVO<>(
                        "activity_management",
                        I18N.text(I18NKeys.FMCG_TPM_GROUP_TPM),
                        describeMap.values().stream().filter(describe -> !BUDGET_OBJECT_API_NAMES.contains(describe.getApiName())
                                && functionBooleanMap.containsKey(describe.getApiName())
                                && functionBooleanMap.get(describe.getApiName()))
                                .map(m -> ObjectSimpleVO.fromDescribe(m, iconList))
                                .collect(Collectors.toList())
                )
        );

        groups = groups.stream().filter(v -> CollectionUtils.isNotEmpty(v.getData())).collect(Collectors.toList());

        String activityTypeId = CollectionUtils.isEmpty(types) ? "" : types.get(0).getId().toString();
        List<ActivityTypeReportDatumVO> reportData = null;
        if (Boolean.TRUE.equals(arg.getNeedReturnFirstReport())) {
            reportData = activityTypeReport(context.getTenantId(), activityTypeId);
        }

        return ActivitySimpleList.Result.builder()
                .objectGroup(sortGroups(groups, distinctApiNames))
                .activityTypeList(voData)
                .reportData(reportData)
                .build();
    }

    private List<GroupVO<ObjectSimpleVO>> sortGroups(List<GroupVO<ObjectSimpleVO>> groups, List<String> distinctApiNames) {
        if (CollectionUtils.isEmpty(groups)) {
            return groups;
        }
        for (GroupVO<ObjectSimpleVO> group : groups) {

            List<ObjectSimpleVO> sortList = Lists.newArrayList();
            Map<String, ObjectSimpleVO> simpleMap = group.getData().stream().collect(Collectors.toMap(ObjectSimpleVO::getApiName, o -> o));

            for (String apiName : distinctApiNames) {
                if (simpleMap.containsKey(apiName)) {
                    sortList.add(simpleMap.get(apiName));
                }
            }
            group.setData(sortList);
        }
        return groups;
    }

    private Map<String, Map<String, Boolean>> queryFunctionCodeMap(String tenantId, String userId, List<String> apiNames, List<String> actionCodes) {
        Map<String, List<String>> functionCodeListMap = new HashMap<>();
        for (String apiName : apiNames) {
            functionCodeListMap.put(apiName, actionCodes);
        }

        RequestContext requestContext = RequestContext.builder()
                .user(new User(tenantId, userId))
                .tenantId(tenantId)
                .build();

        PrivilegeResult<Map<String, Map<String, Boolean>>> codeRst = serviceFacade.batchObjectFuncPrivilegeCheck(requestContext, functionCodeListMap);
        return codeRst.getCode() == 0 ? codeRst.getResult() : new HashMap<>();
    }

    @Override
    public ActivityTypeReport.Result activityTypeReport(ActivityTypeReport.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        ActivityTypePO activityType = activityTypeDAO.get(context.getTenantId(), arg.getId());


        Map<String, String> displayNameMap = activityTypeManager.loadDescribeMap(context.getTenantId(), activityType)
                .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDisplayName()));

        ActivityTypeReport.Result result = ActivityTypeReport.Result.builder()
                .reportData(activityTypeReport(context.getTenantId(), activityType, displayNameMap))
                .activityReportDataList(Lists.newArrayList())
                .build();

        if (arg.isNeedReturnActivityReport()) {
            List<IObjectData> activities = queryActivity(context.getTenantId(), arg.getId());
            fillWebDetailWithData(context.getTenantId(), activities);
            Map<String, Map<String, ActivityReportDatumVO>> reportDatumMap = loadActivityStatisticsData(context.getTenantId(), activityType, activities, displayNameMap);
            for (IObjectData activity : activities) {
                ActivityReportDataVO datum = new ActivityReportDataVO();

                datum.setId(activity.getId());
                datum.setName(activity.getName());
                datum.setTemplateName(activity.get("activity_unified_case_id__r", String.class, ""));
                datum.setReportData(Lists.newArrayList());

                for (ActivityNodeEntity activityNode : activityType.getActivityNodes()) {
                    if (SKIP_ACTIVITY_REFERENCE_CHECK_API_NAMES.contains(activityNode.getObjectApiName())) {
                        continue;
                    }
                    if (reportDatumMap.get(activityNode.getObjectApiName()).containsKey(activity.getId())) {
                        datum.getReportData().add(reportDatumMap.get(activityNode.getObjectApiName()).get(activity.getId()));
                    }
                }

                result.getActivityReportDataList().add(datum);
            }
            result.getActivityReportDataList().sort(Comparator.comparing(ActivityReportDataVO::getTemplateName));
            for (ActivityReportDataVO datum : result.getActivityReportDataList()) {
                datum.setReportData(datum.getReportData().stream().filter(f -> f.getCount() != 0).collect(Collectors.toList()));
            }
        }
        return result;
    }

    private List<IObjectData> fillWebDetailWithData(String tenantId, List<IObjectData> data) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ);
        serviceFacade.fillObjectDataWithRefObject(describe, data, User.systemUser(tenantId), null, false);
        return data;
    }

    private List<IObjectData> queryActivity(String tenantId, String activityTypeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(10);
        query.setOffset(0);

        Filter activityTypeFilter = new Filter();
        activityTypeFilter.setFieldName(TPMActivityFields.ACTIVITY_TYPE);
        activityTypeFilter.setOperator(Operator.EQ);
        activityTypeFilter.setFieldValues(Lists.newArrayList(activityTypeId));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(activityTypeFilter, lifeStatusFilter));

        OrderBy createTimeDescOrder = new OrderBy();
        createTimeDescOrder.setFieldName(CommonFields.CREATE_TIME);
        createTimeDescOrder.setIsAsc(false);

        query.setOrders(Lists.newArrayList(createTimeDescOrder));

        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_OBJ, query);

        return result.getData();
    }

    private Map<String, Map<String, ActivityReportDatumVO>> loadActivityStatisticsData(String tenantId, ActivityTypePO activityType, List<IObjectData> activities, Map<String, String> displayNameMap) {
        List<String> activityIds = activities.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, Map<String, ActivityReportDatumVO>> countMap = new HashMap<>();
        for (ActivityNodeEntity node : activityType.getActivityNodes()) {
            if (SKIP_ACTIVITY_REFERENCE_CHECK_API_NAMES.contains(node.getObjectApiName())) {
                continue;
            }
            if (ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ.equals(node.getObjectApiName())) {
                countMap.put(node.getObjectApiName(), loadAgreementReportData(tenantId, node, activityIds, displayNameMap));
            } else {
                countMap.put(node.getObjectApiName(), loadReportData(tenantId, node, activityIds, displayNameMap));
            }
        }
        return countMap;
    }

    private Map<String, ActivityReportDatumVO> loadAgreementReportData(String tenantId, ActivityNodeEntity node, List<String> activityIds, Map<String, String> displayNameMap) {
        Map<String, Integer> totalCountMap = loadTotalCount(tenantId, node, activityIds);
        Map<String, Integer> inProgressCountMap = loadAgreementInProgressCount(tenantId, node, activityIds);

        Map<String, ActivityReportDatumVO> map = Maps.newHashMap();
        for (String activityId : activityIds) {
            ActivityReportDatumVO report = new ActivityReportDatumVO();

            report.setTemplateId(node.getTemplateId());
            report.setObjectApiName(node.getObjectApiName());
            report.setObjectDisplayName(displayNameMap.getOrDefault(node.getObjectApiName(), "--"));
            report.setType(node.getType());
            report.setCount(totalCountMap.getOrDefault(activityId, 0));
            report.setTotalCount(totalCountMap.getOrDefault(activityId, 0));
            report.setScheduleCount(0L);
            report.setInProgressCount(inProgressCountMap.getOrDefault(activityId, 0));
            report.setEndedCount(0L);

            map.put(activityId, report);
        }
        return map;
    }

    private Map<String, ActivityReportDatumVO> loadReportData(String tenantId, ActivityNodeEntity node, List<String> activityIds, Map<String, String> displayNameMap) {
        Map<String, Integer> totalCountMap = loadTotalCount(tenantId, node, activityIds);
        Map<String, ActivityReportDatumVO> map = Maps.newHashMap();
        for (String activityId : activityIds) {
            ActivityReportDatumVO report = new ActivityReportDatumVO();

            report.setTemplateId(node.getTemplateId());
            report.setObjectApiName(node.getObjectApiName());
            report.setObjectDisplayName(displayNameMap.getOrDefault(node.getObjectApiName(), "--"));
            report.setType(node.getType());
            report.setCount(totalCountMap.getOrDefault(activityId, 0));
            report.setTotalCount(totalCountMap.getOrDefault(activityId, 0));
            report.setScheduleCount(0L);
            report.setInProgressCount(0L);
            report.setEndedCount(0L);

            map.put(activityId, report);
        }
        return map;
    }

    private Map<String, Integer> loadTotalCount(String tenantId, ActivityNodeEntity node, List<String> activityIds) {
        String apiName = node.getObjectApiName();
        String referenceActivityFieldApiName = node.getReferenceActivityFieldApiName();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(20);
        query.setOffset(0);

        if (CollectionUtils.isEmpty(activityIds)) {
            return Maps.newHashMap();
        }
        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(referenceActivityFieldApiName);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityIds));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, activityIdFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                apiName,
                Lists.newArrayList(referenceActivityFieldApiName),
                "count",
                null);

        return data.stream().collect(Collectors.toMap(k -> k.get(referenceActivityFieldApiName, String.class), v -> v.get("groupbycount", Integer.class)));
    }

    private Map<String, Integer> loadAgreementInProgressCount(String tenantId, ActivityNodeEntity node, List<String> activityIds) {
        String apiName = node.getObjectApiName();
        String referenceActivityFieldApiName = node.getReferenceActivityFieldApiName();

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(20);
        query.setOffset(0);

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(referenceActivityFieldApiName);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(Lists.newArrayList(activityIds));

        Filter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(TPMActivityFields.LIFE_STATUS);
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(TPMActivityAgreementFields.AGREEMENT_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS));

        query.setFilters(Lists.newArrayList(lifeStatusFilter, activityIdFilter, statusFilter));

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(tenantId),
                query,
                apiName,
                Lists.newArrayList(referenceActivityFieldApiName),
                "count",
                null);

        return data.stream().collect(Collectors.toMap(k -> k.get(referenceActivityFieldApiName, String.class), v -> v.get("groupbycount", Integer.class)));
    }

    private void fillActivityPlanStatisticsData(String tenantId, List<ActivityTypeSimpleVO> activityTypes) {
        Map<String, Map<String, Long>> activityPlanData = activityMapper.setTenantId(tenantId).countByActivityType(tenantId)
                .stream().collect(Collectors.groupingBy(ActivityPlanGroupByTypeDatumPO::getActivityTypeId))
                .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().stream().collect(Collectors.toMap(
                        ActivityPlanGroupByTypeDatumPO::getStatus,
                        ActivityPlanGroupByTypeDatumPO::getCount
                ))));
        for (ActivityTypeSimpleVO activityType : activityTypes) {
            Map<String, Long> datum = activityPlanData.getOrDefault(activityType.getId(), Maps.newHashMap());
            long total = datum.values().stream().mapToLong(v -> v).sum();
            activityType.setActivityPlanReportData(
                    ActivityPlanReportDatumVO.builder()
                            .totalCount(total)
                            .count(total)
                            .inProgressCount(datum.getOrDefault(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS, 0L))
                            .build()
            );
        }
    }

    private List<ActivityTypeReportDatumVO> activityTypeReport(String tenantId, String id) {
        if (Strings.isNullOrEmpty(id)) {
            return Lists.newArrayList();
        }
        ActivityTypePO activityType = activityTypeDAO.get(tenantId, id);
        Map<String, String> displayNameMap = activityTypeManager.loadDescribeMap(tenantId, activityType)
                .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDisplayName()));
        return activityTypeReport(tenantId, activityType, displayNameMap);
    }

    private List<ActivityTypeReportDatumVO> activityTypeReport(String tenantId, ActivityTypePO activityType, Map<String, String> displayNameMap) {

        String key = String.format("%s.%s", tenantId, activityType.getId().toString());
        List<ActivityTypeReportDatumVO> data = activityTypeReportCache.getIfPresent(key);
        if (!CollectionUtils.isEmpty(data)) {
            return data;
        }

        ACTIVITY_TYPE_LOAD_LOCK.lock();
        try {
            data = activityType.getActivityNodes().stream()
                    .map(node -> ActivityReportLoaderFactory.resolve(node.getType()).loadActivityTypeReport(tenantId, activityType.getId().toString(), node, displayNameMap, activityType))
                    .collect(Collectors.toList());
            activityTypeReportCache.put(key, data);
        } finally {
            ACTIVITY_TYPE_LOAD_LOCK.unlock();
        }

        return data;
    }

    @Override
    public void afterPropertiesSet() {
        activityTypeReportCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .build();
    }
}