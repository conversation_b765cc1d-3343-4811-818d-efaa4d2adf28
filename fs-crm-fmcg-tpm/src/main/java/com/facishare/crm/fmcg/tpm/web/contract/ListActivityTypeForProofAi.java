package com.facishare.crm.fmcg.tpm.web.contract;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IListParameter;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 17:36
 */
public interface ListActivityTypeForProofAi {

    @Data
    @ToString
    class Arg implements IListParameter, Serializable {

        private String keyword;

        private String template;

        // 默认 1 开启，0 不开启的
        private String openAi;

        private Integer limit;

        private Integer offset;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<String> data;

        private long total;
    }
}
