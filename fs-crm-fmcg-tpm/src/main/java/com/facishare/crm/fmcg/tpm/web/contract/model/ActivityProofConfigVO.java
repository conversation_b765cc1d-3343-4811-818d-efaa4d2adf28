package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 15:34
 */
@Data
@ToString
public class ActivityProofConfigVO implements Serializable {

    @JSONField(name = "frequency_config")
    @JsonProperty(value = "frequency_config")
    @SerializedName("frequency_config")
    private ActivityProofFrequencyConfigVO frequencyConfig;

    @JSONField(name = "cost_calculate_config")
    @JsonProperty(value = "cost_calculate_config")
    @SerializedName("cost_calculate_config")
    private ActivityProofCostCalculateConfigVO costCalculateConfig;

    @JSONField(name = "ai_config")
    @JsonProperty(value = "ai_config")
    @SerializedName("ai_config")
    private ActivityProofAiConfigVO aiConfig;

    public static ActivityProofConfigVO fromPO(ActivityProofConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofConfigVO vo = new ActivityProofConfigVO();
        vo.setFrequencyConfig(ActivityProofFrequencyConfigVO.fromPO(po.getFrequencyConfig()));
        vo.setCostCalculateConfig(ActivityProofCostCalculateConfigVO.fromPO(po.getCostCalculateConfig()));
        vo.setAiConfig(ActivityProofAiConfigVO.fromPO(po.getAiConfig()));
        return vo;
    }
}
