package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofCostCalculateConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 18:10
 */
@Data
@ToString
public class ActivityProofCostCalculateConfigEntity implements Serializable {

    @Property("calculate_type")
    private String calculateType;

    @Property("ratio")
    private String ratio;


    public static ActivityProofCostCalculateConfigEntity fromVO(ActivityProofCostCalculateConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityProofCostCalculateConfigEntity entity = new ActivityProofCostCalculateConfigEntity();
        entity.setCalculateType(vo.getCalculateType());
        entity.setRatio(vo.getRatio());
        return entity;
    }
}
