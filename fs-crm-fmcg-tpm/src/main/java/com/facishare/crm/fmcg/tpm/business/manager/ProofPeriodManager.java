package com.facishare.crm.fmcg.tpm.business.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.TPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.abstraction.AbstractDisPlayReportBaseService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.web.contract.model.TPMProofPeriodTime;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class ProofPeriodManager extends AbstractDisPlayReportBaseService {
    private static Map<String, Integer> PROOF_DATA_LIMIT = new HashMap<>();
    private static Map<String, Integer> PROOF_DETAIL_PAGE_SIZE = new HashMap<>();
    @Resource
    private ActivityTypeManager activityTypeManager;
    @Resource
    private ServiceFacade serviceFacade;


    static {
        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            String editFieldJSON = iConfig.get("proof_data_limit");
            if (!Strings.isNullOrEmpty(editFieldJSON)) {
                PROOF_DATA_LIMIT = JSON.parseObject(editFieldJSON, new TypeReference<Map<String, Integer>>() {
                });
            }
            String proofDataPageSize = iConfig.get("proof_data_page_size");
            if (!Strings.isNullOrEmpty(proofDataPageSize)) {
                PROOF_DETAIL_PAGE_SIZE = JSON.parseObject(proofDataPageSize, new TypeReference<Map<String, Integer>>() {
                });
            }
        });
    }

    /**
     * 举证阶段明细新建
     */
    public void addProofPeriodTime(String userId, BaseObjectSaveAction.Result result) {
        log.info("添加活动举证时段");
        String tenantId = result.getObjectData().toObjectData().getTenantId();
        if (!existTPMProofTimePeriodDetailObjectDescribe(tenantId)) {
            return;
        }
        String activityId = "";
        String agreementId = "";
        String proofPeriodStr = "";
        String describeApiName = result.getObjectData().toObjectData().getDescribeApiName();
        if (describeApiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
            activityId = result.getObjectData().toObjectData().getId();
            proofPeriodStr = result.getObjectData().toObjectData().get(TPMActivityFields.PROOF_PERIOD, String.class, "");
        } else if (describeApiName.equals(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)) {
            activityId = result.getObjectData().toObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
            agreementId = result.getObjectData().toObjectData().getId();
            proofPeriodStr = findProofPeriodOfAgreement(userId, result.getObjectData().toObjectData(), tenantId, activityId);
        }

        String id = result.getObjectData().toObjectData().getId();
        if (Objects.isNull(proofPeriodStr) || StringUtils.isBlank(proofPeriodStr)) {
            log.info("活动{}, 举证时段为空 或 为 {}，不处理", id, proofPeriodStr);
            return;
        }
        doBulkSaveObjectData(userId, result, proofPeriodStr, tenantId, activityId, agreementId);
    }

    /**
     * 举证阶段明细编辑
     */
    public void editProofPeriodTime(String userId, BaseObjectSaveAction.Result result) {
        String id = result.getObjectData().toObjectData().getId();
        String tenantId = result.getObjectData().toObjectData().getTenantId();

        if (!existTPMProofTimePeriodDetailObjectDescribe(tenantId)) {
            return;
        }

        String proofPeriodStr = "";
        String activityId = "";
        String agreementId = "";
        String fieldName = "";
        String describeApiName = result.getObjectData().toObjectData().getDescribeApiName();

        if (describeApiName.equals(ApiNames.TPM_ACTIVITY_OBJ)) {
            activityId = result.getObjectData().toObjectData().getId();
            fieldName = TPMProofTimePeriodDetailFields.ACTIVITY_ID;
            proofPeriodStr = result.getObjectData().toObjectData().get(TPMActivityFields.PROOF_PERIOD, String.class, "");
        } else if (describeApiName.equals(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)) {
            activityId = result.getObjectData().toObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID, String.class);
            agreementId = result.getObjectData().toObjectData().getId();
            fieldName = TPMProofTimePeriodDetailFields.AGREEMENT_ID;

            IObjectData agreement = serviceFacade.findObjectData(User.builder().userId(userId).tenantId(tenantId).build(), agreementId, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            Long oldBeginDate = agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            Long oldEndDate = agreement.get(TPMActivityAgreementFields.END_DATE, Long.class);

            long beginDate = result.getObjectData().toObjectData().get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            long endDate = result.getObjectData().toObjectData().get(TPMActivityAgreementFields.END_DATE, Long.class);

            if (oldBeginDate == beginDate && oldEndDate == endDate) {
                log.info("beginDate and endDate not change");
                return;
            }
            proofPeriodStr = findProofPeriodOfAgreement(userId, result.getObjectData().toObjectData(), tenantId, activityId);
        }

        //无论是否有新的举证时段，旧的都删除。todo:可以优化，在新建成功后删除
        List<IObjectData> proofPeriodDetails = queryTPMProofTimePeriodDetailByObjectId(tenantId, fieldName, id);
        if (CollectionUtils.isNotEmpty(proofPeriodDetails)) {
            serviceFacade.bulkInvalid(proofPeriodDetails, User.builder().userId(userId).tenantId(tenantId).build());
        }

        if (Objects.isNull(proofPeriodStr)) {
            log.info("proofPeriodStr is empty");
            return;
        }

        doBulkSaveObjectData(userId, result, proofPeriodStr, tenantId, activityId, agreementId);
    }

    /**
     * 查询所有举证时间段数据
     */
    public TPMProofPeriodTime.Result queryAllProofPeriodTimeData(TPMProofPeriodTime.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();
        Tuple2<String, String> idAndFieldApiName = buildQueryTPMProofTimePeriodDetailIdAndFieldApiName(tenantId, arg.getActivityId(), arg.getActivityAgreementId());

        boolean displayActivity = isDisplayActivity(tenantId, arg.getActivityId());
        List<IObjectData> proofPeriodDetails;
        if (TPMGrayUtils.isRioTenant(tenantId)) {
            proofPeriodDetails = queryTPMProofTimePeriodDetailByActivityId(tenantId,
                    idAndFieldApiName.getSecond(), idAndFieldApiName.getFirst());
        } else {
            IObjectData agreement = serviceFacade.findObjectData(User.systemUser(tenantId), idAndFieldApiName.getFirst(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            proofPeriodDetails = queryTPMProofTimePeriodDetailByObjectIdAndAgreementTime(tenantId,
                    idAndFieldApiName.getSecond(), idAndFieldApiName.getFirst(), agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class), agreement.get(TPMActivityAgreementFields.END_DATE, Long.class));
        }


        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return TPMProofPeriodTime.Result.builder()
                    .total(0)
                    .proofPeriods(Lists.newArrayList())
                    .build();
        }

        // 按阶段排序
        proofPeriodDetails = proofPeriodDetails.stream()
                .sorted(Comparator.comparing(c -> c.get(TPMProofTimePeriodDetailFields.STAGE, Double.class)))
                .collect(Collectors.toList());

        // 2. 转换为业务对象
        List<TPMProofPeriodTime.ProofPeriod> proofPeriods =
                proofPeriodDetails.stream()
                        .map(detail -> JSONObject.parseObject(detail.toJsonString(),
                                TPMProofPeriodTime.ProofPeriod.class))
                        .collect(Collectors.toList());
        Map<String, IObjectData> proofPeriodDataMap = proofPeriodDetails.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (o, n) -> n));

        // 3. 查询举证数据
        List<IObjectData> proofs = rebuildProofs(tenantId, queryProof(tenantId, TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, arg.getActivityAgreementId()), proofPeriods);

        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, Lists.newArrayList(ApiNames.TPM_ACTIVITY_PROOF_OBJ, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ));

        if (CollectionUtils.isNotEmpty(proofs)) {
            // 4. 处理举证图片数据
            processProofImages(tenantId, describes, proofPeriods, proofs, displayActivity);
        }

        fillInformation(proofPeriods, proofPeriodDataMap, describes);
        return TPMProofPeriodTime.Result.builder()
                .total(proofPeriodDetails.size())
                .proofPeriods(proofPeriods)
                .size(PROOF_DETAIL_PAGE_SIZE.getOrDefault(tenantId, 50))
                .build();
    }

    /**
     * 查询范围内的举证时间段
     */
    public TPMProofPeriodTime.ProofResult queryRangeProofPeriodTime(TPMProofPeriodTime.Arg arg) {

        ApiContext context = ApiContextManager.getContext();
        String tenantId = context.getTenantId();

        Tuple2<String, String> idAndFieldApiName = buildQueryTPMProofTimePeriodDetailIdAndFieldApiName(tenantId, arg.getActivityId(), arg.getActivityAgreementId());
        List<IObjectData> proofPeriodDetails = queryTPMProofTimePeriodDetailByObjectIdAndTime(tenantId,
                idAndFieldApiName.getSecond(), idAndFieldApiName.getFirst(), arg.getBeginDate(), arg.getEndDate());

        boolean displayActivity = isDisplayActivity(tenantId, arg.getActivityId());
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return TPMProofPeriodTime.ProofResult.builder()
                    .total(0)
                    .proofList(Lists.newArrayList())
                    .build();
        }

        // 按阶段排序
        proofPeriodDetails = proofPeriodDetails.stream()
                .sorted(Comparator.comparing(c -> c.get(TPMProofTimePeriodDetailFields.STAGE, Double.class)))
                .collect(Collectors.toList());

        // 2. 转换为业务对象
        List<TPMProofPeriodTime.ProofPeriod> proofPeriods =
                proofPeriodDetails.stream()
                        .map(detail -> JSONObject.parseObject(detail.toJsonString(),
                                TPMProofPeriodTime.ProofPeriod.class))
                        .collect(Collectors.toList());
        Map<String, IObjectData> proofPeriodDataMap = proofPeriodDetails.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (o, n) -> n));

        Map<String, List<IObjectData>> groupedData = loadAndGroupData(tenantId, arg, TPMActivityProofFields.ACTIVITY_AGREEMENT_ID, arg.getActivityAgreementId());


        List<List<IObjectData>> sortedGroups = sortGroups(groupedData);

        int total = sortedGroups.size();
        int start = (arg.getPage() - 1) * arg.getSize();
        int end = Math.min(start + arg.getSize(), total);

        List<List<IObjectData>> pageProofs = start < total ? sortedGroups.subList(start, end) : Lists.newArrayList();

        List<IObjectData> proofs = pageProofs.stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> describes = serviceFacade.findObjects(tenantId, Lists.newArrayList(ApiNames.TPM_ACTIVITY_PROOF_OBJ, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ));

        if (CollectionUtils.isNotEmpty(proofs)) {
            proofs = proofs.stream()
                    .sorted(Comparator.comparingLong((IObjectData obj) -> obj.get(CommonFields.CREATE_TIME, Long.class))
                            .reversed())
                    .collect(Collectors.toList());

            processProofImages(tenantId, describes, proofPeriods, proofs, displayActivity);
        }

        fillInformation(proofPeriods, proofPeriodDataMap, describes);

        if (CollectionUtils.isEmpty(proofPeriods)) {
            return TPMProofPeriodTime.ProofResult.builder()
                    .total(0)
                    .proofList(Lists.newArrayList())
                    .build();
        }

        return TPMProofPeriodTime.ProofResult.builder()
                .total(total)
                .proofList(proofPeriods.get(0).getProofList())
                .build();

    }

    /**
     * 处理单个举证时段对象的状态更新
     */
    public void processOnePeriodDetail(String tenantId, IObjectData periodDetailObj) {
        String periodDetailId = periodDetailObj.getId();
        log.info("处理举证时段对象，ID: {}", periodDetailId);
        try {
            // 查询与该时段相关的举证数据
            List<IObjectData> proofList = queryProofDataByPeriodDetailId(tenantId, periodDetailId);

            // 获取时段的开始和结束时间
            Long startTime = periodDetailObj.get(TPMProofTimePeriodDetailFields.BEGIN_DATE, Long.class);
            Long endTime = periodDetailObj.get(TPMProofTimePeriodDetailFields.END_DATE, Long.class);

            // 计算各状态值
            String achievementStatus = calculateAchievementStatus(proofList);
            String proofStatus = calculateProofStatus(proofList, startTime, endTime);
            int proofNumber = proofList.size();
            int proofAgreeNumber = countProofByStatus(proofList, TPMActivityProofFields.AUDIT_STATUS__PASS);
            int proofDisagreeNumber = countProofByStatus(proofList, TPMActivityProofFields.AUDIT_STATUS__REJECT);

            // 更新对象
            updatePeriodDetailObj(
                    tenantId,
                    periodDetailObj,
                    achievementStatus,
                    proofStatus,
                    proofNumber,
                    proofAgreeNumber,
                    proofDisagreeNumber
            );

            log.info("举证时段对象更新成功，ID: {}, 达成状态: {}, 举证状态: {}, 举证次数: {}",
                    periodDetailId, achievementStatus, proofStatus, proofNumber);
        } catch (Exception e) {
            log.error("处理举证时段对象失败，ID: {}", periodDetailId, e);
            // 继续处理其他对象，不中断整个流程
        }
    }

    /**
     * 计算合适的协议举证时段
     * 1. 先按stage排序proofPeriods
     * 2. 将proofPeriods内的时间段作为一个整体与[beginDate, endDate]取交集
     * 3. 得出proofPeriods中在[beginDate, endDate]时间段内的所有时间段
     * 4. 如果新的proofPeriods中第一个和最后一个与[beginDate, endDate]有重合，则取[beginDate,
     * endDate]中的时间
     *
     * @param proofPeriods 原始活动举证时段列表
     * @param beginDate    协议开始日期
     * @param endDate      协议结束日期
     * @return 处理后的协议举证时段列表
     */
    private List<TPMDisplayReportService.ProofPeriod> calculateProofPeriods(List<TPMDisplayReportService.ProofPeriod> proofPeriods, long beginDate, long endDate) {
        if (CollectionUtils.isEmpty(proofPeriods)) {
            return new ArrayList<>();
        }

        try {
            List<TPMDisplayReportService.ProofPeriod> sortedList = new ArrayList<>(proofPeriods);
            sortedList.sort(Comparator.comparingInt(TPMDisplayReportService.ProofPeriod::getStage));

            List<TPMDisplayReportService.ProofPeriod> filteredList = new ArrayList<>();
            for (TPMDisplayReportService.ProofPeriod period : sortedList) {
                if (period == null) {
                    continue;
                }

                if (period.getEndDate() <= beginDate || period.getBeginDate() >= endDate) {
                    continue;
                }

                TPMDisplayReportService.ProofPeriod newPeriod = TPMDisplayReportService.ProofPeriod.builder()
                        .stage(period.getStage())
                        .beginDate(period.getBeginDate())
                        .endDate(period.getEndDate())
                        .build();

                filteredList.add(newPeriod);
            }

            if (filteredList.isEmpty()) {
                log.error("filteredList 举证周期列表为空");
                return filteredList;
            }

            TPMDisplayReportService.ProofPeriod firstPeriod = filteredList.get(0);
            if (firstPeriod.getBeginDate() < beginDate) {
                firstPeriod.setBeginDate(beginDate);
            }

            TPMDisplayReportService.ProofPeriod lastPeriod = filteredList.get(filteredList.size() - 1);
            if (lastPeriod.getEndDate() > endDate) {
                lastPeriod.setEndDate(endDate);
            }

            for (int i = 0; i < filteredList.size(); i++) {
                filteredList.get(i).setStage(i + 1);
            }

            return filteredList;
        } catch (Exception e) {
            log.error("计算举证周期时发生异常", e);
            return new ArrayList<>();
        }
    }


    @NotNull
    private String findProofPeriodOfAgreement(String userId, IObjectData agreement, String tenantId, String activityId) {
        String proofPeriodStr;
        IObjectData activity = serviceFacade.findObjectData(User.builder().userId(userId).tenantId(tenantId).build(), activityId, ApiNames.TPM_ACTIVITY_OBJ);
        proofPeriodStr = activity.get(TPMActivityFields.PROOF_PERIOD, String.class, "");
        if (StringUtils.isBlank(proofPeriodStr)) {
            Long oldBeginDate = agreement.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            Long oldEndDate = agreement.get(TPMActivityAgreementFields.END_DATE, Long.class);
            List<LimitSpanEntity> limitDays = getLimitSpanEntities(activity, tenantId);
            if (CollectionUtils.isNotEmpty(limitDays)) {
                //当活动对象没有举证时段时，需要判断活动类型举证节点有时段配置没，有则需要从活动类型获取
                proofPeriodStr = getAgreementProofPeriodStrFromActivityType(oldBeginDate, oldEndDate, limitDays, tenantId);
            } else {
                //当活动对象和活动类型都没有举证时段时，直接用协议的时段作为举证时段
                proofPeriodStr = getAgreementProofPeriodFromAgreementObj(oldBeginDate, oldEndDate);
            }
        }
        return proofPeriodStr;
    }

    private List<LimitSpanEntity> getLimitSpanEntities(IObjectData activity, String tenantId) {
        String activityType = activity.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        ActivityTypeExt activityTypeExt = activityTypeManager.find(tenantId, activityType);
        ActivityProofConfigEntity activityProofConfigEntity = activityTypeExt.proofConfig();
        if (Objects.isNull(activityProofConfigEntity)) {
            return Lists.newArrayList();
        }
        ActivityProofFrequencyConfigEntity frequencyConfig = activityProofConfigEntity.getFrequencyConfig();
        if (Objects.isNull(frequencyConfig)) {
            return Lists.newArrayList();
        }
        List<LimitSpanEntity> limitDays = frequencyConfig.getLimitDays();
        if (CollectionUtils.isEmpty(limitDays)) {
            return Lists.newArrayList();
        }
        return limitDays;
    }


    private String getAgreementProofPeriodStrFromActivityType(Long oldBeginDate, Long oldEndDate, List<LimitSpanEntity> limitDays, String tenantId) {
        // 计算时间范围内的月份的from-to组合
        List<TPMDisplayReportService.ProofPeriod> generatedPeriods = Lists.newLinkedList();
        for (LimitSpanEntity limitSpanEntity : limitDays) {
            String from = limitSpanEntity.getFrom();
            String to = limitSpanEntity.getTo();

            // 将oldBeginDate转为日历对象便于操作
            Calendar beginCal = Calendar.getInstance();
            beginCal.setTimeInMillis(oldBeginDate);

            // 将oldEndDate转为日历对象便于操作
            Calendar endCal = Calendar.getInstance();
            endCal.setTimeInMillis(oldEndDate);

            // 遍历每个月
            while (!beginCal.after(endCal)) {
                int year = beginCal.get(Calendar.YEAR);
                int month = beginCal.get(Calendar.MONTH);

                // 计算当前月的from日期
                Calendar fromCal = Calendar.getInstance();
                fromCal.set(year, month, Integer.parseInt(from), 0, 0, 0);
                fromCal.set(Calendar.MILLISECOND, 0);

                // 计算当前月的to日期
                Calendar toCal = Calendar.getInstance();
                toCal.set(year, month, Integer.parseInt(to), 23, 59, 59);
                toCal.set(Calendar.MILLISECOND, 999);

                // 检查是否与整体时间段有交集
                if (!(toCal.getTimeInMillis() < oldBeginDate || fromCal.getTimeInMillis() > oldEndDate)) {
                    // 确定实际的开始和结束时间（取交集）
                    long periodBegin = Math.max(fromCal.getTimeInMillis(), oldBeginDate);
                    long periodEnd = Math.min(toCal.getTimeInMillis(), oldEndDate);

                    // 创建ProofPeriod对象并添加到列表
                    TPMDisplayReportService.ProofPeriod period = TPMDisplayReportService.ProofPeriod.builder()
                            .beginDate(periodBegin)
                            .endDate(periodEnd)
                            .build();

                    generatedPeriods.add(period);
                }

                // 移动到下个月的1号
                beginCal.add(Calendar.MONTH, 1);
                beginCal.set(Calendar.DAY_OF_MONTH, 1);
            }
        }

        if (generatedPeriods.isEmpty()) {
            return "";
        }

        // 根据beginDate将generatedPeriods排序,并赋值stage
        generatedPeriods.sort(Comparator.comparingLong(TPMDisplayReportService.ProofPeriod::getBeginDate));
        for (int i = 0; i < generatedPeriods.size(); i++) {
            generatedPeriods.get(i).setStage(i + 1);
        }

        return JSONArray.toJSONString(generatedPeriods);
    }

    @NotNull
    private static String getAgreementProofPeriodFromAgreementObj(Long oldBeginDate, Long oldEndDate) {
        TPMDisplayReportService.ProofPeriod period = TPMDisplayReportService.ProofPeriod.builder()
                .stage(1)
                .beginDate(oldBeginDate)
                .endDate(oldEndDate)
                .build();
        return JSONArray.toJSONString(Lists.newArrayList(period));
    }

    private List<IObjectData> buildProofPeriodDetail(String tenantId, String userId, String activityId, String agreementId, List<TPMDisplayReportService.ProofPeriod> proofPeriods, IObjectData toObjectData) {
        List<IObjectData> proofPeriodDetailList = Lists.newLinkedList();
        if (StringUtils.isNotEmpty(agreementId)) {
            long beginDate = toObjectData.get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
            long endDate = toObjectData.get(TPMActivityAgreementFields.END_DATE, Long.class);
            proofPeriods = calculateProofPeriods(proofPeriods, beginDate, endDate);
        }
        if (CollectionUtils.isNotEmpty(proofPeriods)) {
            for (TPMDisplayReportService.ProofPeriod proofPeriod : proofPeriods) {
                IObjectData objectData = buildDefaultIObjectData(userId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, toObjectData);

                if (StringUtils.isNotEmpty(agreementId)) {
                    objectData.set(TPMProofTimePeriodDetailFields.AGREEMENT_ID, agreementId);
                }
                objectData.set(TPMProofTimePeriodDetailFields.ACTIVITY_ID, activityId);
                objectData.set(TPMProofTimePeriodDetailFields.STAGE, proofPeriod.getStage());
                objectData.set(TPMProofTimePeriodDetailFields.BEGIN_DATE, proofPeriod.getBeginDate());
                objectData.set(TPMProofTimePeriodDetailFields.END_DATE, proofPeriod.getEndDate());
                objectData.set(TPMProofTimePeriodDetailFields.PROOF_AGREE_NUMBER, 0);
                objectData.set(TPMProofTimePeriodDetailFields.PROOF_DISAGREE_NUMBER, 0);
                objectData.set(TPMProofTimePeriodDetailFields.PROOF_NUMBER, 0);
                objectData.set(TPMProofTimePeriodDetailFields.SOURCE_TYPE, SourceType.getValueByName(toObjectData.getDescribeApiName()));
                proofPeriodDetailList.add(objectData);
            }
        }
        return proofPeriodDetailList;
    }


    private Boolean existTPMProofTimePeriodDetailObjectDescribe(String tenantId) {
        try {
            IObjectDescribe tpmProofTimePeriodDetailDescribe = serviceFacade.findObject(tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ);
            return Objects.nonNull(tpmProofTimePeriodDetailDescribe);
        } catch (ObjectDefNotFoundError e) {
            log.info("tpmProofTimePeriodDetailDescribe is empty");
            return false;
        }
    }

    private List<List<IObjectData>> sortGroups(Map<String, List<IObjectData>> groupedData) {
        // 将Map转换为List并排序
        List<List<IObjectData>> result = Lists.newArrayList();
        groupedData.forEach((k, v) -> {
            result.add(v);
        });
        return result;
    }


    /**
     * 处理举证图片数据
     */
    private void processProofImages(String tenantId,
                                    Map<String, IObjectDescribe> describes,
                                    List<TPMProofPeriodTime.ProofPeriod> proofPeriods,
                                    List<IObjectData> proofs,
                                    boolean displayActivity) {

        // 1. 查询举证图片
        List<IObjectData> proofDisplayImgs = queryTPMActivityProofDisplayImgsByProofIds(tenantId, proofs.stream().map(DBRecord::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(proofDisplayImgs)) {
            return;
        }

        // 2. 构建举证ID到图片的映射
        Map<String, List<IObjectData>> proofIdDisplayImgsMap = proofDisplayImgs.stream()
                .collect(Collectors
                        .groupingBy(img -> img.get(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID, String.class)));

        // 3. 收集图片关联的举证ID和活动项目ID
        List<String> imgProofIds = proofDisplayImgs.stream()
                .map(img -> img.get(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID, String.class))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<String> activityItemIds = proofDisplayImgs.stream()
                .map(img -> img.get(TPMActivityProofDisplayImgFields.ACTIVITY_ITEM_ID, String.class))
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<String> displayFormIds = proofDisplayImgs.stream()
                .map(img -> img.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class))
                .distinct()
                .collect(Collectors.toList());

        // 4. 查询举证详情数据
        List<IObjectData> proofDetails = queryTPMActivityProofDetailsByProofId(
                tenantId, imgProofIds, displayFormIds, activityItemIds);

        // 5. 构建详情数据索引
        Map<String, Map<String, List<IObjectData>>> proofDetailIndex = buildProofDetailIndex(proofDetails);

        // 6. 获取陈列形式和活动项目名称映射
        Map<String, String> displayFormNames = getDisplayFormNames(displayFormIds, tenantId);
        Map<String, String> activityItemNames = getActivityItemNames(activityItemIds, tenantId);

        // 7. 为每个举证时段分配相应的举证数据
        assignProofsToTimePeriods(tenantId, describes, proofPeriods, proofs, proofIdDisplayImgsMap,
                proofDetailIndex, displayFormNames, activityItemNames, displayActivity);
    }

    private void assignProofsToTimePeriods(
            String tenantId,
            Map<String, IObjectDescribe> describes,
            List<TPMProofPeriodTime.ProofPeriod> proofPeriods,
            List<IObjectData> proofs,
            Map<String, List<IObjectData>> proofIdDisplayImgsMap,
            Map<String, Map<String, List<IObjectData>>> proofDetailIndex,
            Map<String, String> displayFormNames,
            Map<String, String> activityItemNames,
            boolean displayActivity) {


        for (TPMProofPeriodTime.ProofPeriod period : proofPeriods) {
            long beginDate = period.getBeginDate();
            long endDate = period.getEndDate();

            // 找出当前时段内的所有举证
            List<IObjectData> periodProofs = proofs.stream()
                    .filter(proof -> {
                        Long createTime = proof.get(CommonFields.CREATE_TIME, Long.class);
                        return beginDate < createTime && createTime <= endDate;
                    })
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(periodProofs)) {
                continue;
            }

            // 处理时段内的举证数据
            List<TPMProofPeriodTime.TPMActivityProof> tpmActivityProofs = processProofsInPeriod(tenantId, describes, periodProofs,
                    proofIdDisplayImgsMap, proofDetailIndex,
                    displayFormNames, activityItemNames, displayActivity);

            period.setProofList(tpmActivityProofs);
        }
    }


    /**
     * 处理时段内的举证数据
     */
    private List<TPMProofPeriodTime.TPMActivityProof> processProofsInPeriod(
            String tenantId,
            Map<String, IObjectDescribe> describes,
            List<IObjectData> periodProofs,
            Map<String, List<IObjectData>> proofIdDisplayImgsMap,
            Map<String, Map<String, List<IObjectData>>> proofDetailIndex,
            Map<String, String> displayFormNames,
            Map<String, String> activityItemNames,
            boolean displayActivity) {

        List<TPMProofPeriodTime.TPMActivityProof> result = new ArrayList<>();

        // 分成两组：非访问举证和访问举证
        List<IObjectData> notVisitProofs = periodProofs.stream()
                .filter(obj -> StringUtils.isBlank(obj.get(TPMActivityProofFields.VISIT_ID, String.class)))
                .collect(Collectors.toList());

        // 处理非访问举证
        for (IObjectData proof : notVisitProofs) {
            TPMProofPeriodTime.TPMActivityProof tpmActivityProof = createActivityProof(
                    tenantId, describes, proof, proofIdDisplayImgsMap, proofDetailIndex, displayFormNames, activityItemNames, displayActivity);

            if (tpmActivityProof != null) {
                result.add(tpmActivityProof);
            }
        }

        Map<String, List<IObjectData>> visitProofMap = periodProofs.stream()
                .filter(obj -> StringUtils.isNotBlank(obj.get(TPMActivityProofFields.VISIT_ID, String.class)))
                .collect(Collectors.groupingBy(
                        obj -> obj.get(TPMActivityProofFields.VISIT_ID, String.class),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        visitProofMap.forEach((visitId, visitProofs) -> {
            if (CollectionUtils.isEmpty(visitProofs)) {
                return;
            }

            // 创建访问举证基本信息
            IObjectData firstProof = visitProofs.get(0);
            TPMProofPeriodTime.StatusVO proofStatusVO = TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_ACTIVITY_PROOF_OBJ), TPMActivityProofFields.AI_IDENTIFY_STATUS, firstProof);
            TPMProofPeriodTime.TPMActivityProof tpmActivityProof = TPMProofPeriodTime.TPMActivityProof.builder()
                    .id(firstProof.getId())
                    .checkinId(firstProof.get(TPMActivityProofFields.VISIT_ID, String.class))
                    .statusVO(proofStatusVO)
                    .isDisplayActivity(displayActivity)
                    .isShowReportLink(TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(proofStatusVO.getValue()) && displayActivity)
                    .groupIds(visitProofs.stream().map(DBRecord::getId).collect(Collectors.toList()))
                    .createTime(firstProof.get(TPMActivityProofFields.CREATE_TIME, Long.class))
                    .build();

            boolean openAI = super.openAi(tenantId);
            if (Boolean.TRUE.equals(openAI)) {
                if (!TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(firstProof.get(TPMActivityProofFields.AI_IDENTIFY_STATUS, String.class))) {
                    tpmActivityProof.setDisplayStatusVO(proofStatusVO);
                }
            } else {
                tpmActivityProof.setDisplayStatusVO(TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_ACTIVITY_PROOF_OBJ), TPMActivityProofFields.AUDIT_STATUS, firstProof));
            }

            // 收集所有图片
            List<TPMProofPeriodTime.TPMActivityProofDisplayImg> allImages = new ArrayList<>();

            for (IObjectData proof : visitProofs) {
                List<TPMProofPeriodTime.TPMActivityProofDisplayImg> proofImages = collectProofImages(tenantId, describes, proof,
                        proofIdDisplayImgsMap, proofDetailIndex,
                        displayFormNames, activityItemNames);

                allImages.addAll(proofImages);
            }

            if (!allImages.isEmpty()) {
                tpmActivityProof.setProofItemList(allImages);
                result.add(tpmActivityProof);
            }
        });

        return result;
    }


    /**
     * 收集举证图片数据
     */
    private List<TPMProofPeriodTime.TPMActivityProofDisplayImg> collectProofImages(
            String tenantId,
            Map<String, IObjectDescribe> describes,
            IObjectData proof,
            Map<String, List<IObjectData>> proofIdDisplayImgsMap,
            Map<String, Map<String, List<IObjectData>>> proofDetailIndex,
            Map<String, String> displayFormNames,
            Map<String, String> activityItemNames) {

        String proofId = proof.getId();
        List<TPMProofPeriodTime.TPMActivityProofDisplayImg> result = new ArrayList<>();

        List<IObjectData> displayImgs = proofIdDisplayImgsMap.getOrDefault(proofId, Collections.emptyList());
        if (CollectionUtils.isEmpty(displayImgs)) {
            return result;
        }

        for (IObjectData displayImg : displayImgs) {
            String displayFormId = displayImg.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID, String.class);
            String activityItemId = displayImg.get(TPMActivityProofDisplayImgFields.ACTIVITY_ITEM_ID, String.class);

            // 构建详情数据
            List<TPMProofPeriodTime.TPMActivityProofDetailData> detailData = buildProofDetailData(proofId,
                    displayFormId, activityItemId, proofDetailIndex);
            TPMProofPeriodTime.StatusVO statusVO = null;
            if (super.openAi(tenantId) && TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(proof.get(TPMActivityProofFields.AI_IDENTIFY_STATUS, String.class))) {
                statusVO = TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ), TPMActivityProofDisplayImgFields.AUDIT_STATUS, displayImg);
            }
            // 创建展示图片对象
            TPMProofPeriodTime.TPMActivityProofDisplayImg displayImgObj = TPMProofPeriodTime.TPMActivityProofDisplayImg
                    .builder()
                    .id(displayImg.getId())
                    .image(TPMProofPeriodTime.ImageVO.extractImageVO(displayImg))
                    .displayFormId(StringUtils.isNotBlank(displayFormId) ? displayFormId : activityItemId)
                    .displayFormName(
                            StringUtils.isNotBlank(displayFormId) ? displayFormNames.getOrDefault(displayFormId, "")
                                    : activityItemNames.getOrDefault(activityItemId, ""))
                    .auditStatus(statusVO)
                    .activityProofDetail(detailData)
                    .build();

            result.add(displayImgObj);
        }

        return result;
    }

    /**
     * 创建活动举证对象
     */
    private TPMProofPeriodTime.TPMActivityProof createActivityProof(
            String tenantId,
            Map<String, IObjectDescribe> describes,
            IObjectData proof,
            Map<String, List<IObjectData>> proofIdDisplayImgsMap,
            Map<String, Map<String, List<IObjectData>>> proofDetailIndex,
            Map<String, String> displayFormNames,
            Map<String, String> activityItemNames,
            boolean displayActivity) {

        String proofId = proof.getId();
        TPMProofPeriodTime.StatusVO proofStatusVO = TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_ACTIVITY_PROOF_OBJ), TPMActivityProofFields.AI_IDENTIFY_STATUS, proof);
        TPMProofPeriodTime.TPMActivityProof tpmActivityProof = TPMProofPeriodTime.TPMActivityProof.builder()
                .id(proofId)
                .statusVO(proofStatusVO)
                .isDisplayActivity(displayActivity)
                .isShowReportLink(TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(proofStatusVO.getValue()) && displayActivity)
                .groupIds(Lists.newArrayList(proofId))
                .createTime(proof.get(TPMActivityProofFields.CREATE_TIME, Long.class))
                .build();

        boolean openAI = super.openAi(tenantId);
        if (Boolean.TRUE.equals(openAI)) {
            if (!TPMActivityProofFields.AI_IDENTIFY_STATUS_IDENTIFIED.equals(proof.get(TPMActivityProofFields.AI_IDENTIFY_STATUS, String.class))) {
                tpmActivityProof.setDisplayStatusVO(proofStatusVO);
            }
        } else {
            tpmActivityProof.setDisplayStatusVO(TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_ACTIVITY_PROOF_OBJ), TPMActivityProofFields.AUDIT_STATUS, proof));
        }

        List<TPMProofPeriodTime.TPMActivityProofDisplayImg> proofImages = collectProofImages(tenantId, describes, proof,
                proofIdDisplayImgsMap, proofDetailIndex,
                displayFormNames, activityItemNames);

        if (!proofImages.isEmpty()) {
            tpmActivityProof.setProofItemList(proofImages);
            return tpmActivityProof;
        }

        return null;
    }


    private void fillInformation(List<TPMProofPeriodTime.ProofPeriod> proofPeriods, Map<String, IObjectData> proofPeriodDataMap, Map<String, IObjectDescribe> describes) {
        long now = System.currentTimeMillis();
        for (TPMProofPeriodTime.ProofPeriod period : proofPeriods) {
            long beginDate = period.getBeginDate();
            long endDate = period.getEndDate();

            TPMProofPeriodTime.StatusVO proofPeriodStatusVO = TPMProofPeriodTime.StatusVO.of(describes.get(ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ), TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS, proofPeriodDataMap.get(period.getId()));
            period.setAchievementStatusVO(proofPeriodStatusVO);
            if (beginDate <= now && now <= endDate) {
                period.setCurrentPeriod(true);
            }

        }
    }

    /**
     * 构建举证详情数据
     */
    private List<TPMProofPeriodTime.TPMActivityProofDetailData> buildProofDetailData(
            String proofId,
            String displayFormId,
            String activityItemId,
            Map<String, Map<String, List<IObjectData>>> proofDetailIndex) {

        List<TPMProofPeriodTime.TPMActivityProofDetailData> result = new ArrayList<>();

        Map<String, List<IObjectData>> proofIdDetailsMap = proofDetailIndex.getOrDefault(proofId,
                Collections.emptyMap());
        if (proofIdDetailsMap.isEmpty()) {
            return result;
        }

        String lookupKey = StringUtils.isBlank(displayFormId) ? "item" + "_" + activityItemId
                : "display" + "_" + displayFormId;

        List<IObjectData> details = proofIdDetailsMap.getOrDefault(lookupKey, Collections.emptyList());

        for (IObjectData detail : details) {
            result.add(TPMProofPeriodTime.TPMActivityProofDetailData.builder()
                    .id(detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class))
                    .label(detail.get(TPMActivityProofDetailFields.PROOF_ITEM, String.class))
                    .value(TPMProofPeriodTime.TPMActivityProofDetailData.stripTrailingZeros(BigDecimal.valueOf(detail.get(TPMActivityProofDetailFields.AMOUNT, Double.class))))
                    .standardValue(TPMProofPeriodTime.TPMActivityProofDetailData.stripTrailingZeros(BigDecimal.valueOf(detail.get(TPMActivityProofDetailFields.PROOF_DETAIL_AMOUNT_STANDARD, Double.class))))
                    .build());
        }

        return result;
    }

    private List<IObjectData> rebuildProofs(String tenantId, List<IObjectData> proofs,
                                            List<TPMProofPeriodTime.ProofPeriod> proofPeriods) {
        List<IObjectData> filteredProofs = new ArrayList<>();
        Integer limit = PROOF_DATA_LIMIT.getOrDefault(tenantId, 3);
        for (TPMProofPeriodTime.ProofPeriod period : proofPeriods) {
            long beginDate = period.getBeginDate();
            long endDate = period.getEndDate();
            // 找出当前时段内的所有举证
            List<IObjectData> periodProofs = proofs.stream()
                    .filter(proof -> {
                        Long createTime = proof.get(CommonFields.CREATE_TIME, Long.class);
                        return createTime != null && beginDate <= createTime && createTime <= endDate;
                    })
                    .limit(limit)
                    .collect(Collectors.toList());

            filteredProofs.addAll(periodProofs);
        }

        return filteredProofs;
    }

    private boolean isDisplayActivity(String tenantId, String activityId) {
        ActivityTypeExt activityTypeExt = activityTypeManager.findByActivityId(tenantId, activityId);
        String templateId = activityTypeExt.get().getTemplateId();
        log.info("activityTypeExt templateId is {}", templateId);
        // 协议类的陈列铺货类型
        return !Strings.isNullOrEmpty(templateId) && templateId.contains("display") && activityTypeExt.agreementNode() != null;
    }


    /**
     * 获取陈列形式名称映射
     */
    private Map<String, String> getDisplayFormNames(List<String> displayFormIds, String tenantId) {

        if (CollectionUtils.isEmpty(displayFormIds)) {
            return new HashMap<>();
        }

        return serviceFacade.findNameByIds(User.systemUser(tenantId), ApiNames.DISPLAY_FORM_OBJ, displayFormIds);
    }


    private Map<String, String> getActivityItemNames(List<String> activityItemIds, String tenantId) {
        if (CollectionUtils.isEmpty(activityItemIds)) {
            return new HashMap<>();
        }

        return serviceFacade.findNameByIds(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_ITEM_OBJ, activityItemIds);
    }

    /**
     * 构建举证详情索引
     */
    private Map<String, Map<String, List<IObjectData>>> buildProofDetailIndex(List<IObjectData> proofDetails) {
        if (CollectionUtils.isEmpty(proofDetails)) {
            return new HashMap<>();
        }

        return proofDetails.stream()
                .collect(Collectors.groupingBy(
                        detail -> detail.get(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID, String.class),
                        Collectors.groupingBy(detail -> {
                            String displayFormId = detail.get(TPMActivityProofDetailFields.DISPLAY_FORM_ID,
                                    String.class);
                            if (StringUtils.isNotBlank(displayFormId)) {
                                return "display" + "_" + displayFormId;
                            } else {
                                return "item" + "_"
                                        + detail.get(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID, String.class);
                            }
                        })));
    }


    private Tuple2<String, String> buildQueryTPMProofTimePeriodDetailIdAndFieldApiName(String tenantId, String activityId, String activityAgreementId) {
        if (TPMGrayUtils.isRioTenant(tenantId)) {
            return new Tuple2<>(activityId, TPMProofTimePeriodDetailFields.ACTIVITY_ID);
        } else {
            return new Tuple2<>(activityAgreementId, TPMProofTimePeriodDetailFields.AGREEMENT_ID);
        }
    }

    private Map<String, List<IObjectData>> loadAndGroupData(String tenantId, TPMProofPeriodTime.Arg arg,
                                                            String fieldName, String value) {

        List<IObjectData> allData = loadData(tenantId, arg, fieldName, value);
        allData = allData.stream()
                .sorted(Comparator.comparingLong((IObjectData obj) -> obj.get(CommonFields.CREATE_TIME, Long.class))
                        .reversed())
                .collect(Collectors.toList());

        Map<String, List<IObjectData>> groupedData = new LinkedHashMap<>();
        for (IObjectData data : allData) {
            String key = StringUtils.isNotBlank(data.get(TPMActivityProofFields.VISIT_ID, String.class))
                    ? data.get(TPMActivityProofFields.VISIT_ID, String.class)
                    : data.get(CommonFields.ID, String.class);
            groupedData.put(key, Lists.newLinkedList());
        }

        for (IObjectData data : allData) {
            String key = StringUtils.isNotBlank(data.get(TPMActivityProofFields.VISIT_ID, String.class))
                    ? data.get(TPMActivityProofFields.VISIT_ID, String.class)
                    : data.get(CommonFields.ID, String.class);

            groupedData.get(key).add(data);

            groupedData.get(key).sort(Comparator.comparing((IObjectData d) -> d.get(CommonFields.CREATE_TIME, Long.class)).reversed());
        }

        return groupedData;
    }

    private void doBulkSaveObjectData(String userId, BaseObjectSaveAction.Result result, String proofPeriodStr, String tenantId, String activityId, String agreementId) {
        if (proofPeriodStr.contains("type") && proofPeriodStr.contains("ALL")) {
            return;
        }
        JSONArray proofPeriodArray = JSONArray.parseArray(proofPeriodStr);
        if (CollectionUtils.isNotEmpty(proofPeriodArray)) {
            List<TPMDisplayReportService.ProofPeriod> proofPeriods = JSONArray.parseArray(proofPeriodStr, TPMDisplayReportService.ProofPeriod.class);
            List<IObjectData> proofPeriodDetailList = buildProofPeriodDetail(tenantId, userId, activityId, agreementId, proofPeriods, result.getObjectData().toObjectData());
            serviceFacade.bulkSaveObjectData(proofPeriodDetailList, User.builder().userId(userId).tenantId(tenantId).build());
        }
    }


    /**
     * 计算达成状态
     * 达成状态规则：
     * 1. 未执行 - 无举证数据时的默认状态
     * 2. 待检核 - 所有举证数据都是待检核状态
     * 3. 同意 - 过滤排期状态后，剩余状态只有通过
     * 4. 不同意 - 过滤排期状态后，剩余状态只有拒绝
     * 5. 部分同意 - 过滤排期状态后，有多种状态(通过/拒绝)
     *
     * @param proofList 举证数据列表
     * @return 达成状态代码
     */
    private String calculateAchievementStatus(List<IObjectData> proofList) {
        // 如果没有举证数据，返回"未执行"状态
        if (CollectionUtils.isEmpty(proofList)) {
            return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__UNEXECUTED;
        }

        // 收集所有举证的审核状态
        Set<String> auditStatusSet = proofList.stream()
                .map(proof -> proof.get(TPMActivityProofFields.AUDIT_STATUS, String.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 有举证数据，但没有检核状态的数据，认为是待检核状态
        if (CollectionUtils.isEmpty(auditStatusSet)) {
            return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__SCHEDULE;
        }

        // 如果所有状态都是"待检核"，返回"待检核"状态
        if (auditStatusSet.size() == 1 && auditStatusSet.contains(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE)) {
            return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__SCHEDULE;
        }

        // 创建非待检核状态的集合
        Set<String> nonScheduleStatusSet = new HashSet<>(auditStatusSet);
        nonScheduleStatusSet.remove(TPMActivityProofFields.AUDIT_STATUS__SCHEDULE);

        // 如果移除待检核后只剩一种状态
        if (nonScheduleStatusSet.size() == 1) {
            String status = nonScheduleStatusSet.iterator().next();

            if (TPMActivityProofFields.AUDIT_STATUS__PASS.equals(status)) {
                return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__AGREE;
            } else if (TPMActivityProofFields.AUDIT_STATUS__REJECT.equals(status)) {
                return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__DISAGREE;
            } else if (TPMActivityProofFields.AUDIT_STATUS__PARTIALLY.equals(status)) {
                return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__PARTIALLY;
            } else {
                return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__SCHEDULE;
            }
        }

        // 返回"部分同意"状态
        return TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS__PARTIALLY;
    }

    /**
     * 计算举证状态
     * 未开始 - 当前时间早于阶段开始时间
     * 未举证 - 该时段内无举证数据
     * 已举证 - 该时段内有举证数据
     * 已逾期 - 该时段结束后仍未产生举证数据
     */
    private String calculateProofStatus(List<IObjectData> proofList,
                                        Long startTime, Long endTime) {
        Long currentTime = System.currentTimeMillis();
        if (currentTime < startTime) {
            return TPMProofTimePeriodDetailFields.PROOF_STATUS__SCHEDULE;
        }

        if (proofList.isEmpty()) {
            if (currentTime > endTime) {
                return TPMProofTimePeriodDetailFields.PROOF_STATUS__EXPECT;
            } else {
                return TPMProofTimePeriodDetailFields.PROOF_STATUS__NO_PROOF;
            }
        }

        return TPMProofTimePeriodDetailFields.PROOF_STATUS__PROOF;
    }

    /**
     * 统计特定状态的举证数量
     */
    private int countProofByStatus(List<IObjectData> proofList, String status) {
        int count = 0;
        for (IObjectData proof : proofList) {
            if (status.equals(proof.get(TPMActivityProofFields.AUDIT_STATUS, String.class))) {
                count++;
            }
        }
        return count;
    }

    /**
     * 更新举证时段对象状态
     */
    private void updatePeriodDetailObj(
            String tenantId,
            IObjectData periodDetailObj,
            String achievementStatus,
            String proofStatus,
            int proofNumber,
            int proofAgreeNumber,
            int proofDisagreeNumber) {
        try {
            Map<String, Object> updateData = new HashMap<>();
            updateData.put(TPMProofTimePeriodDetailFields.ACHIEVEMENT_STATUS, achievementStatus);
            updateData.put(TPMProofTimePeriodDetailFields.PROOF_STATUS, proofStatus);
            updateData.put(TPMProofTimePeriodDetailFields.PROOF_NUMBER, proofNumber);
            updateData.put(TPMProofTimePeriodDetailFields.PROOF_AGREE_NUMBER, proofAgreeNumber);
            updateData.put(TPMProofTimePeriodDetailFields.PROOF_DISAGREE_NUMBER, proofDisagreeNumber);

            serviceFacade.updateWithMap(
                    User.systemUser(tenantId),
                    periodDetailObj,
                    updateData
            );
        } catch (Exception e) {
            log.error("更新举证时段对象失败, ID: {}", periodDetailObj.getId(), e);
            throw new RuntimeException("update proof period error", e);
        }
    }

    private List<IObjectData> loadData(String tenantId, TPMProofPeriodTime.Arg arg, String fieldName, String value) {
        // 1. 查询所有数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter beginFilter = new Filter();
        beginFilter.setFieldName(CommonFields.CREATE_TIME);
        beginFilter.setOperator(Operator.GTE);
        beginFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getBeginDate())));

        Filter endFilter = new Filter();
        endFilter.setFieldName(CommonFields.CREATE_TIME);
        endFilter.setOperator(Operator.LTE);
        endFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getEndDate())));


        Filter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(value));

        query.setFilters(Lists.newArrayList(beginFilter, endFilter, filter));

        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                query,
                TPMActivityProofFields.ALL);
    }

    private List<IObjectData> queryTPMProofTimePeriodDetailByActivityId(String tenantId, String fieldName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(fieldName);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter emptyFilter = new Filter();
        emptyFilter.setFieldName(TPMProofTimePeriodDetailFields.AGREEMENT_ID);
        emptyFilter.setOperator(Operator.IS);
        emptyFilter.setFieldValues(Lists.newArrayList());

        query.setFilters(Lists.newArrayList(refrenceIdFilter, emptyFilter));

        List<IObjectData> proofPeriodDetails = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }
        return proofPeriodDetails;
    }

    private List<IObjectData> queryTPMProofTimePeriodDetailByObjectIdAndAgreementTime(String tenantId, String fieldName, String id, long agreementBeginTime, long agreementEndTime) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(fieldName);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));


        Filter beginFilter = new Filter();
        beginFilter.setFieldName(TPMProofTimePeriodDetailFields.BEGIN_DATE);
        beginFilter.setOperator(Operator.LTE);
        beginFilter.setFieldValues(Lists.newArrayList(String.valueOf(agreementEndTime)));

        Filter endFilter = new Filter();
        endFilter.setFieldName(TPMProofTimePeriodDetailFields.END_DATE);
        endFilter.setOperator(Operator.GTE);
        endFilter.setFieldValues(Lists.newArrayList(String.valueOf(agreementBeginTime)));

        query.setFilters(Lists.newArrayList(refrenceIdFilter, beginFilter, endFilter));
        List<IObjectData> proofPeriodDetails = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query, TPMProofTimePeriodDetailFields.ALL);
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }
        return proofPeriodDetails;
    }


    private List<IObjectData> queryProof(String tenantId, String fieldName, String value) {
        int offset = 0;
        int limit = 500; // 每次加载500条数据
        List<IObjectData> finalProofs = Lists.newArrayList();
        List<IObjectData> batchData;
        do {
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(limit);
            query.setOffset(offset);
            query.setNeedReturnQuote(false);
            query.setNeedReturnCountNum(false);
            query.setSearchSource("db");

            Filter filter = new Filter();
            filter.setFieldName(fieldName);
            filter.setOperator(Operator.EQ);
            filter.setFieldValues(Lists.newArrayList(value));

            query.setFilters(Lists.newArrayList(filter));

            batchData = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ, query,
                    TPMActivityProofFields.ALL);
            if (CollectionUtils.isNotEmpty(batchData)) {
                finalProofs.addAll(batchData);
            }
            // 处理batchData
            offset += limit;
        } while (!batchData.isEmpty());


        return finalProofs.stream().sorted(Comparator.comparingLong((IObjectData obj) ->
                obj.get(CommonFields.CREATE_TIME, Long.class)).reversed()).collect(Collectors.toList());
    }

    private List<IObjectData> queryTPMActivityProofDisplayImgsByProofIds(String tenantId, List<String> proofIds) {
        if (CollectionUtils.isEmpty(proofIds)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityIdFilter = new Filter();
        activityIdFilter.setFieldName(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID);
        activityIdFilter.setOperator(Operator.IN);
        activityIdFilter.setFieldValues(proofIds);

        query.setFilters(Lists.newArrayList(activityIdFilter));

        // ACTIVITY_PROOF_ID + DISPLAY_FORM_ID + ACTIVITY_ITEM_ID 共同组成一组举证项目
        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DISPLAY_IMG_OBJ, query);
    }

    private List<IObjectData> queryTPMActivityProofDetailsByProofId(String tenantId,
                                                                    List<String> imgProofIds,
                                                                    List<String> displayFormIds, List<String> activityItems) {
        if (CollectionUtils.isEmpty(imgProofIds) && CollectionUtils.isEmpty(activityItems)) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter activityProofIdFilter = new Filter();
        activityProofIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_PROOF_ID);
        activityProofIdFilter.setOperator(Operator.IN);
        activityProofIdFilter.setFieldValues(imgProofIds);

        Filter activityItemIdFilter = new Filter();
        activityItemIdFilter.setFieldName(TPMActivityProofDetailFields.ACTIVITY_ITEM_ID);
        activityItemIdFilter.setOperator(Operator.IN);
        activityItemIdFilter.setFieldValues(activityItems);

        Filter displlayFormFilter = new Filter();
        displlayFormFilter.setFieldName(TPMActivityProofDetailFields.DISPLAY_FORM_ID);
        displlayFormFilter.setOperator(Operator.IN);
        displlayFormFilter.setFieldValues(displayFormIds);

        Wheres wheres1 = new Wheres();
        wheres1.setFilters(Lists.newArrayList(activityProofIdFilter, activityItemIdFilter));

        Wheres wheres2 = new Wheres();
        wheres2.setFilters(Lists.newArrayList(activityProofIdFilter, displlayFormFilter));

        query.setFilters(Lists.newArrayList());
        query.setWheres(Lists.newArrayList(wheres1, wheres2));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_DETAIL_OBJ, query, TPMActivityProofDetailFields.ALL);
    }

    private List<IObjectData> queryTPMProofTimePeriodDetailByObjectIdAndTime(String tenantId, String fieldName, String id, long begin, long end) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(fieldName);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        Filter beginDateFilter = new Filter();
        beginDateFilter.setFieldName(TPMProofTimePeriodDetailFields.BEGIN_DATE);
        beginDateFilter.setOperator(Operator.GTE);
        beginDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(begin)));

        Filter endDateFilter = new Filter();
        endDateFilter.setFieldName(TPMProofTimePeriodDetailFields.END_DATE);
        endDateFilter.setOperator(Operator.LTE);
        endDateFilter.setFieldValues(Lists.newArrayList(String.valueOf(end)));

        Filter emptyFilter = new Filter();
        emptyFilter.setFieldName(TPMProofTimePeriodDetailFields.AGREEMENT_ID);
        emptyFilter.setOperator(Operator.IS);
        emptyFilter.setFieldValues(Lists.newArrayList());

        query.setFilters(Lists.newArrayList(refrenceIdFilter, emptyFilter));

        query.setFilters(Lists.newArrayList(refrenceIdFilter, beginDateFilter, endDateFilter, emptyFilter));


        List<IObjectData> proofPeriodDetails = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query, TPMProofTimePeriodDetailFields.ALL);
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }
        return proofPeriodDetails;
    }

    private List<IObjectData> queryTPMProofTimePeriodDetailByObjectId(String tenantId, String fieldName, String id) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setNeedReturnQuote(false);
        query.setNeedReturnCountNum(false);
        query.setSearchSource("db");

        Filter refrenceIdFilter = new Filter();
        refrenceIdFilter.setFieldName(fieldName);
        refrenceIdFilter.setOperator(Operator.EQ);
        refrenceIdFilter.setFieldValues(Lists.newArrayList(id));

        query.setFilters(Lists.newArrayList(refrenceIdFilter));

        List<IObjectData> proofPeriodDetails = QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_PROOF_TIME_PERIOD_DETAIL_OBJ, query);
        if (CollectionUtils.isEmpty(proofPeriodDetails)) {
            return Lists.newArrayList();
        }
        return proofPeriodDetails;
    }

    /**
     * 根据时段ID查询举证数据
     */
    private List<IObjectData> queryProofDataByPeriodDetailId(String tenantId, String periodDetailId) {
        try {
            // 查询条件可以根据实际需求调整
            SearchTemplateQuery query = new SearchTemplateQuery();

            query.setLimit(-1);
            query.setOffset(0);
            query.setSearchSource("db");
            query.setNeedReturnCountNum(false);

            Filter contactIdFilter = new Filter();
            contactIdFilter.setFieldName(TPMActivityProofFields.PROOF_TIME_PERIOD_DETAIL_ID);
            contactIdFilter.setOperator(Operator.EQ);
            contactIdFilter.setFieldValues(com.fxiaoke.functions.utils.Lists.newArrayList(periodDetailId));
            query.getFilters().add(contactIdFilter);

            return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_ACTIVITY_PROOF_OBJ,
                    query, com.fxiaoke.functions.utils.Lists.newArrayList(CommonFields.ID, CommonFields.NAME, CommonFields.OBJECT_DESCRIBE_API_NAME,
                            TPMActivityProofFields.AUDIT_STATUS, TPMActivityProofFields.AI_IDENTIFY_STATUS));
        } catch (Exception e) {
            log.error("查询举证数据失败, 时段ID: {}", periodDetailId, e);
            throw new RuntimeException("query proof data error", e);
        }
    }
}
