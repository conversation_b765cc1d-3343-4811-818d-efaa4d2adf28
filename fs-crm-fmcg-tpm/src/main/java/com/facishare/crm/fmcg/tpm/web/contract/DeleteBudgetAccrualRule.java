package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/10/9 下午2:40
 */
public interface DeleteBudgetAccrualRule {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

    }
}
