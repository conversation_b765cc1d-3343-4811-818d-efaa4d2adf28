package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTableFrozenVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/14 下午4:09
 */
@Data
@ToString
public class BudgetTableFrozenEntity implements Serializable {


    public static final String F_API_NAME = "api_name";
    public static final String F_RELATION_FIELD = "relation_field";
    public static final String F_AMOUNT_FIELD = "amount_field";
    public static final String F_MASTER_DETAIL = "master_detail";

    public static final String F_BUDGET_TABLE_PROVISION = "budget_table_provision";

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_RELATION_FIELD)
    private String relationField;

    @Property(F_AMOUNT_FIELD)
    private String amountField;

    //对象为从对象，该字段为主从字段
    @Property(F_MASTER_DETAIL)
    private String masterDetail;

    @Embedded(F_BUDGET_TABLE_PROVISION)
    private BudgetTableProvisionEntity budgetTableProvisionEntity;


    public static BudgetTableFrozenEntity fromVO(BudgetTableFrozenVO budgetTableFrozen) {
        BudgetTableFrozenEntity tableFrozenEntity = new BudgetTableFrozenEntity();
        tableFrozenEntity.setApiName(budgetTableFrozen.getApiName());
        tableFrozenEntity.setRelationField(budgetTableFrozen.getRelationField());
        tableFrozenEntity.setAmountField(budgetTableFrozen.getAmountField());
        tableFrozenEntity.setMasterDetail(budgetTableFrozen.getMasterDetail());
        tableFrozenEntity.setBudgetTableProvisionEntity(BudgetTableProvisionEntity.fromVO(budgetTableFrozen.getBudgetTableProvision()));
        return tableFrozenEntity;
    }

    public static BudgetTableFrozenVO toVO(BudgetTableFrozenEntity tableFrozenEntity) {
        BudgetTableFrozenVO tableFrozenVO = new BudgetTableFrozenVO();
        tableFrozenVO.setApiName(tableFrozenEntity.getApiName());
        tableFrozenVO.setRelationField(tableFrozenEntity.getRelationField());
        tableFrozenVO.setAmountField(tableFrozenEntity.getAmountField());
        tableFrozenVO.setMasterDetail(tableFrozenEntity.getMasterDetail());
        tableFrozenVO.setBudgetTableProvision(BudgetTableProvisionEntity.toVO(tableFrozenEntity.getBudgetTableProvisionEntity()));
        return tableFrozenVO;
    }
}
