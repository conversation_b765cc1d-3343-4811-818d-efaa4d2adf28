package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import de.lab4inf.math.util.Strings;
import org.mongodb.morphia.query.Criteria;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 16:36
 */
public class ActivityTypeDAO extends UniqueIdBaseDAO<ActivityTypePO> {

    protected ActivityTypeDAO(Class<ActivityTypePO> clazz) {
        super(clazz);
    }

    public List<ActivityTypePO> all(String tenantId, boolean onlyNormal, boolean includeDeleted) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        if (onlyNormal) {
            query.field(ActivityTypePO.F_STATUS).equal(StatusType.NORMAL.value());
        }
        if (!includeDeleted) {
            query.field(MongoPO.F_IS_DELETED).equal(false);
        }
        return query.asList();
    }

    public boolean isDuplicateName(String tenantId, String name) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateName(String tenantId, String uniqueId, String name) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateApiName(String tenantId, String name) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_API_NAME).equal(name);
        return query.countAll() > 0;
    }

    public boolean isDuplicateApiName(String tenantId, String uniqueId, String name) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).notEqual(uniqueId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_API_NAME).equal(name);
        return query.countAll() > 0;
    }

    public List<ActivityTypePO> list(String tenantId, String keyword, String template, int limit, int offset) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order("-" + MongoPO.F_LAST_UPDATE_TIME)
                .offset(offset);
        if (limit != -1) {
            query.limit(limit);
        }
        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityTypePO.F_NAME).containsIgnoreCase(keyword);
        }
        if (!Strings.isNullOrEmpty(template)) {
            query.field(ActivityTypePO.F_TEMPLATE_ID).containsIgnoreCase(template);
        }
        return query.asList();
    }

    public List<ActivityTypePO> query(int limit, int offset) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order(MongoPO.F_ID)
                .limit(limit)
                .offset(offset);

        // 正常状态 OR (异常状态 AND (异常次数不存在 OR 异常次数小于3))
        query.or(
                query.criteria(ActivityTypePO.F_EXCEPTION_STATUS).equal(ExceptionStatusType.NORMAL.value()),
                query.criteria(ActivityTypePO.F_EXCEPTION_STATUS).notEqual(ExceptionStatusType.NORMAL.value())
                        .and(
                                query.or(
                                        query.criteria(ActivityTypePO.F_EXCEPTION_COUNT).doesNotExist(),
                                        query.criteria(ActivityTypePO.F_EXCEPTION_COUNT).lessThan(3)
                                )
                        )
        );
        return query.asList();
    }

    public List<ActivityTypePO> allListByTenantId(int limit, int offset, String tenantId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .order(MongoPO.F_ID)
                .limit(limit)
                .offset(offset);
        return query.asList();
    }

    public List<ActivityTypePO> validActivityType(String tenantId, String nodeApiName, List<Integer> departmentIds, Integer employeeId, List<String> roleIds) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_STATUS).equal(StatusType.NORMAL.value())
                .field("activity_nodes.object_api_name").equal(nodeApiName);

        Criteria department = query.criteria(ActivityTypePO.F_DEPARTMENT_IDS).hasAnyOf(departmentIds);
        Criteria employee = query.criteria(ActivityTypePO.F_EMPLOYEE_IDS).hasThisOne(employeeId);
        Criteria role = query.criteria(ActivityTypePO.F_ROLE_IDS).hasAnyOf(roleIds);
        query.or(department, employee, role);

        query.order("-" + MongoPO.F_LAST_UPDATE_TIME);
        return query.asList();
    }

    public long count(String tenantId, String keyword) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityTypePO.F_NAME).containsIgnoreCase(keyword);
        }

        return query.countAll();
    }

    public void edit(String tenantId,
                     String uniqueId,
                     int operator,
                     String name,
                     List<Integer> employeeIds,
                     List<Integer> departmentIds,
                     List<String> roleIds,
                     String scopeDescription,
                     String description,
                     String status,
                     String exceptionStatus,
                     List<ActivityNodeEntity> nodes,
                     Boolean forbidRelateCustomer) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityTypePO> updateOperations = mongoContext.createUpdateOperations(ActivityTypePO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(ActivityTypePO.F_NAME, name)
                .set(ActivityTypePO.F_EMPLOYEE_IDS, employeeIds)
                .set(ActivityTypePO.F_DEPARTMENT_IDS, departmentIds)
                .set(ActivityTypePO.F_ROLE_IDS, roleIds)
                .set(ActivityTypePO.F_SCOPE_DESCRIPTION, scopeDescription)
                .set(ActivityTypePO.F_DESCRIPTION, description)
                .set(ActivityTypePO.F_STATUS, status)
                .set(ActivityTypePO.F_EXCEPTION_STATUS, exceptionStatus)
                .inc(ActivityTypePO.F_VERSION)
                .set(ActivityTypePO.F_FORBID_RELATE_CUSTOMER, forbidRelateCustomer)
                .set(ActivityTypePO.F_ACTIVITY_NODES, nodes);

        mongoContext.update(query, updateOperations);
    }

    public boolean templateIsUsed(String tenantId, String templateId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".template_id").equal(templateId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .limit(1);
        return query.countAll() > 0;
    }

    public List<ActivityTypePO> queryByNodeTemplateId(String tenantId, String templateId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".template_id").equal(templateId)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public List<ActivityTypePO> queryByNodeTemplateId(String tenantId, List<String> templateIds) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".template_id").in(templateIds)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public List<ActivityTypePO> queryByTypeTemplateId(String tenantId, String templateId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_TEMPLATE_ID).equal(templateId)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public List<ActivityTypePO> queryByStartWithTypeTemplateId(String tenantId, String templateId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_TEMPLATE_ID).startsWith(templateId)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public List<ActivityTypePO> queryByTypeTemplateId(String tenantId, List<String> templateIds) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_TEMPLATE_ID).in(templateIds)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.asList();
    }

    public void setStatus(String tenantId, int operator, String uniqueId, String status) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityTypePO> updateOperations = mongoContext.createUpdateOperations(ActivityTypePO.class)
                .set(ActivityTypePO.F_STATUS, status)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }


    public List<String> queryProofActivityTypeIds(String tenantId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".type").equal(NodeType.PROOF.value())
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false).retrievedFields(true, MongoPO.F_ID, MongoPO.F_UNIQUE_ID);

        return query.asList().stream().map(v -> v.getId().toString()).collect(Collectors.toList());
    }

    public List<ActivityTypePO> queryActivityTypesContainsAudit(String tenantId) {
        return queryActivityTypesContainsXXNodeType(tenantId, NodeType.AUDIT.value());
    }

    public List<ActivityTypePO> queryActivityTypesContainsXXNodeType(String tenantId, String nodeType) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".type").equal(nodeType)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        return query.asList();
    }

    public List<String> querySystemActivityType(String tenantId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_PACKAGE).equal(PackageType.SYSTEM.value());
        if (query.countAll() > 0) {
            return query.asList().stream().map(ActivityTypePO::getApiName).collect(Collectors.toList());
        } else {
            return Lists.newArrayList();
        }
    }

    public void editExceptionStatus(String tenantId,
                                    String uniqueId,
                                    String exceptionStatus,
                                    Integer exceptionCount,
                                    List<ActivityNodeEntity> nodes) {

        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_ID).equal(uniqueId);

        UpdateOperations<ActivityTypePO> updateOperations = mongoContext.createUpdateOperations(ActivityTypePO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(ActivityTypePO.F_EXCEPTION_STATUS, exceptionStatus)
                .set(ActivityTypePO.F_EXCEPTION_COUNT, exceptionCount)
                .set(ActivityTypePO.F_ACTIVITY_NODES, nodes);

        mongoContext.update(query, updateOperations);
    }

    public boolean containNode(String tenantId, String uniqueId, String nodeType) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".type").equal(nodeType)
                .field(MongoPO.F_IS_DELETED).equal(false);
        return query.get() != null;
    }

    public Map<String, String> queryActivityTypeIdByName(String tenantId, Set<String> activityNames) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_NAME).in(activityNames)
                .field(MongoPO.F_IS_DELETED).equal(false);
        if (query.countAll() > 0) {
            return query.asList().stream().collect(Collectors.toMap(map -> map.getId().toString(), ActivityTypePO::getName));
        } else {
            return Maps.newHashMap();
        }
    }

    public ActivityTypePO findActivityTypeByApiName(String tenantId, String apiName) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityTypePO.F_API_NAME).equal(apiName);
        return query.get();
    }

    public List<ActivityTypePO> queryActivityTypesByModelIdOrRuleId(String tenantId, String modelId, String ruleId) {
        Query<ActivityTypePO> query = mongoContext.createQuery(ActivityTypePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(ActivityTypePO.F_ACTIVITY_NODES + ".activity_proof_config.ai_config.enable_ai_display_recognition").equal(true);
        if(!Strings.isNullOrEmpty(modelId)) {
            query.field(ActivityTypePO.F_ACTIVITY_NODES + ".activity_proof_config.ai_config.display_recognition_model").equal(modelId);
        }
        if (!Strings.isNullOrEmpty(ruleId)) {
            query.field(ActivityTypePO.F_ACTIVITY_NODES + ".activity_proof_config.ai_config.adaptation_rule").equal(ruleId);
        }
        return query.asList();
    }
}
