package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface ValidationStoreWriteOffData {

    @Data
    @ToString
    class Arg implements Serializable {

        private List<String> ids;

        private String type;

        private String tenantId;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @SerializedName("validation_result")
        @JSONField(name = "validation_result")
        @JsonProperty("validation_result")
        private Boolean validationResult;

        @SerializedName("validation_message")
        @JSONField(name = "validation_message")
        @JsonProperty("validation_message")
        private String validationMessage;
    }

}
