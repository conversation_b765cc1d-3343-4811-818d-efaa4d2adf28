package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityProofCostCalculateConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 15:21
 */
@Data
@ToString
public class ActivityProofCostCalculateConfigVO implements Serializable {

    @JSONField(name = "calculate_type")
    @JsonProperty(value = "calculate_type")
    @SerializedName("calculate_type")
    private String calculateType;

    @JSONField(name = "ratio")
    @JsonProperty(value = "ratio")
    @SerializedName("ratio")
    private String ratio;

    public static ActivityProofCostCalculateConfigVO fromPO(ActivityProofCostCalculateConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityProofCostCalculateConfigVO vo = new ActivityProofCostCalculateConfigVO();
        vo.setCalculateType(po.getCalculateType());
        vo.setRatio(po.getRatio());
        return vo;
    }
}