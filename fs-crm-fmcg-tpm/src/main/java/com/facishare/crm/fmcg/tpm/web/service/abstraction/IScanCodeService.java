package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.api.scan.*;

/**
 * Author: linmj
 * Date: 2023/9/20 10:39
 */
public interface IScanCodeService {

    BigDatePay.Result bigDatePay(BigDatePay.Arg arg);

    BigDateScanCode.Result bigDateScanCode(BigDateScanCode.Arg arg);

    CloseBigDateWxOrder.Result closeBigDateWxOrder(CloseBigDateWxOrder.Arg arg);


    ConsumerScanInnerCode.Result consumerScanCode(ConsumerScanInnerCode.Arg arg);

    ConsumerScanCodeType.Result codeType(ConsumerScanCodeType.Arg arg);

    CodeRewarded.Result rewarded(CodeRewarded.Arg arg);

    ConsumerScanEnableReward.Result enableReward(ConsumerScanEnableReward.Arg arg);

    ConsumerScanCheckLock.Result checkLock(ConsumerScanCheckLock.Arg arg);

    ConsumerScanUnlock.Result unlock(ConsumerScanUnlock.Arg arg);
}
