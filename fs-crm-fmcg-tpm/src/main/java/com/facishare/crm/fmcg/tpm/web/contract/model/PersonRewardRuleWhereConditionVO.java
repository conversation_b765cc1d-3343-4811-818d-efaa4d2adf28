package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


@Data
@ToString
public class PersonRewardRuleWhereConditionVO implements Serializable {

    @JSONField(name = "connector")
    @JsonProperty(value = "connector")
    @SerializedName("connector")
    private String connector;

    @JSONField(name = "filters")
    @JsonProperty(value = "filters")
    @SerializedName("filters")
    private List<PersonRewardRuleTriggerConditionVO> triggerConditions;
}
