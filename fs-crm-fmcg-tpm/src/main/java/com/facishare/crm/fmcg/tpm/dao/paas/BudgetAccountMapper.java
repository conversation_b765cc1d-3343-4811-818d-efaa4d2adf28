package com.facishare.crm.fmcg.tpm.dao.paas;

import com.facishare.paas.metadata.ratelimit.DBLimit;
import com.facishare.paas.metadata.ratelimit.MethodType;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/14 下午4:55
 */
public interface BudgetAccountMapper extends ITenant<BudgetAccountMapper> {


    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.UPDATE
    )
    void updateAccountAmount(@Param("tenant_id") String tenantId, @Param("_id") String id, @Param("all") Map<String, Object> updateMap);

    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    Map<String, Object> statisticDepartmentAmount(String tenantId, String budgetId);
}
