package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/6/20 17:52
 */
public interface GetFoundAccountList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_type")
        @JsonProperty(value = "activity_type")
        @SerializedName("activity_type")
        private String activityType;

        @JSONField(name = "fund_account_id")
        @JsonProperty(value = "fund_account_id")
        @SerializedName("fund_account_id")
        private String fundAccountId;


    }

    @Data
    @ToString
    class Result implements Serializable {

        @JSONField(name = "cash_account_list")
        @JsonProperty(value = "cash_account_list")
        @SerializedName("cash_account_list")
        private List<SimpleAccountDTO> cashAccountList;


        @JSONField(name = "goods_quantity_account_list")
        @JsonProperty(value = "goods_quantity_account_list")
        @SerializedName("goods_quantity_account_list")
        private List<SimpleAccountDTO> goodsQuantityAccountList;

        @JSONField(name = "assign_account_info")
        @JsonProperty(value = "assign_account_info")
        @SerializedName("assign_account_info")
        private SimpleAccountDTO assignAccountIfo;

        @JSONField(name = "allow_edit")
        @JsonProperty(value = "allow_edit")
        @SerializedName("allow_edit")
        private boolean allowEdit;
    }

    @Data
    @ToString
    @Builder
    class SimpleAccountDTO implements Serializable {

        private String name;

        private String id;

        @JSONField(name = "access_module")
        @JsonProperty(value = "access_module")
        @SerializedName("access_module")
        private String accessModule;
        @JSONField(name = "account_type")
        @JsonProperty(value = "account_type")
        @SerializedName("account_type")
        private String accountType;
    }


}
