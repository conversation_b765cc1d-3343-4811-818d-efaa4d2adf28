package com.facishare.crm.fmcg.tpm.web.poc.abstraction;

import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivity;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivityAgreement;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivityProof;
import com.facishare.crm.fmcg.tpm.web.contract.poc.POCActivityProofAudit;

import java.util.List;
import java.util.Map;

public interface IPOCInitService {
    List<POCActivity.Result> initWholeActivities(String tenantId, List<POCActivity.Result> activities,
                                                 Map<String, String> activityTypeIdAndName, Map<String, String> activityItemObjMap);

    List<POCActivity.Result> initIncrementActivities(String tenantId, Long begin, List<POCActivity.Result> results,
                                                     Map<String, String> activityTypeIdAndName, Map<String, String> agreementActivityTypeIdName, Map<String, String> activityItemObjMap);

    List<POCActivityAgreement.Result> initAgreement(List<POCActivity.Result> activitiesResults, Map<String, String> activityItemIdName, Map<String, String> agreementActivityTypeIdName, ApiContext context);

    /**
     * 增量举证
     */
    List<POCActivityProof.Result> initProofIncrement(List<POCActivity.Result> activitiesResult, List<POCActivityAgreement.Result> activityAgreementsResult,
                                                     Map<String, String> activityTypeIdGroupByActivityId, Map<String, String> activityTypeIdAndName, Long date, ApiContext context);

    List<POCActivityProofAudit.Result> initProofAudit(List<POCActivityProof.Result> proofs);

    void initStoreWriteOff(List<String> activityIds, ApiContext context);

    void initBeforeProof(ApiContext context, Long beginDate, Long endDate);

}
