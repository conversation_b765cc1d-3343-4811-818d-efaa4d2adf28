package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 14:21
 */
@Data
@ToString
@Builder
public class ObjectVO implements Serializable {

    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    @SerializedName("api_name")
    private String apiName;

    @JSONField(name = "display_name")
    @JsonProperty(value = "display_name")
    @SerializedName("display_name")
    private String displayName;
}
