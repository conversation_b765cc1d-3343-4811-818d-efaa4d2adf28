package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/3/20 7:58 PM
 */
public interface PreAdd {

    @Data
    @ToString
    class Arg {

        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("proof_id")
        @JSONField(name = "proof_id")
        @JsonProperty("proof_id")
        private String proofId;

        @SerializedName("activity_agreement_id")
        @JSONField(name = "activity_agreement_id")
        @JsonProperty("activity_agreement_id")
        private String activityAgreementId;
    }

    @Data
    @ToString
    class Result {

        @SerializedName("is_agreement_activity")
        @JSONField(name = "is_agreement_activity")
        @JsonProperty("is_agreement_activity")
        private boolean activityIsAgreementRequired;

        @SerializedName("is_dealer_activity")
        @JSONField(name = "is_dealer_activity")
        @JsonProperty("is_dealer_activity")
        private Boolean isDealerActivity = false;

        @SerializedName("object_data")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        private ProofDataVO objectData;

        // 编辑时需要的举证数据主对象
        @SerializedName("proof_data")
        @JSONField(name = "proof_data")
        @JsonProperty("proof_data")
        private ObjectDataDocument proofData;

        @SerializedName("details")
        @JSONField(name = "details")
        @JsonProperty("details")
        private Map<String, List<?>> details;

        @SerializedName("display_fields")
        @JSONField(name = "display_fields")
        @JsonProperty("display_fields")
        private List<Field> displayFields;

        @SerializedName("is_edit")
        @JSONField(name = "is_edit")
        @JsonProperty("is_edit")
        private Boolean isEdit = false;

    }

    @Data
    @ToString
    @Builder
    class Field implements Serializable {

        @SerializedName("label")
        @JSONField(name = "label")
        @JsonProperty("label")
        private String label;

        @SerializedName("api_name")
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        private String apiName;

        @SerializedName("type")
        @JSONField(name = "type")
        @JsonProperty("type")
        private String type;
    }

    @Data
    @ToString
    class ProofDetailDataVO implements Serializable {

        @SerializedName("activity_agreement_detail_id")
        @JSONField(name = "activity_agreement_detail_id")
        @JsonProperty("activity_agreement_detail_id")
        private String activityAgreementDetailId;

        @SerializedName("activity_agreement_detail_id__r")
        @JSONField(name = "activity_agreement_detail_id__r")
        @JsonProperty("activity_agreement_detail_id__r")
        private String activityAgreementDetailName;

        @SerializedName("activity_item_id")
        @JSONField(name = "activity_item_id")
        @JsonProperty("activity_item_id")
        private String activityItemId;

        @SerializedName("activity_item_id__r")
        @JSONField(name = "activity_item_id__r")
        @JsonProperty("activity_item_id__r")
        private String activityItemLabel;

        @SerializedName("activity_detail_id")
        @JSONField(name = "activity_detail_id")
        @JsonProperty("activity_detail_id")
        private String activityDetailId;

        @SerializedName("activity_detail_id__r")
        @JSONField(name = "activity_detail_id__r")
        @JsonProperty("activity_detail_id__r")
        private String activityDetailName;

        @SerializedName("amount_standard")
        @JSONField(name = "amount_standard")
        @JsonProperty("amount_standard")
        private Double amountStandard;

        @SerializedName("proof_detail_amount_standard")
        @JSONField(name = "proof_detail_amount_standard")
        @JsonProperty("proof_detail_amount_standard")
        private Double proofDetailAmountStandard;

        @SerializedName("proof_detail_cost_standard")
        @JSONField(name = "proof_detail_cost_standard")
        @JsonProperty("proof_detail_cost_standard")
        private Double proofDetailCostStandard;

        @SerializedName("agreement_amount_standard")
        @JSONField(name = "agreement_amount_standard")
        @JsonProperty("agreement_amount_standard")
        private Double agreementAmountStandard;

        private String type;

        @SerializedName("proof_data_type")
        @JSONField(name = "proof_data_type")
        @JsonProperty("proof_data_type")
        private String proofDataType;

        @SerializedName("activity_cost_standard")
        @JSONField(name = "activity_cost_standard")
        @JsonProperty("activity_cost_standard")
        private Double activityCostStandard;

        @SerializedName("calculate_pattern")
        @JSONField(name = "calculate_pattern")
        @JsonProperty("calculate_pattern")
        private String calculatePattern;

        @SerializedName("calculate_pattern__r")
        @JSONField(name = "calculate_pattern__r")
        @JsonProperty("calculate_pattern__r")
        private String calculatePatternLabel;

        @SerializedName("calculate_pattern__v")
        @JSONField(name = "calculate_pattern__v")
        @JsonProperty("calculate_pattern__v")
        private String calculatePatternValue;

        @SerializedName("amount_standard_check")
        @JSONField(name = "amount_standard_check")
        @JsonProperty("amount_standard_check")
        private String amountStandardCheck;

        @SerializedName("amount_standard_check__v")
        @JSONField(name = "amount_standard_check__v")
        @JsonProperty("amount_standard_check__v")
        private Boolean amountStandardCheckValue;

        @SerializedName("activity_item_cost_standard_id")
        @JSONField(name = "activity_item_cost_standard_id")
        @JsonProperty("activity_item_cost_standard_id")
        private String activityItemCostStandardId;

        @SerializedName("activity_item_cost_standard_id__r")
        @JSONField(name = "activity_item_cost_standard_id__r")
        @JsonProperty("activity_item_cost_standard_id__r")
        private String activityItemCostStandardLabel;

        @SerializedName("is_report_item_quantity")
        @JSONField(name = "is_report_item_quantity")
        @JsonProperty("is_report_item_quantity")
        private Boolean isReportItemQuantity;


        @SerializedName("proof_item")
        @JSONField(name = "proof_item")
        @JsonProperty("proof_item")
        private String proofItem;
    }

    @Data
    @ToString
    class ProofDataVO implements Serializable {

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private String storeId;

        @SerializedName("store_id__r")
        @JSONField(name = "store_id__r")
        @JsonProperty("store_id__r")
        private String storeName;

        @SerializedName("dealer_id")
        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        private String dealerId;

        @SerializedName("dealer_id__r")
        @JSONField(name = "dealer_id__r")
        @JsonProperty("dealer_id__r")
        private String dealerName;

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("activity_id__r")
        @JSONField(name = "activity_id__r")
        @JsonProperty("activity_id__r")
        private String activityName;

        @SerializedName("activity_agreement_id")
        @JSONField(name = "activity_agreement_id")
        @JsonProperty("activity_agreement_id")
        private String activityAgreementId;

        @SerializedName("activity_agreement_id__r")
        @JSONField(name = "activity_agreement_id__r")
        @JsonProperty("activity_agreement_id__r")
        private String activityAgreementName;


        @SerializedName("visit_id")
        @JSONField(name = "visit_id")
        @JsonProperty("visit_id")
        private String visitId;

        @SerializedName("action_id")
        @JSONField(name = "action_id")
        @JsonProperty("action_id")
        private String actionId;

        private List<String> owner;

        @SerializedName("audit_status")
        @JSONField(name = "audit_status")
        @JsonProperty("audit_status")
        private String auditStatus = TPMActivityProofFields.AUDIT_STATUS__SCHEDULE;

        @SerializedName("activity_store_id__c")
        @JSONField(name = "activity_store_id__c")
        @JsonProperty("activity_store_id__c")
        private String activityStoreId;

        @SerializedName("activity_store_id__c__r")
        @JSONField(name = "activity_store_id__c__r")
        @JsonProperty("activity_store_id__c__r")
        private String activityStoreName;


        @SerializedName("cost_conversion_ratio")
        @JSONField(name = "cost_conversion_ratio")
        @JsonProperty("cost_conversion_ratio")
        private Double costConversionRatio;

        @SerializedName("record_type")
        @JSONField(name = "record_type")
        @JsonProperty("record_type")
        private String recordType;

        @SerializedName("open_ai")
        @JSONField(name = "open_ai")
        @JsonProperty("open_ai")
        private Boolean openAi;

        @SerializedName("proof_time_period_detail_id")
        @JSONField(name = "proof_time_period_detail_id")
        @JsonProperty("proof_time_period_detail_id")
        private String proofTimePeriodDetailId;

    }

    @Data
    @ToString
    class ProofDisplayImgDataVO implements Serializable {

        @SerializedName("activity_item_id")
        @JSONField(name = "activity_item_id")
        @JsonProperty("activity_item_id")
        private String activityItemId;

        @SerializedName("activity_item_id__r")
        @JSONField(name = "activity_item_id__r")
        @JsonProperty("activity_item_id__r")
        private String activityItemLabel;

        @SerializedName("show_display_id")
        @JSONField(name = "show_display_id")
        @JsonProperty("show_display_id")
        private String showDisplayId;

        @SerializedName("show_display_id__r")
        @JSONField(name = "show_display_id__r")
        @JsonProperty("show_display_id__r")
        private String showDisplayLabel;

        @SerializedName("image")
        @JSONField(name = "image")
        @JsonProperty("image")
        private String image;

        @SerializedName("display_form_id")
        @JSONField(name = "display_form_id")
        @JsonProperty("display_form_id")
        private String displayFormId;

        @SerializedName("display_form_id__r")
        @JSONField(name = "display_form_id__r")
        @JsonProperty("display_form_id__r")
        private String displayFormLabel;

        @SerializedName("actual_display_position")
        @JSONField(name = "actual_display_position")
        @JsonProperty("actual_display_position")
        private String actualDisplayPosition;

        @SerializedName("standard_display_position")
        @JSONField(name = "standard_display_position")
        @JsonProperty("standard_display_position")
        private String standardDisplayPosition;

        @SerializedName("record_type")
        @JSONField(name = "record_type")
        @JsonProperty("record_type")
        private String recordType;
    }
}