package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/28 12:26
 */
public interface AgreementAuditSummaryList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "agreement_id")
        @JsonProperty(value = "agreement_id")
        @SerializedName("agreement_id")
        private String agreementId;

        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "dealer_id")
        @JsonProperty(value = "dealer_id")
        @SerializedName("dealer_id")
        private String dealerId;

        @JSONField(name = "store_id")
        @JsonProperty(value = "store_id")
        @SerializedName("store_id")
        private String storeId;

        @JSONField(name = "begin_date")
        @JsonProperty(value = "begin_date")
        @SerializedName("begin_date")
        private Long beginDate;

        @JSONField(name = "end_date")
        @JsonProperty(value = "end_date")
        @SerializedName("end_date")
        private Long endDate;

        @JSONField(name = "minimum_proof_count")
        @JsonProperty(value = "minimum_proof_count")
        @SerializedName("minimum_proof_count")
        private Long minimumProofCount;

        @JSONField(name = "view_mode")
        @JsonProperty(value = "view_mode")
        @SerializedName("view_mode")
        private String viewMode;

        @JSONField(name = "is_asc")
        @JsonProperty(value = "is_asc")
        @SerializedName("is_asc")
        private boolean isAsc;

        @JSONField(name = "limit")
        @JsonProperty(value = "limit")
        @SerializedName("limit")
        private Long limit;

        @JSONField(name = "offset")
        @JsonProperty(value = "offset")
        @SerializedName("offset")
        private Long offset;


        private String keyword;

        @JSONField(name = "out_tenant_id")
        @JsonProperty(value = "out_tenant_id")
        @SerializedName("out_tenant_id")
        private String outTenantId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private List<AgreementAuditSummaryDatumVO> data;


        @JSONField(name = "use_audit_role")
        @JsonProperty(value = "use_audit_role")
        @SerializedName("use_audit_role")
        private Boolean useAuditRole;

        private long total;

        @JSONField(name = "use_complex_mode")
        @JsonProperty(value = "use_complex_mode")
        @SerializedName("use_complex_mode")
        private Boolean useComplexMode;

        @JSONField(name = "activity_data")
        @JsonProperty(value = "activity_data")
        @SerializedName("activity_data")
        private ObjectDataDocument activityData;

        @JSONField(name = "activity_describe")
        @JsonProperty(value = "activity_describe")
        @SerializedName("activity_describe")
        private ObjectDescribeDocument activityDescribe;

        @JSONField(name = "dealer_data")
        @JsonProperty(value = "dealer_data")
        @SerializedName("dealer_data")
        private ObjectDataDocument dealerData;

        @JSONField(name = "dealer_describe")
        @JsonProperty(value = "dealer_describe")
        @SerializedName("dealer_describe")
        private ObjectDescribeDocument dealerDescribe;

        @JSONField(name = "need_store")
        @JsonProperty(value = "need_store")
        @SerializedName("need_store")
        private Boolean needStore;

    }


    @Data
    @ToString
    class AgreementAuditSummaryDatumVO implements Serializable {

        private String id;

        private String name;

        @JSONField(name = "data_count")
        @JsonProperty(value = "data_count")
        @SerializedName("data_count")
        private long dataCount;

        @JSONField(name = "pass_data_count")
        @JsonProperty(value = "pass_data_count")
        @SerializedName("pass_data_count")
        private long passDataCount;

        @JSONField(name = "unaudited_data_count")
        @JsonProperty(value = "unaudited_data_count")
        @SerializedName("unaudited_data_count")
        private long unauditedDataCount;

        @JSONField(name = "reject_data_count")
        @JsonProperty(value = "reject_data_count")
        @SerializedName("reject_data_count")
        private long rejectDataCount;

        @JSONField(name = "data_id_list")
        @JsonProperty(value = "data_id_list")
        @SerializedName("data_id_list")
        private List<String> dataIdList;
    }
}
