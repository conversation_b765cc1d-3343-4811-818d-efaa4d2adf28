package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityWriteOffChargeUpConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
public class ActivityWriteOffChargeUpConfigEntity implements Serializable {

    /**
     * 现金上账账户id
     */
    @Property("cash_account_id")
    private String cashAccountId;


    /**
     * 货补返数量上账账户id
     */
    @Property("return_goods_quantity_account_id")
    private String returnGoodsQuantityAccountId;

    /**
     * 用户是否勾选上账账户
     */
    @Property("charge_up_account_status")
    private Boolean chargeUpAccountStatus;

    /**
     * 上账类型
     */
    @Property("charge_up_type")
    private String chargeUpType;


    @Property("enable_edit_default_account")
    private Boolean enableEditDefaultAccount = true;

    public static ActivityWriteOffChargeUpConfigEntity fromVO(ActivityWriteOffChargeUpConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityWriteOffChargeUpConfigEntity po = new ActivityWriteOffChargeUpConfigEntity();
        po.setChargeUpType(vo.getChargeUpType());
        po.setCashAccountId(vo.getCashAccountId());
        po.setReturnGoodsQuantityAccountId(vo.getReturnGoodsQuantityAccountId());
        po.setChargeUpAccountStatus(vo.getChargeUpAccountStatus());
        po.setEnableEditDefaultAccount(vo.getEnableEditDefaultAccount());
        return po;
    }
}
