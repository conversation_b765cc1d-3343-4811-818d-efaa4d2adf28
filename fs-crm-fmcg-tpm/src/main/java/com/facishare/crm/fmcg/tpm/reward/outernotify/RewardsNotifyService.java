package com.facishare.crm.fmcg.tpm.reward.outernotify;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberFields;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuManufacturerInformation;
import com.facishare.crm.fmcg.tpm.reward.decoder.QRDecoderCenter;
import com.facishare.crm.fmcg.tpm.reward.dto.OuterRewardExecuteResult;
import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotify;
import com.facishare.crm.fmcg.tpm.reward.dto.RewardNotifyRetry;
import com.facishare.crm.fmcg.tpm.reward.dto.SerialNumberData;
import com.facishare.crm.fmcg.tpm.reward.handler.ConsumerRewardHandler;
import com.facishare.crm.fmcg.tpm.web.service.ScanCodeService;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.concurrent.DynamicExecutors;
import com.github.autoconf.ConfigFactory;
import com.github.rholder.retry.*;
import com.github.trace.TraceContext;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RewardsNotifyService implements IRewardsNotifyService, InitializingBean {

    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private TenantHierarchyService tenantHierarchyService;
    @Resource
    private RewardsNotifyAuthenticationCmd rewardsNotifyAuthenticationCmd;
    @Resource
    private ConsumerRewardHandler consumerRewardHandler;
    @Resource
    private ScanCodeService scanCodeService;

    private ThreadPoolExecutor executor;

    private final Retryer<OuterRewardExecuteResult> retryer = RetryerBuilder.<OuterRewardExecuteResult>newBuilder()
            .retryIfResult(OuterRewardExecuteResult::isRetry)
            .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(3))
            .build();

    public static final String API_NAME = "outer_rewards_notify__c";
    public static final String SOURCE_APP = "source_app__c";
    public static final String MESSAGE_ID = "message_id__c";
    public static final String ORIGINAL_DATA = "original_data__c";
    public static final String PLATFORM = "platform__c";
    public static final String MESSAGE_STATUS = "message_status__c";
    public static final String MESSAGE_STATUS_VALUE_INIT = "init";
    public static final String MESSAGE_STATUS_VALUE_SUCCESS = "success";
    public static final String MESSAGE_STATUS_VALUE_ERROR = "error";
    public static final String OUTER_CONSUMER_REWARD_RULE_ID = "outer_consumer_reward_rule_id__c";
    public static final String PRODUCT_CODE = "product_code__c";
    public static final String ERROR_MESSAGE = "error_message__c";

    private static final String PLATFORM_VALUE = "mn_code_center";
    private int threadPoolSize = 5;
    private int threadPoolMaxSize = 10;

    @Override
    public void afterPropertiesSet() {
        ConfigFactory.getConfig("fs-fmcg-reward-config", data -> {
            this.threadPoolSize = data.getInt("mn-outer-reward-executor-thread-pool-size");
            this.threadPoolMaxSize = data.getInt("mn-outer-reward-executor-thread-pool-max-size");

            this.reloadThreadPool(this.threadPoolSize, this.threadPoolMaxSize);
        });
    }

    private synchronized void reloadThreadPool(int threadPoolSize, int threadPoolMaxSize) {
        if (Objects.isNull(this.executor)) {
            ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("MN-OUTER-REWARD-%d").setDaemon(true).build();
            this.executor = DynamicExecutors.newThreadPool(
                    threadPoolSize,
                    threadPoolMaxSize,
                    10000L,
                    3000,
                    threadFactory
            );
        } else {
            this.executor.setCorePoolSize(threadPoolSize);
            this.executor.setMaximumPoolSize(threadPoolMaxSize);
        }
    }

    public static final long LOCK_WAIT = 5;
    public static final long LOCK_LEASE = 120;

    @Override
    public RewardNotify.Result rewardsNotify(String environment, String auth, RewardNotify.Arg arg) {
        TraceContext context = TraceContext.get();
        context.setTraceId(String.format("F-%s.%s", environment, IdGenerator.get()));

        if (!this.authorization(arg.getAppId(), arg.getMessageId(), auth)) {
            return RewardNotify.Result.fail(401_001, "authorization failed");
        }

        if (Objects.isNull(arg.getData())) {
            return RewardNotify.Result.fail(403_002, "notify data can not be null");
        }

        MengNiuManufacturerInformation tenant;
        try {
            tenant = tenantHierarchyService.findManufacturerByEnvironment(environment);
        } catch (MetaDataBusinessException ex) {
            return RewardNotify.Result.fail(500_001, "environment can not match to any tenant");
        }
        context.setEi(tenant.getTenantId());

        String key = String.format("FMCG_REWARDS_NOTIFY:%s.%s", arg.getAppId(), arg.getMessageId());
        if (!this.tryLock(key)) {
            return RewardNotify.Result.fail(500_003, "try lock notify request failed");
        }

        try {

            String currentRecordId = findRecordId(tenant.getTenantId(), arg.getMessageId());
            if (!Strings.isNullOrEmpty(currentRecordId)) {
                return RewardNotify.Result.success(currentRecordId);
            }

            String outerConsumerRewardRuleId = arg.getData().getString("codecenteractID");
            String snCode = arg.getData().getString("markcode");

            IObjectData data = new ObjectData();

            data.setTenantId(tenant.getTenantId());
            data.setDescribeApiName(API_NAME);
            data.setRecordType("default__c");
            data.setOwner(Lists.newArrayList("-10000"));

            data.set(SOURCE_APP, arg.getAppId());
            data.set(MESSAGE_ID, arg.getMessageId());
            data.set(ORIGINAL_DATA, arg.getData().toJSONString());
            data.set(MESSAGE_STATUS, MESSAGE_STATUS_VALUE_INIT);
            data.set(PLATFORM, PLATFORM_VALUE);
            data.set(OUTER_CONSUMER_REWARD_RULE_ID, arg.getData().getString("codecenteractID"));
            data.set(PRODUCT_CODE, snCode);

            IObjectData saved = serviceFacade.saveObjectData(User.systemUser(tenant.getTenantId()), data);

            log.info("rewards notify success : {} to {}", arg.getMessageId(), saved.getId());

            this.asyncReward(tenant.getTenantId(), saved.getId(), outerConsumerRewardRuleId, snCode);

            return RewardNotify.Result.success(saved.getId());
        } catch (Exception ex) {
            log.error("rewards notify exception : ", ex);
            return RewardNotify.Result.fail(500_001, "unknown exception");
        } finally {
            this.unlock(key);
        }
    }

    @Override
    public RewardNotifyRetry.Result retry(RewardNotifyRetry.Arg arg) {
        ApiContext context = ApiContextManager.getContext();

        if (Strings.isNullOrEmpty(arg.getId())) {
            return RewardNotifyRetry.Result.builder().id(arg.getId()).build();
        }

        IObjectData data;
        try {
            data = serviceFacade.findObjectData(User.systemUser(context.getTenantId()), arg.getId(), API_NAME);
        } catch (Exception ex) {
            return RewardNotifyRetry.Result.builder().id(arg.getId()).build();
        }

        String status = data.get(MESSAGE_STATUS, String.class);
        String outerConsumerRewardRuleId = data.get(OUTER_CONSUMER_REWARD_RULE_ID, String.class);
        String snCode = data.get(PRODUCT_CODE, String.class);
        if (MESSAGE_STATUS_VALUE_SUCCESS.equals(status)) {
            return RewardNotifyRetry.Result.builder().id(arg.getId()).build();
        }

        asyncReward(context.getTenantId(), data.getId(), outerConsumerRewardRuleId, snCode);
        return RewardNotifyRetry.Result.builder().id(arg.getId()).build();
    }

    private void asyncReward(String tenantId, String notifyId, String outerConsumerRewardRuleId, String snCode) {
        executor.submit(MonitorTaskWrapper.wrap(() -> rewardWithRetry(tenantId, notifyId, outerConsumerRewardRuleId, snCode)));
    }

    private void rewardWithRetry(String tenantId, String notifyId, String outerConsumerRewardRuleId, String snCode) {
        String status;
        String message;
        String snOuterCode = null;

        try {
            OuterRewardExecuteResult result = retryer.call(() -> reward(tenantId, outerConsumerRewardRuleId, snCode));
            if (result.isSuccess()) {
                status = MESSAGE_STATUS_VALUE_SUCCESS;
            } else {
                status = MESSAGE_STATUS_VALUE_ERROR;
            }
            message = result.getMessage();
            snOuterCode = result.getSnOuterName();
        }  catch (ExecutionException ex) {
            log.error("outer reward cause execution exception : ", ex);
            status = MESSAGE_STATUS_VALUE_ERROR;
            message = "EXECUTION EXCEPTION : " + ex.getMessage();
        } catch (RetryException ex) {
            log.error("outer reward cause retry exception : ", ex);
            status = MESSAGE_STATUS_VALUE_ERROR;
            message = "RETRY EXCEPTION : " + ex.getMessage();
        }

        IObjectData data = new ObjectData();
        List<String> updateFields = Lists.newArrayList(MESSAGE_STATUS, ERROR_MESSAGE);

        data.setTenantId(tenantId);
        data.setId(notifyId);
        data.setDescribeApiName(API_NAME);
        data.setRecordType("default__c");
        data.set(MESSAGE_STATUS, status);
        data.set(ERROR_MESSAGE, message);
        if (!Strings.isNullOrEmpty(snOuterCode)) {
            data.set("sn_name__c", snOuterCode);
            updateFields.add("sn_name__c");
        }

        serviceFacade.batchUpdateByFields(User.systemUser(tenantId), Lists.newArrayList(data), updateFields);
    }

    @Override
    public OuterRewardExecuteResult reward(String tenantId, String outerConsumerRewardRuleId, String snCode) {
        try {
            SerialNumberData code = QRDecoderCenter.getDecoder("").decode(tenantId, snCode);
            log.info("decode result {} : {}", snCode, JSON.toJSONString(code));

            if (Objects.isNull(code)) {
                return OuterRewardExecuteResult.builder().retry(false).success(false).message("sn code not found").build();
            }

            if (!code.getType().equals(SerialNumberData.INNER_TYPE)) {
                throw new ValidateException("input code is not inner code");
            }

            IObjectData sn = serviceFacade.findObjectData(User.systemUser(tenantId), code.getSnId(), ApiNames.FMCG_SERIAL_NUMBER_OBJ);
            log.info("sn : {}.{}", sn.getId(), sn.getName());

            IObjectData sku = serviceFacade.findObjectData(User.systemUser(tenantId), sn.get(FMCGSerialNumberFields.PRODUCT_ID, String.class), ApiNames.PRODUCT_OBJ);
            log.info("sku : {}.{}", sku.getId(), sku.getName());

            IObjectData store = scanCodeService.getStoreByStoreSign(tenantId, code.getSnId(), true);
            if (Objects.isNull(store)) {
                throw new ValidateException("store not found");
            }
            log.info("store : {}.{}", store.getId(), store.getName());

            IObjectData activity = scanCodeService.findConsumerScanCodeActivity(tenantId, String.format("%s.%s", PLATFORM_VALUE, outerConsumerRewardRuleId), store, sku, sn);
            if (Objects.isNull(activity)) {
                throw new ValidateException("activity not found");
            }
            log.info("activity : {}.{}", activity.getId(), activity.getName());

            consumerRewardHandler.dealOuterRewardInformation(tenantId, activity.getId(), sn, code, store);
            return OuterRewardExecuteResult.builder().retry(false).snOuterName(sn.getName()).success(true).message(MESSAGE_STATUS_VALUE_SUCCESS).build();
        } catch (BizException ex) {
            log.info("outer reward cause BizException exception : ", ex);
            return OuterRewardExecuteResult.builder().retry(false).success(false).message(String.format("BUSINESS EXCEPTION : %s", ex.getMessage())).build();
        } catch (AppBusinessException ex) {
            log.error("outer reward cause business exception : ", ex);
            return OuterRewardExecuteResult.builder().retry(false).success(false).message(String.format("BUSINESS EXCEPTION : %s", ex.getMessage())).build();
        } catch (Exception ex) {
            log.error("outer reward cause unknown exception : ", ex);
            return OuterRewardExecuteResult.builder().retry(true).success(false).message(String.format("UNKNOWN EXCEPTION : %s", ex.getMessage())).build();
        }
    }

    private String findRecordId(String tenantId, String messageId) {
        IFilter messageIdFilter = new Filter();
        messageIdFilter.setFieldName(MESSAGE_ID);
        messageIdFilter.setOperator(Operator.EQ);
        messageIdFilter.setFieldValues(Lists.newArrayList(messageId));

        List<IObjectData> currentData = QueryDataUtil.find(serviceFacade, tenantId, API_NAME, QueryDataUtil.minimumFindOneQuery(messageIdFilter), Lists.newArrayList("_id"));

        if (CollectionUtils.isEmpty(currentData)) {
            return null;
        }

        return currentData.get(0).getId();
    }

    @SuppressWarnings("all")
    private boolean tryLock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    private void unlock(String lockKey) {
        RLock lock = redissonCmd.getLock(lockKey);
        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean authorization(String appId, String messageId, String auth) {
        return rewardsNotifyAuthenticationCmd.verify(appId, messageId, auth);
    }
}
