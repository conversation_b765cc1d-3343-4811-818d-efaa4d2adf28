package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityCostAssignConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class ActivityCostAssignConfigVO implements Serializable {

    @JSONField(name = "activity_cost_assign_accept_config")
    @JsonProperty(value = "activity_cost_assign_accept_config")
    @SerializedName("activity_cost_assign_accept_config")
    private ActivityCostAssignAcceptConfigVO activityCostAssignAcceptConfigVO;

    public static ActivityCostAssignConfigVO fromPO(ActivityCostAssignConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityCostAssignConfigVO vo = new ActivityCostAssignConfigVO();
        vo.setActivityCostAssignAcceptConfigVO(ActivityCostAssignAcceptConfigVO.fromPO(po.getActivityCostAssignAcceptConfig()));
        return vo;
    }
}
