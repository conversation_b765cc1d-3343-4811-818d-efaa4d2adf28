package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RandomRewardLevelEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardStrategyEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/15 17:43
 */
@Data
@ToString
public class RewardStrategyDTO implements Serializable {

    @JsonProperty(value = RewardStrategyEntity.F_REWARD_METHOD)
    @SerializedName(RewardStrategyEntity.F_REWARD_METHOD)
    @JSONField(name = RewardStrategyEntity.F_REWARD_METHOD)
    private String rewardMethod;

    @JsonProperty(value = RewardStrategyEntity.F_REWARD_METHOD_TYPE)
    @SerializedName(RewardStrategyEntity.F_REWARD_METHOD_TYPE)
    @JSONField(name = RewardStrategyEntity.F_REWARD_METHOD_TYPE)
    private String rewardMethodType;

    @JsonProperty(value = RewardStrategyEntity.F_DISTRIBUTE_METHOD)
    @SerializedName(RewardStrategyEntity.F_DISTRIBUTE_METHOD)
    @JSONField(name = RewardStrategyEntity.F_DISTRIBUTE_METHOD)
    private String distributeMethod;

    @JsonProperty(value = RewardStrategyEntity.F_EXPIRED_DAYS)
    @SerializedName(RewardStrategyEntity.F_EXPIRED_DAYS)
    @JSONField(name = RewardStrategyEntity.F_EXPIRED_DAYS)
    private Integer expiredDays;


    @JsonProperty(value = RewardStrategyEntity.F_RANDOM_REWARD_LEVELS)
    @SerializedName(RewardStrategyEntity.F_RANDOM_REWARD_LEVELS)
    @JSONField(name = RewardStrategyEntity.F_RANDOM_REWARD_LEVELS)
    private List<RandomRewardLevelDTO> randomRewardLevels;

    @JsonProperty(value = RewardStrategyEntity.F_REWARD_QUANTITY)
    @SerializedName(RewardStrategyEntity.F_REWARD_QUANTITY)
    @JSONField(name = RewardStrategyEntity.F_REWARD_QUANTITY)
    private BigDecimal rewardQuantity;

    @JsonProperty(value = RewardStrategyEntity.F_REWARD_REMARK)
    @SerializedName(RewardStrategyEntity.F_REWARD_REMARK)
    @JSONField(name = RewardStrategyEntity.F_REWARD_REMARK)
    private String rewardRemark;

    @JsonProperty(value = RewardStrategyEntity.F_REWARD_COUNT)
    @SerializedName(RewardStrategyEntity.F_REWARD_COUNT)
    @JSONField(name = RewardStrategyEntity.F_REWARD_COUNT)
    private Integer rewardCount;

    @JsonProperty(value = RewardStrategyEntity.F_DAILY_REWARD_LIMIT)
    @SerializedName(RewardStrategyEntity.F_DAILY_REWARD_LIMIT)
    @JSONField(name = RewardStrategyEntity.F_DAILY_REWARD_LIMIT)
    private Integer dailyRewardLimit;

    @JsonProperty(value = RewardStrategyEntity.F_INDIVIDUAL_REWARD_LIMIT)
    @SerializedName(RewardStrategyEntity.F_INDIVIDUAL_REWARD_LIMIT)
    @JSONField(name = RewardStrategyEntity.F_INDIVIDUAL_REWARD_LIMIT)
    private Integer individualRewardLimit;


    @JsonProperty(value = RewardStrategyEntity.F_REWARD_GET_METHOD)
    @SerializedName(RewardStrategyEntity.F_REWARD_GET_METHOD)
    @JSONField(name = RewardStrategyEntity.F_REWARD_GET_METHOD)
    private List<String> rewardGetMethod;


    @JsonProperty(value = RewardStrategyEntity.F_EXCEPTION_STRATEGY)
    @SerializedName(RewardStrategyEntity.F_EXCEPTION_STRATEGY)
    @JSONField(name = RewardStrategyEntity.F_EXCEPTION_STRATEGY)
    private String exceptionStrategy;

    @JsonProperty(value = RewardStrategyEntity.F_EXCEPTION_ACTION_ID)
    @SerializedName(RewardStrategyEntity.F_EXCEPTION_ACTION_ID)
    @JSONField(name = RewardStrategyEntity.F_EXCEPTION_ACTION_ID)
    private String exceptionActionId;

    @JsonProperty(value = RewardStrategyEntity.F_EXCEPTION_TYPES)
    @SerializedName(RewardStrategyEntity.F_EXCEPTION_TYPES)
    @JSONField(name = RewardStrategyEntity.F_EXCEPTION_TYPES)
    private List<String> exceptionTypes;

    @JsonProperty(value = RewardStrategyEntity.F_REBATE_USE_TYPE)
    @SerializedName(RewardStrategyEntity.F_REBATE_USE_TYPE)
    @JSONField(name = RewardStrategyEntity.F_REBATE_USE_TYPE)
    private String rebateUseType;



    public RewardStrategyEntity toEntity() {
        RewardStrategyEntity rewardStrategyEntity = new RewardStrategyEntity();
        rewardStrategyEntity.setRewardMethod(this.rewardMethod);
        if (this.rewardQuantity != null) {
            rewardStrategyEntity.setRewardQuantity(this.rewardQuantity.toString());
        }
        rewardStrategyEntity.setRewardMethodType(this.rewardMethodType);
        rewardStrategyEntity.setRandomRewardLevels(new ArrayList<>());
        rewardStrategyEntity.setDistributeMethod(this.distributeMethod);
        rewardStrategyEntity.setExpiredDays(this.expiredDays);
        rewardStrategyEntity.setRewardCount(this.rewardCount);
        rewardStrategyEntity.setDailyRewardLimit(this.dailyRewardLimit);
        rewardStrategyEntity.setIndividualRewardLimit(this.individualRewardLimit);
        rewardStrategyEntity.setRewardGetMethod(this.rewardGetMethod);
        rewardStrategyEntity.setExceptionStrategy(this.exceptionStrategy);
        rewardStrategyEntity.setExceptionTypes(this.exceptionTypes);
        rewardStrategyEntity.setExceptionActionId(this.exceptionActionId);
        rewardStrategyEntity.setRebateUseType(this.rebateUseType);
        if (this.randomRewardLevels != null) {
            for (RandomRewardLevelDTO randomRewardLevelDTO : this.randomRewardLevels) {
                rewardStrategyEntity.getRandomRewardLevels().add(randomRewardLevelDTO.toEntity());
            }
        }
        rewardStrategyEntity.setRewardRemark(this.rewardRemark);
        return rewardStrategyEntity;
    }

    public static RewardStrategyDTO fromEntity(RewardStrategyEntity rewardStrategyEntity) {
        RewardStrategyDTO rewardStrategyDTO = new RewardStrategyDTO();
        rewardStrategyDTO.setRewardMethod(rewardStrategyEntity.getRewardMethod());
        if (rewardStrategyEntity.getRewardQuantity() != null) {
            rewardStrategyDTO.setRewardQuantity(new BigDecimal(rewardStrategyEntity.getRewardQuantity()));
        }
        rewardStrategyDTO.setRewardMethodType(rewardStrategyEntity.getRewardMethodType());
        rewardStrategyDTO.setRandomRewardLevels(new ArrayList<>());
        rewardStrategyDTO.setDistributeMethod(rewardStrategyEntity.getDistributeMethod());
        rewardStrategyDTO.setExpiredDays(rewardStrategyEntity.getExpiredDays());
        rewardStrategyDTO.setRewardCount(rewardStrategyEntity.getRewardCount());
        rewardStrategyDTO.setDailyRewardLimit(rewardStrategyEntity.getDailyRewardLimit());
        rewardStrategyDTO.setIndividualRewardLimit(rewardStrategyEntity.getIndividualRewardLimit());
        rewardStrategyDTO.setRewardGetMethod(rewardStrategyEntity.getRewardGetMethod());
        rewardStrategyDTO.setExceptionStrategy(rewardStrategyEntity.getExceptionStrategy());
        rewardStrategyDTO.setExceptionTypes(rewardStrategyEntity.getExceptionTypes());
        rewardStrategyDTO.setExceptionActionId(rewardStrategyEntity.getExceptionActionId());
        rewardStrategyDTO.setRebateUseType(rewardStrategyEntity.getRebateUseType());
        if (rewardStrategyEntity.getRandomRewardLevels() != null) {
            for (RandomRewardLevelEntity randomRewardLevelEntity : rewardStrategyEntity.getRandomRewardLevels()) {
                rewardStrategyDTO.getRandomRewardLevels().add(RandomRewardLevelDTO.fromEntity(randomRewardLevelEntity));
            }
        }
        rewardStrategyDTO.setRewardRemark(rewardStrategyEntity.getRewardRemark());
        return rewardStrategyDTO;
    }
}
