package com.facishare.crm.fmcg.tpm.dao.mongo.po;


/**
 * <AUTHOR>
 */
public enum ActionModeType {

    ADD("add", "新建"),
    EDIT("edit", "编辑"),
    CHANGE("change", "变更"),

    INVALID("invalid", "作废"),
    CANCEL("cancel", "取消"),
    AGREEMENT_ABANDON("agreement_abandon", "协议终止"),
    RANDOM_AUDIT("random_audit", "抽检"),

    STORE_WRITE_OFF("store_write_off", "门店核销"),
    CLOSURE("closure", "结案");

    private final String key;
    private final String label;

    ActionModeType(String key, String label) {
        this.key = key;
        this.label = label;
    }

    public String getKey() {
        return this.key;
    }

    public String getLabel() {
        return this.label;
    }

}
