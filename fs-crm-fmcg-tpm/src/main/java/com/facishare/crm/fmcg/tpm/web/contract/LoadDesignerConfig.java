package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.NodePermissionVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 17:36
 */
public interface LoadDesignerConfig {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "_id")
        @JsonProperty(value = "_id")
        @SerializedName("_id")
        private String id;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "enable_charge_up")
        @JsonProperty(value = "enable_charge_up")
        @SerializedName("enable_charge_up")
        private Boolean chargeUpEnable;

        @JSONField(name = "node_permission")
        @JsonProperty(value = "node_permission")
        @SerializedName("node_permission")
        private NodePermissionVO nodePermission;

        @JSONField(name = "enable_audit_mode")
        @JsonProperty(value = "enable_audit_mode")
        @SerializedName("enable_audit_mode")
        private Boolean enableAuditMode;

        @JSONField(name = "enable_agreement_store_confirm")
        @JsonProperty(value = "enable_agreement_store_confirm")
        @SerializedName("enable_agreement_store_confirm")
        private Boolean enableAgreementStoreConfirm;

        @JSONField(name = "enable_cost_assign")
        @JsonProperty(value = "enable_cost_assign")
        @SerializedName("enable_cost_assign")
        private Boolean enableCostAssign;

        @JSONField(name = "enable_exists_store_write_off")
        @JsonProperty(value = "enable_exists_store_write_off")
        @SerializedName("enable_exists_store_write_off")
        private Boolean enableExistsStoreWriteOff;

        @JSONField(name = "enable_rebate")
        @JsonProperty(value = "enable_rebate")
        @SerializedName("enable_rebate")
        private Boolean enableRebate;

        @JSONField(name = "enable_ai")
        @JsonProperty(value = "enable_ai")
        @SerializedName("enable_ai")
        private Boolean enableAi;

        @JSONField(name = "show_ai_config")
        @JsonProperty(value = "show_ai_config")
        @SerializedName("show_ai_config")
        private Boolean showAiConfig;

    }
}
