package com.facishare.crm.fmcg.tpm.dao.pg;

import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityPlanGroupByTypeDatumPO;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityPlanUnauditedStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO;
import com.facishare.crm.fmcg.tpm.dao.pg.po.OptionPO;
import com.facishare.paas.metadata.ratelimit.DBLimit;
import com.facishare.paas.metadata.ratelimit.MethodType;
import com.github.mybatis.mapper.ITenant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;

import java.util.List;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/9 19:00
 */
public interface ActivityMapper extends ITenant<ActivityMapper> {

    @Results({
            @Result(property = "activityTypeId", column = "activity_type_id"),
            @Result(property = "count", column = "count"),
            @Result(property = "status", column = "status")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<ActivityPlanGroupByTypeDatumPO> countByActivityType(@Param("tenant_id") String tenantId);

    @Results({
            @Result(property = "activityTypeId", column = "activity_type_id"),
            @Result(property = "activityPlanId", column = "activity_plan_id"),
            @Result(property = "unauditedCount", column = "unaudited_count")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<ActivityPlanUnauditedStatisticsDatumPO> unauditedStatistics(
            @Param("tenant_id") String tenantId,
            @Param("proof_object_api_name") String proofObjectApiName,
            @Param("proof_table_name") String proofTableName,
            @Param("proof_table_is_mt_data") boolean proofTableIsMtData,
            @Param("proof_table_reference_field_name") String proofTableReferenceFieldName,
            @Param("proof_table_reference_field_is_extend") boolean proofTableReferenceFieldIsExtend,
            @Param("proof_table_life_status_field_name") String proofTableLifeStatusFieldName,
            @Param("proof_table_life_status_field_is_extend") boolean proofTableLifeStatusFieldIsExtend,
            @Param("proof_audit_table_reference_field_name") String proofAuditTableReferenceFieldName,
            @Param("proof_audit_table_reference_field_is_extend") boolean proofAuditTableReferenceFieldIsExtend,
            @Param("activity_type_id_list") List<String> activityTypeIdList
    );

    /**
     * main data of audit list for admin
     * 2.0
     */
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<Map<String, Object>> queryAuditList(@Param("tenant_id") String tenantId,

                                             @Param("proof_object_api_name") String proofObjectApiName,
                                             @Param("proof_table_is_mt_data") boolean proofTableIsMtData,
                                             @Param("proof_table_name") String proofTableName,
                                             @Param("proof_table_has_extend_field") boolean proofTableHasExtendField,

                                             @Param("proof_table_dealer_id_field_is_extend") boolean proofTableDealerIdFieldIsExtend,
                                             @Param("proof_table_dealer_id_field") String proofTableDealerIdField,

                                             @Param("proof_table_activity_id_field_is_extend") boolean proofTableActivityIdFieldIsExtend,
                                             @Param("proof_table_activity_id_field") String proofTableActivityIdField,

                                             @Param("has_proof_cost_field") boolean hasProofCostField,
                                             @Param("proof_table_cost_field_is_extend") boolean proofTableCostFieldIsExtend,
                                             @Param("proof_table_cost_field") String proofTableCostField,

                                             @Param("proof_table_life_status_field_is_extend") boolean proofTableLifeStatusFieldIsExtend,
                                             @Param("proof_table_life_status_field") String proofTableLifeStatusField,

                                             @Param("audit_table_proof_id_field_is_extend") boolean auditTableProofIdFieldIsExtend,
                                             @Param("audit_table_proof_id_field") String auditTableProofIdField,
                                             @Param("audit_table_object_record_type") String auditTableRecordType,

                                             @Param("activity_type_id") String activityTypeId,
                                             @Param("activity_id") String activityId,
                                             @Param("dealer_id") String dealerId,
                                             @Param("begin_date") Long beginDate,
                                             @Param("end_date") Long endDate,

                                             @Param("activity_owner") String activityOwner,

                                             @Param("activity_begin_date") Long activityBeginDate,
                                             @Param("activity_end_date") Long activityEndDate,
                                             @Param("activity_status") String activityStatus,
                                             @Param("activity_closed_status") String activityClosedStatus,

                                             @Param("activity_create_time_begin_date") Long activityCreateTimeBeginDate,
                                             @Param("activity_create_time_end_date") Long activityCreateTimeEndDate,

                                             @Param("number_filter_string") String numberFilterString,
                                             @Param("activity_data_auth_code") String activityAuthCode,
                                             @Param("order_by") String orderBy,
                                             @Param("limit") long limit,
                                             @Param("offset") long offset);

    /**
     * main data of audit list for admin
     * 2.0
     */
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<Map<String, Object>> queryAuditListForFuMao(@Param("tenant_id") String tenantId,

                                                     @Param("proof_object_api_name") String proofObjectApiName,
                                                     @Param("proof_table_is_mt_data") boolean proofTableIsMtData,
                                                     @Param("proof_table_name") String proofTableName,
                                                     @Param("proof_table_has_extend_field") boolean proofTableHasExtendField,

                                                     @Param("proof_table_dealer_id_field_is_extend") boolean proofTableDealerIdFieldIsExtend,
                                                     @Param("proof_table_dealer_id_field") String proofTableDealerIdField,

                                                     @Param("proof_table_activity_id_field_is_extend") boolean proofTableActivityIdFieldIsExtend,
                                                     @Param("proof_table_activity_id_field") String proofTableActivityIdField,

                                                     @Param("has_proof_cost_field") boolean hasProofCostField,
                                                     @Param("proof_table_cost_field_is_extend") boolean proofTableCostFieldIsExtend,
                                                     @Param("proof_table_cost_field") String proofTableCostField,

                                                     @Param("proof_table_life_status_field_is_extend") boolean proofTableLifeStatusFieldIsExtend,
                                                     @Param("proof_table_life_status_field") String proofTableLifeStatusField,

                                                     @Param("audit_table_proof_id_field_is_extend") boolean auditTableProofIdFieldIsExtend,
                                                     @Param("audit_table_proof_id_field") String auditTableProofIdField,

                                                     @Param("activity_type_id") String activityTypeId,
                                                     @Param("activity_id") String activityId,
                                                     @Param("dealer_id") String dealerId,
                                                     @Param("begin_date") Long beginDate,
                                                     @Param("end_date") Long endDate,

                                                     @Param("activity_owner") String activityOwner,

                                                     @Param("activity_begin_date") Long activityBeginDate,
                                                     @Param("activity_end_date") Long activityEndDate,

                                                     @Param("activity_create_time_begin_date") Long activityCreateTimeBeginDate,
                                                     @Param("activity_create_time_end_date") Long activityCreateTimeEndDate,

                                                     @Param("number_filter_string") String numberFilterString,
                                                     @Param("limit") long limit,
                                                     @Param("offset") long offset,
                                                     @Param("assistant_department_ids") List<String> assistantDepartmentIds
    );

    /**
     * main data of audit list for admin
     * 2.0
     */
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<Map<String, Object>> queryAuditListWithOutDealer(@Param("tenant_id") String tenantId,

                                                          @Param("proof_object_api_name") String proofObjectApiName,
                                                          @Param("proof_table_is_mt_data") boolean proofTableIsMtData,
                                                          @Param("proof_table_name") String proofTableName,
                                                          @Param("proof_table_has_extend_field") boolean proofTableHasExtendField,

                                                          @Param("proof_table_activity_id_field_is_extend") boolean proofTableActivityIdFieldIsExtend,
                                                          @Param("proof_table_activity_id_field") String proofTableActivityIdField,

                                                          @Param("has_proof_cost_field") boolean hasProofCostField,
                                                          @Param("proof_table_cost_field_is_extend") boolean proofTableCostFieldIsExtend,
                                                          @Param("proof_table_cost_field") String proofTableCostField,

                                                          @Param("proof_table_life_status_field_is_extend") boolean proofTableLifeStatusFieldIsExtend,
                                                          @Param("proof_table_life_status_field") String proofTableLifeStatusField,

                                                          @Param("audit_table_proof_id_field_is_extend") boolean auditTableProofIdFieldIsExtend,
                                                          @Param("audit_table_proof_id_field") String auditTableProofIdField,
                                                          @Param("audit_table_object_record_type") String auditTableRecordType,

                                                          @Param("activity_type_id") String activityTypeId,
                                                          @Param("activity_id") String activityId,
                                                          @Param("dealer_id") String dealerId,
                                                          @Param("begin_date") Long beginDate,
                                                          @Param("end_date") Long endDate,

                                                          @Param("activity_owner") String activityOwner,

                                                          @Param("activity_begin_date") Long activityBeginDate,
                                                          @Param("activity_end_date") Long activityEndDate,
                                                          @Param("activity_status") String activityStatus,
                                                          @Param("activity_closed_status") String activityClosedStatus,

                                                          @Param("activity_create_time_begin_date") Long activityCreateTimeBeginDate,
                                                          @Param("activity_create_time_end_date") Long activityCreateTimeEndDate,

                                                          @Param("number_filter_string") String numberFilterString,
                                                          @Param("activity_data_auth_code") String activityAuthCode,
                                                          @Param("order_by") String orderBy,
                                                          @Param("limit") long limit,
                                                          @Param("offset") long offset
    );


    /**
     * main data of audit list for admin
     * 2.0
     */
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<Map<String, Object>> queryAuditListWithOutDealerForFuMao(@Param("tenant_id") String tenantId,

                                                                 @Param("proof_object_api_name") String proofObjectApiName,
                                                                 @Param("proof_table_is_mt_data") boolean proofTableIsMtData,
                                                                 @Param("proof_table_name") String proofTableName,
                                                                 @Param("proof_table_has_extend_field") boolean proofTableHasExtendField,

                                                                 @Param("proof_table_activity_id_field_is_extend") boolean proofTableActivityIdFieldIsExtend,
                                                                 @Param("proof_table_activity_id_field") String proofTableActivityIdField,

                                                                 @Param("has_proof_cost_field") boolean hasProofCostField,
                                                                 @Param("proof_table_cost_field_is_extend") boolean proofTableCostFieldIsExtend,
                                                                 @Param("proof_table_cost_field") String proofTableCostField,

                                                                 @Param("proof_table_life_status_field_is_extend") boolean proofTableLifeStatusFieldIsExtend,
                                                                 @Param("proof_table_life_status_field") String proofTableLifeStatusField,

                                                                 @Param("audit_table_proof_id_field_is_extend") boolean auditTableProofIdFieldIsExtend,
                                                                 @Param("audit_table_proof_id_field") String auditTableProofIdField,

                                                                 @Param("activity_type_id") String activityTypeId,
                                                                 @Param("activity_id") String activityId,
                                                                 @Param("dealer_id") String dealerId,
                                                                 @Param("begin_date") Long beginDate,
                                                                 @Param("end_date") Long endDate,

                                                                 @Param("activity_owner") String activityOwner,

                                                                 @Param("activity_begin_date") Long activityBeginDate,
                                                                 @Param("activity_end_date") Long activityEndDate,

                                                                 @Param("activity_create_time_begin_date") Long activityCreateTimeBeginDate,
                                                                 @Param("activity_create_time_end_date") Long activityCreateTimeEndDate,

                                                                 @Param("number_filter_string") String numberFilterString,

                                                                 @Param("limit") long limit,
                                                                 @Param("offset") long offset,
                                                                 @Param("assistant_department_ids") List<String> assistantDepartmentIds);

    @Results({
            @Result(property = "label", column = "label"),
            @Result(property = "value", column = "value")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<OptionPO> queryOptionsByActivityType(@Param("tenant_id") String tenantId,
                                              @Param("activity_type_id") String activityTypeId,
                                              @Param("data_auth_code") String dataAuthCode);

    @Results({
            @Result(property = "label", column = "label"),
            @Result(property = "value", column = "value")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<OptionPO> queryDealerOptionsByType(
            @Param("tenant_id") String tenantId,
            @Param("api_name") String apiName,
            @Param("table_name") String tableName,
            @Param("is_mt_data") boolean isMtData,
            @Param("dealer_column_name") String dealerColumnName,
            @Param("dealer_column_is_extend") boolean dealerColumnIsExtend,
            @Param("activity_column_name") String activityColumnName,
            @Param("activity_column_is_extend") boolean activityColumnIsExtend,
            @Param("activity_type_id") String activityTypeId
    );

    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<Map<String, Object>> queryEnableActivity(
            @Param("tenant_id") String tenantId,
            @Param("store_id") String storeId,
            @Param("dealer_id") String dealerId,
            @Param("now") long now,
            @Param("skip_department_check") boolean skipDepartmentCheck,
            @Param("department_ids") List<String> departmentIds,
            @Param("skip_activity_type_check") boolean skipActivityTypeCheck,
            @Param("activity_types") List<String> activityTypes
    );

    @Results({
            @Result(property = "status", column = "status"),
            @Result(property = "count", column = "count")
    })
    @DBLimit(
            eiIndex = 1,
            argType = String.class,
            methodType = MethodType.QUERY
    )
    List<ActivityTypeStatisticsDatumPO> queryActivityTypeStatisticsData(
            @Param("tenant_id") String tenantId,
            @Param("activity_type_id") String activityTypeId
    );
}
