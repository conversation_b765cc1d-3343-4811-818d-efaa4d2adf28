package com.facishare.crm.fmcg.tpm.web.contract;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityTypeVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * description : 通过modelId和ruleId查询ActivityType的契约
 * <p>
 * create time: 2023/11/01
 */
public interface QueryActivityTypeByModelAndRule {

    @Data
    class Arg implements Serializable {
        /**
         * 模型ID
         */
        private String modelId;

        /**
         * 规则ID
         */
        private String ruleId;
    }

    @Data
    class Result implements Serializable {

        /**
         * 活动类型数据
         */
        private List<ActivityTypeVO> activityTypeList;
    }

    @Data
    class ActivityTypeVO {
        /**
         * 活动类型ID
         */
        private String id;

        /**
         * 活动类型名称
         */
        private String name;

        private String ruleId;
    }


} 