package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.PromotionPolicyPO;
import de.lab4inf.math.util.Strings;
import org.apache.commons.collections.MapUtils;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/6/28 20:59
 */
public class PromotionPolicyDAO extends UniqueIdBaseDAO<PromotionPolicyPO> {


    protected PromotionPolicyDAO(Class<PromotionPolicyPO> clazz) {
        super(clazz);
    }


    public PromotionPolicyPO find(String tenantId, String objectId, String objectApiName) {
        Query<PromotionPolicyPO> query = mongoContext.createQuery(PromotionPolicyPO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(PromotionPolicyPO.F_REF_OBJECT_ID).equal(objectId);
        query.field(PromotionPolicyPO.F_REF_OBJECT_API_NAME).equal(objectApiName);
        query.order(PromotionPolicyPO.F_LAST_UPDATE_TIME);
        query.limit(1);
        return query.get();
    }

    public void editPromotionPolicy(String tenantId, Integer userId, PromotionPolicyPO promotionPolicyPO) {
        Query<PromotionPolicyPO> query = mongoContext.createQuery(PromotionPolicyPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(promotionPolicyPO.getUniqueId());

        UpdateOperations<PromotionPolicyPO> updateOperations = mongoContext.createUpdateOperations(PromotionPolicyPO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(MongoPO.F_LAST_UPDATER, userId)
                .set(PromotionPolicyPO.F_PRODUCT_GIFT_DATA_JSON, promotionPolicyPO.getProductGiftData());

        if (!Strings.isNullOrEmpty(promotionPolicyPO.getProductGiftDataBackUp())){
            updateOperations.set(PromotionPolicyPO.F_PRODUCT_GIFT_JSON_BACK_UP, promotionPolicyPO.getProductGiftDataBackUp());
        }

        if (MapUtils.isNotEmpty(promotionPolicyPO.getPromotionPolicyIdMap())) {
            updateOperations.set(PromotionPolicyPO.F_PROMOTION_POLICY_IDS, promotionPolicyPO.getPromotionPolicyIdMap());
        }

        if (MapUtils.isNotEmpty(promotionPolicyPO.getUpdateLimitAccountMap())) {
            updateOperations.set(PromotionPolicyPO.F_IS_UPDATE_LIMIT_ACCOUNT, promotionPolicyPO.getUpdateLimitAccountMap());
        }

        mongoContext.update(query, updateOperations);
    }

    public List<PromotionPolicyPO> queryByObjectIds(String tenantId, List<String> objectIds, String objectApiName) {
        Query<PromotionPolicyPO> query = mongoContext.createQuery(PromotionPolicyPO.class).field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(PromotionPolicyPO.F_REF_OBJECT_ID).in(objectIds);
        query.field(PromotionPolicyPO.F_REF_OBJECT_API_NAME).equal(objectApiName);
        query.order(PromotionPolicyPO.F_LAST_UPDATE_TIME);
        query.limit(objectIds.size());
        return query.asList();
    }
}
