package com.facishare.crm.fmcg.tpm.aop;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


@Aspect
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class FacadeAspect {

    @Around(value = "execution(* com.facishare.crm.fmcg.tpm.facade.*.*(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        try {
            return joinPoint.proceed(args);
        } catch (ValidateException | MetaDataBusinessException ve) {
            log.info(String.format("[ValidateException] - method - %s : ", joinPoint.getSignature().getName()), ve);
            throw ve;
        } catch (Exception ex) {
            log.info(String.format("[UnknownException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw new ValidateException(I18N.text(I18NKeys.FACADE_ASPECT_0));
        }
    }
}
