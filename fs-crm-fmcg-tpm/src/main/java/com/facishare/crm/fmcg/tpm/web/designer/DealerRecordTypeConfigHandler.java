package com.facishare.crm.fmcg.tpm.web.designer;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRangeFieldBusiness;
import com.facishare.crm.fmcg.tpm.service.abstraction.ScriptService;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/23 17:24
 */
@Slf4j
@Component
public class DealerRecordTypeConfigHandler implements IConfigHandler {
    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private ScriptService scriptService;

    @Override
    public void validation(String tenantId, ConfigVO vo) {

        List<IRecordTypeOption> recordTypeOptions = serviceFacade.findRecordTypeOptionList(tenantId, ApiNames.ACCOUNT_OBJ, false);

        if (CollectionUtils.isEmpty(recordTypeOptions)) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_RECORD_TYPE_CONFIG_HANDLER_0));
        }
        Map<String, IRecordTypeOption> recordTypeOptionMap = recordTypeOptions.stream().collect(Collectors.toMap(IRecordTypeOption::getApiName, v -> v));

        List<String> recordTypes = new ArrayList<>();
        if (vo.getValue() instanceof List) {
            recordTypes.addAll(vo.getValue());
        } else {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_DEALER_RECORD_TYPE_CONFIG_HANDLER_0));
        }
        recordTypes.forEach(recordType -> {
            if (!recordTypeOptionMap.containsKey(recordType)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.DEALER_RECORD_TYPE_CONFIG_HANDLER_1), vo.getValue()));
            }
            IRecordTypeOption recordTypeOption = recordTypeOptionMap.get(recordType);
            if (Boolean.FALSE.equals(recordTypeOption.isActive())) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.DEALER_RECORD_TYPE_CONFIG_HANDLER_2), vo.getValue()));
            }
        });
    }

    @Override
    public void after(String tenantId, ConfigVO vo) {
        //todo:  判断是否更改了业务类型才需要执行
        updateCondition(tenantId);
        //更新活动协议经销商数据范围条件
        Map<String, String> apiNameFieldNameMap = Maps.newHashMap();
        apiNameFieldNameMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.DEALER_ID);
        apiNameFieldNameMap.put(ApiNames.TPM_DEALER_ACTIVITY_COST, TPMDealerActivityCostFields.DEALER_ID);

        for (Map.Entry<String, String> entry : apiNameFieldNameMap.entrySet()) {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, entry.getKey());
            if (Objects.isNull(describe)) {
                log.info("TPMActivityAgreementObj is not found . tenantId={} ", tenantId);
                return;
            }

            Integer index = null;
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(entry.getValue());
            if (!Objects.isNull(fieldDescribe) && fieldDescribe instanceof ObjectReferenceFieldDescribe) {
                ObjectReferenceFieldDescribe objectReferenceField = (ObjectReferenceFieldDescribe) fieldDescribe;

                List<LinkedHashMap> wheres = objectReferenceField.getWheres();
                for (Map where : wheres) {
                    JSONArray filters = JSON.parseArray(JSON.toJSONString(where.get("filters")));
                    for (int i = 0; i < filters.size(); i++) {
                        JSONObject filter = filters.getJSONObject(i);
                        String fieldName = filter.getString("field_name");
                        if ("record_type".equals(fieldName)) {
                            String fieldValue = filter.getJSONArray("field_values").getString(0);
                            if (!vo.getValue().equals(fieldValue)) {
                                filter.put("field_values", vo.getValue());
                                index = i;
                            }
                            String operator = filter.getString("operator");
                            if ("EQ".equalsIgnoreCase(operator)) {
                                filter.put("operator", "IN");
                            }
                            if ("NEQ".equalsIgnoreCase(operator)) {
                                filter.put("operator", "NIN");
                            }
                            break;
                        }
                    }
                    if (index != null) {
                        where.put("filters", filters);
                    }
                }
                ((ObjectReferenceFieldDescribe) fieldDescribe).setWheres(wheres);
            }

            if (index != null) {
                serviceFacade.updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
            }
        }
    }

    private void updateCondition(String tenantId) {

        ParallelUtils.createParallelTask().submit(() -> {
            scriptService.updateStoreRange(Collections.singletonList(tenantId), true);
        }).run();
    }
}
