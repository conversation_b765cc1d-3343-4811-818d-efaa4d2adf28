package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface BatchEnableV2 {

    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("store_ids")
        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        private List<String> storeIds;

        @SerializedName("use_cache")
        @JSONField(name = "use_cache")
        @JsonProperty("use_cache")
        private boolean useCache = true;
    }

    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable {

        @SerializedName("enable_data")
        @JSONField(name = "enable_data")
        @JsonProperty("enable_data")
        private Map<String, Boolean> enableData;
    }
}