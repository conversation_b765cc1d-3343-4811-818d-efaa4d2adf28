package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.NodeType;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IReportDataLoader;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/22 19:06
 */
public class ActivityReportLoaderFactory {

    protected static final Map<String, String> RESOLVE_MAP = new HashMap<>();
    protected static final String DEFAULT_BEAN_NAME = "customNodeManager";

    static {
        RESOLVE_MAP.put(NodeType.PLAN_TEMPLATE.value(), "activityPlanTemplateManager");
        RESOLVE_MAP.put(NodeType.PLAN.value(), "activityPlanManager");
        RESOLVE_MAP.put(NodeType.AGREEMENT.value(), "activityAgreementManager");
        RESOLVE_MAP.put(NodeType.PROOF.value(), "activityProofManager");
        RESOLVE_MAP.put(NodeType.AUDIT.value(), "activityAuditManager");
        RESOLVE_MAP.put(NodeType.WRITE_OFF.value(), "activityWriteOffManager");
        RESOLVE_MAP.put(NodeType.COST_ASSIGN.value(), "activityCostAssignManager");
        RESOLVE_MAP.put(NodeType.STORE_WRITE_OFF.value(), "activityStoreWriteOffManager");
        RESOLVE_MAP.put(NodeType.CUSTOM.value(), "customNodeManager");
    }

    private ActivityReportLoaderFactory() {
    }

    public static IReportDataLoader resolve(String nodeType) {
        String name = RESOLVE_MAP.getOrDefault(nodeType, DEFAULT_BEAN_NAME);
        return SpringUtil.getContext().getBean(name, IReportDataLoader.class);
    }
}
