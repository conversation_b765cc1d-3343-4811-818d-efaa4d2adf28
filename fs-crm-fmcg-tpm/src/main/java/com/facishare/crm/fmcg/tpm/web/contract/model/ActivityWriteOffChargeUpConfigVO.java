package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffChargeUpConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 19:42
 */
@Data
@ToString
public class ActivityWriteOffChargeUpConfigVO implements Serializable {

    @JSONField(name = "cash_account_id")
    @JsonProperty(value = "cash_account_id")
    @SerializedName("cash_account_id")
    private String cashAccountId;


    @JSONField(name = "return_goods_quantity_account_id")
    @JsonProperty(value = "return_goods_quantity_account_id")
    @SerializedName("return_goods_quantity_account_id")
    private String returnGoodsQuantityAccountId;


    @JSONField(name = "charge_up_type")
    @JsonProperty(value = "charge_up_type")
    @SerializedName("charge_up_type")
    private String chargeUpType;

    @JSONField(name = "charge_up_account_status")
    @JsonProperty(value = "charge_up_account_status")
    @SerializedName("charge_up_account_status")
    private Boolean chargeUpAccountStatus;

    @JSONField(name = "enable_edit_default_account")
    @JsonProperty(value = "enable_edit_default_account")
    @SerializedName("enable_edit_default_account")
    private Boolean enableEditDefaultAccount;

    public static ActivityWriteOffChargeUpConfigVO fromPO(ActivityWriteOffChargeUpConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityWriteOffChargeUpConfigVO vo = new ActivityWriteOffChargeUpConfigVO();
        vo.setChargeUpType(po.getChargeUpType());
        vo.setCashAccountId(po.getCashAccountId());
        vo.setReturnGoodsQuantityAccountId(po.getReturnGoodsQuantityAccountId());
        vo.setChargeUpAccountStatus(po.getChargeUpAccountStatus());
        vo.setEnableEditDefaultAccount(po.getEnableEditDefaultAccount());
        return vo;
    }
}
