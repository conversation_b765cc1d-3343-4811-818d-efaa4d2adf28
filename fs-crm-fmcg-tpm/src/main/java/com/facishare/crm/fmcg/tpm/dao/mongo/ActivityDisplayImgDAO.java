package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityDisplayImgPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.StatusType;
import com.google.common.base.Strings;
import org.mongodb.morphia.query.Query;
import org.mongodb.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 活动展示图片DAO
 */
@Repository
@SuppressWarnings("Duplicates")
public class ActivityDisplayImgDAO extends UniqueIdBaseDAO<ActivityDisplayImgPO> {

    protected ActivityDisplayImgDAO() {
        super(ActivityDisplayImgPO.class);
    }

    /**
     * 获取所有活动展示图片
     * 
     * @param tenantId 租户ID
     * @param onlyNormal 是否只获取正常状态的
     * @param includeDeleted 是否包含已删除的
     * @return 活动展示图片列表
     */
    public List<ActivityDisplayImgPO> all(String tenantId, boolean onlyNormal, boolean includeDeleted) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId);
        if (onlyNormal) {
            query.field(ActivityDisplayImgPO.F_STATUS).equal(StatusType.NORMAL.value());
        }
        if (!includeDeleted) {
            query.field(MongoPO.F_IS_DELETED).equal(false);
        }
        return query.asList();
    }

    /**
     * 根据举证ID获取活动展示图片
     * 
     * @param tenantId 租户ID
     * @param proofId 举证ID
     * @return 活动展示图片列表
     */
    public List<ActivityDisplayImgPO> findByProofId(String tenantId, String proofId) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityDisplayImgPO.F_PROOF_ID).equal(proofId);
        return query.asList();
    }

    /**
     * 根据拜访ID获取活动展示图片
     * 
     * @param tenantId 租户ID
     * @param visitId 拜访ID
     * @return 活动展示图片列表
     */
    public List<ActivityDisplayImgPO> findByVisitId(String tenantId, String visitId) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityDisplayImgPO.F_VISIT_ID).equal(visitId);
        return query.asList();
    }

    /**
     * 分页获取活动展示图片
     * 
     * @param tenantId 租户ID
     * @param keyword 搜索关键字
     * @param limit 限制条数
     * @param offset 偏移量
     * @return 活动展示图片列表
     */
    public List<ActivityDisplayImgPO> list(String tenantId, String keyword, int limit, int offset) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .order("-" + MongoPO.F_LAST_UPDATE_TIME)
                .offset(offset);
        if (limit != -1) {
            query.limit(limit);
        }
        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityDisplayImgPO.F_N_PATH).containsIgnoreCase(keyword);
        }
        return query.asList();
    }

    /**
     * 统计数量
     * 
     * @param tenantId 租户ID
     * @param keyword 搜索关键字
     * @return 数量
     */
    public long count(String tenantId, String keyword) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false);

        if (!Strings.isNullOrEmpty(keyword)) {
            query.field(ActivityDisplayImgPO.F_N_PATH).containsIgnoreCase(keyword);
        }

        return query.countAll();
    }

    /**
     * 更新活动展示图片
     * 
     * @param tenantId 租户ID
     * @param uniqueId 唯一ID
     * @param operator 操作人
     * @param nPath 图片路径
     * @param status 状态
     * @param errorMessage 错误信息
     */
    public void edit(String tenantId,
                     String uniqueId,
                     int operator,
                     String nPath,
                     String status,
                     String errorMessage) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityDisplayImgPO> updateOperations = mongoContext.createUpdateOperations(ActivityDisplayImgPO.class)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis())
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(ActivityDisplayImgPO.F_N_PATH, nPath)
                .set(ActivityDisplayImgPO.F_STATUS, status)
                .set(ActivityDisplayImgPO.F_ERROR_MESSAGE, errorMessage);

        mongoContext.update(query, updateOperations);
    }

    /**
     * 设置状态
     * 
     * @param tenantId 租户ID
     * @param operator 操作人
     * @param uniqueId 唯一ID
     * @param status 状态
     */
    public void setStatus(String tenantId, int operator, String uniqueId, String status) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        UpdateOperations<ActivityDisplayImgPO> updateOperations = mongoContext.createUpdateOperations(ActivityDisplayImgPO.class)
                .set(ActivityDisplayImgPO.F_STATUS, status)
                .set(MongoPO.F_LAST_UPDATER, operator)
                .set(MongoPO.F_LAST_UPDATE_TIME, System.currentTimeMillis());

        mongoContext.update(query, updateOperations);
    }

    /**
     * 根据举证ID列表获取活动展示图片
     * 
     * @param tenantId 租户ID
     * @param proofIds 举证ID列表
     * @return 活动展示图片列表
     */
    public List<ActivityDisplayImgPO> findByProofIds(String tenantId, List<String> proofIds) {
        Query<ActivityDisplayImgPO> query = mongoContext.createQuery(ActivityDisplayImgPO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_IS_DELETED).equal(false)
                .field(ActivityDisplayImgPO.F_PROOF_ID).in(proofIds)
                .field(ActivityDisplayImgPO.F_STATUS).equal(StatusType.ERROR.value());
        return query.asList();
    }
} 