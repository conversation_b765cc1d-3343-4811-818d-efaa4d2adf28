package com.facishare.crm.fmcg.tpm.api.scan;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * Author: linmj
 * Date: 2023/9/26 11:49
 */
@Data
@ToString
public class ProductInfoDTO {

    private String code;

    @JSONField(name = "product_name")
    @JsonProperty(value = "product_name")
    @SerializedName("product_name")
    private String productName;

    @JSONField(name = "product_code")
    @JsonProperty(value = "product_code")
    @SerializedName("product_code")
    private String productCode;

    @JSONField(name = "product_value")
    @JsonProperty(value = "product_value")
    @SerializedName("product_value")
    private BigDecimal productValue;

    @JSONField(name = "product_image_url")
    @JsonProperty(value = "product_image_url")
    @SerializedName("product_image_url")
    private String productImageUrl;

    private String date;

    private String store;

    private boolean sold;

    @JSONField(name = "original_amount")
    @JsonProperty(value = "original_amount")
    @SerializedName("original_amount")
    private BigDecimal originalAmount;

    @JSONField(name = "need_pay")
    @JsonProperty(value = "need_pay")
    @SerializedName("need_pay")
    private BigDecimal needPay;

    @JSONField(name = "deduct_amount")
    @JsonProperty(value = "deduct_amount")
    @SerializedName("deduct_amount")
    private BigDecimal deductAmount;

    @JSONField(name = "group_id")
    @JsonProperty(value = "group_id")
    @SerializedName("group_id")
    private String groupId;

    @JSONField(name = "rebate_name")
    @JsonProperty(value = "rebate_name")
    @SerializedName("rebate_name")
    private String rebateName;
}
