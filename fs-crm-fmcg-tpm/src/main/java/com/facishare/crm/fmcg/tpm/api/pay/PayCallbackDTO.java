package com.facishare.crm.fmcg.tpm.api.pay;

import lombok.Data;
import lombok.ToString;

/**
 * Author: linmj
 * Date: 2023/9/13 14:34
 */
@Data
@ToString
public class PayCallbackDTO {

    private String toEA;

    private String enterpriseAccount;

    private String fsUserId;

    private String toUserId;

    //订单号
    private String orderNo;

    //业务方单号 业务自定义号
    private String merchantOrderNo;

    //业务对应的编码 例如tpm是 1017
    private String goodsId;

    //1完成 2初始化
    private Integer status;

    //支付金额
    private Long amount;

    //手续费
    private Long feeAmount;

    private String goodsName;

    //支付方式 ALIPAY, WEIXIN, BANK, WALLET, APPLEPAY
    private Integer payType;


    private Long responseTime;

    private Long requestTime;

    private String finalPayOrderNo;

}
