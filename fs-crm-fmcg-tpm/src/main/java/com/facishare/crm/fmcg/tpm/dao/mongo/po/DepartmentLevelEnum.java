package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/9/14 19:30
 */
public enum DepartmentLevelEnum {
    ONE("1", "一","fmcg.reward_rule.department_level.1"),
    TWO("2", "二","fmcg.reward_rule.department_level.2"),
    THREE("3", "三","fmcg.reward_rule.department_level.3"),
    FOUR("4", "四","fmcg.reward_rule.department_level.4"),
    FIVE("5", "五","fmcg.reward_rule.department_level.5"),;


    DepartmentLevelEnum(String code, String describe,String i18nKey) {
        this.code = code;
        this.describe = describe;
        this.i18nKey = i18nKey;
    }

    private static final Map<String, DepartmentLevelEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(DepartmentLevelEnum::code, rewardTypeEnum -> rewardTypeEnum));
    private String code;

    private String describe;

    private String i18nKey;

    public String code() {
        return this.code;
    }

    public String describe() {
        return this.describe;
    }

    public String i18nDescribe() {
        String text;
        if (this.i18nKey == null) {
            text = this.describe;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.describe;
            }
        }
        return text;
    }

    public static DepartmentLevelEnum get(String code) {
        return CODE_MAP.get(code);
    }
}
