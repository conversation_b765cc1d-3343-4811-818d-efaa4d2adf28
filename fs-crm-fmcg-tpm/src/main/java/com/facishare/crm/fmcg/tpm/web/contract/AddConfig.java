package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AddConfig {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "configs")
        @JsonProperty(value = "configs")
        @SerializedName("configs")
        private List<ConfigVO> configs;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {
    }
}
