package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.facishare.crm.fmcg.tpm.dao.pg.po.OptionPO;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/26 15:36
 */
@Data
@ToString
@Builder
public class OptionVO implements Serializable {

    private String value;

    private String label;

    public static OptionVO fromOptionPO(OptionPO po) {
        if (po == null) {
            return null;
        }
        return OptionVO.builder()
                .value(po.getValue())
                .label(po.getLabel())
                .build();
    }
}