package com.facishare.crm.fmcg.tpm.utils;

import lombok.Getter;

@Getter
public enum I18NEnums {

    ACTIVITY_REWARD_HAS_SENT_ALL("fmcg.activity_reward_has_sent_all", "活动奖品已经全部发放完毕，欢迎下次参与。"),
    SCANNING_BY_OTHERS("fmcg.scanning_by_others", "该码正在被他人扫描中，请稍后再试。"),
    PLEASE_SCAN_CORRECT_QR_CODE("fmcg.please_scan_correct_qr_code", "请扫描正确的二维码"),
    LAST_REWARD_NODE_MUST_BE_CONSUMER("fmcg.last_reward_node_must_be_consumer", "最后一个奖品节点必须是消费者"),
    JUST_ONLY_ONE_CONSUMER_NODE("fmcg.just_only_one_consumer_node", "只能有一个消费者节点"),
    CONSUMER_REWARD_ACTION_MUST_BE_CONSUMER_SCAN_REWARD("fmcg.consumer_reward_action_must_be_consumer_scan_reward", "消费者奖励动作必须是消费者扫码奖励"),
    PERSONAL_GET_COUNT_MUST_BIGGER_THAN_ZERO("fmcg.personal_get_count_must_bigger_than_zero", "单人累计领取限量不能小于0。"),
    PERSONAL_DAILY_GET_COUNT_MUST_BIGGER_THAN_ZERO("fmcg.personal_daily_get_count_must_bigger_than_zero", "单人每日领取限量不能小于0。"),
    RANDOM_RED_PACKET_NUMBER_CAN_NOT_BE_EMPTY("fmcg.random_red_packet_number_can_not_be_empty", "随机红包数量不能为空"),
    RED_PACKET_NUMBER_CAN_NOT_BE_EMPTY("fmcg.red_packet_number_can_not_be_empty", "红包数量不能为空"),
    FRESHNESS_MATCHING_1("fmcg.freshness_matching_1", "按剩余有效期匹配时不允许设置生产日期。"),
    FRESHNESS_MATCHING_2("fmcg.freshness_matching_2", "按生产日期匹时不允许设置临期时间。"),
    FRESHNESS_MATCHING_3("fmcg.freshness_matching_3", "请填写【活动品项保鲜度标准】"),
    PLEASE_FILL_ACTIVITY_PRIZE_DETAIL("fmcg.please_fill_activity_prize_detail", "实物激励需填写【活动奖品】从对象"),
    FORBIDDEN_DELETE_ACTIVITY_REWARD("fmcg.forbidden_delete_activity_reward", "禁止删除活动奖品"),
    NO_REWARD_ACTIVITY("fmcg.no_reward_activity", "该产品码没有可参与的消费者活动。"),
    RELEASE_NODE_NOT_RELATED_FROZEN_DATA("fmcg.release_node_not_related_frozen_data", "该数据未关联冻结数据，无法进行消费规则中间释放。"),
    EXISTS_WITHHOLDING_DETAIL_CAN_NOT_DEDUCT("fmcg.exists_withholding_detail_can_not_deduct", "存在未转化为冻结明细的预提单据，无法进行扣减，请核查对应冻结对象的预提单据。"),
    WITHHOLDING_MAPPING_IS_LACKING("fmcg.withholding_mapping_is_lacking", "该单据缺失消费规则里定义的相关预提映射值，请依照消费规则核查数据。"),
    DEDUCT_HAS_NOT_RELATED_FROZEN_DATA("fmcg.deduct_has_not_related_frozen_data", "该数据未关联冻结数据或数据没进行冻结金额，无法进行消费规则扣减。"),
    REWARD_EXCEPTION_TYPES_IS_NOT_EMPTY("fmcg.reward_exception_types_is_not_empty", "不发放奖励时，激励策略的异常类型不能为空"),
    REWARD_EXCEPTION_TYPES_IS_EMPTY("fmcg.reward_exception_types_is_empty", "照常发放时，激励策略的异常类型不能有值"),


    ACTIVITY_DISPLAY_NO_MATCH_BY_AI_IDENTIFY("fmcg.activity_display_no_match_by_ai_identify", "活动类型中开启了举证AI识别，活动项目中的陈列标准字段和标准陈列形式不匹配"),
    ACTIVITY_DISPLAY_NO_EMPTY_BY_AI_IDENTIFY("fmcg.activity_display_no_empty_by_ai_identify", "活动类型中开启了举证AI识别，活动项目中的陈列形式不能为空"),
    ACTIVITY_ITEM_NO_EMPTY_BY_AI_IDENTIFY("fmcg.activity_item_no_empty_by_ai_identify", "活动类型中开启了举证AI识别，活动项目不能为空"),
    PROOF_PERIOD_COUNT_EXCEED_MAX_COUNT("fmcg.proof_period_count_exceed_max_count", "举证时段数量已超过20个，请调整数量。"),
    DISPLAY_PROOF_IMG_NOT_EMPTY_BY_AI_IDENTIFY("fmcg.display_proof_img_not_empty_by_ai_identify", "举证开启了AI动作，陈列举证图片从对象不能为空"),
    PROOF_NOT_ADD_CHECK_IN_PROOF_EXISTS("fmcg.proof_not_add_check_in_proof_exists", "外勤动作下存在已经关联的举证，不能新建"),
    PROOF_NOT_ENABLE_RANGE_DISABLE_ADD("fmcg.proof_not_enable_range_disable_add", "举证不在可举证的时段内，不能新建"),
    DISPLAY_PROOF_IMG_PROOF_IMG_FIELD_NOT_EMPTY("fmcg.display_proof_img_proof_img_field_not_empty", "举证陈列图片中的举证图片字段不能为空"),
    DISPLAY_PROOF_IMG_NOT_ALL_EMPTY("fmcg.display_proof_img_not_all_empty", "举证陈列图片不可都为空"),
    DISPLAY_PROOF_IMG_EXISTS_DUPLICATE_ITEM_DETAIL("fmcg.display_proof_img_exists_duplicate_item_detail", "举证陈列图片中存在重复的陈列形式、活动项目组合"),
    DISPLAY_PROOF_IMG_WITH_ITEM_DETAIL_NO_MATCH("fmcg.display_proof_img_with_item_detail_no_match", "举证陈列图片中的陈列形式、活动项目与%s项目不匹配"),
    PROOF_PERIOD_NOT_IN_ACTIVITY_TIME_RANGE("fmcg.proof_period_not_in_activity_time_range", "举证时段不在活动的开始结束时间范围内!"),
    PROOF_PERIOD_EXISTS_DUPLICATE_PERIOD_IN_ACTIVITY("fmcg.proof_period_exists_duplicate_period_in_activity", "活动中的举证时段存在重复时段。"),
    ACTIVITY_AGREEMENT_NEED_PROOF_PERIOD_IN_ACTIVITY("fmcg.activity_agreement_need_proof_period_in_activity", "协议活动需要举证，当前协议周期不在活动举证阶段内，请调整协议周期后重新提交");




    private String code;

    private String description;

    I18NEnums(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
