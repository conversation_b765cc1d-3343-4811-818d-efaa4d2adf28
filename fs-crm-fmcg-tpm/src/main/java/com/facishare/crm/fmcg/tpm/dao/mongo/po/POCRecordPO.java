package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.fmcg.framework.http.contract.tpm.PocDailyAccount;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * activity type po
 * create by @yangqf
 * create time 2021/11/15 16:00
 */
@Entity(value = "fmcg_tpm_poc_record", noClassnameStored = true)
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class POCRecordPO extends MongoPO {

    public static final String F_DATE = "date";
    public static final String F_DEALER_ID_MAP = "dealer_id_map";
    public static final String F_ACTIVITY_DEALER_IDS = "activity_dealer_ids";
    public static final String F_ACTIVITY_AGREEMENT_DEALER_IDS = "activity_agreement_dealer_ids";


    @Property(F_DATE)
    private Long date;


    @Embedded(F_DEALER_ID_MAP)
    private Map<String, List<LoopRouteAccountEntity>> dealerIdAndAccountInfosMap;

    @Embedded(F_ACTIVITY_DEALER_IDS)
    private List<String> activityDealerIds;
    @Embedded(F_ACTIVITY_AGREEMENT_DEALER_IDS)
    private List<String> activityAgreementDealerIds;

    public static Map<String, List<LoopRouteAccountEntity>> of(Map<String, List<PocDailyAccount.LoopRouteAccountInfo>> dealerIdAndAccountInfosMap) {
        Map<String, List<LoopRouteAccountEntity>> newMap = Maps.newHashMap();
        if (dealerIdAndAccountInfosMap == null) {
            return newMap;
        }

        for (Map.Entry<String, List<PocDailyAccount.LoopRouteAccountInfo>> entry : dealerIdAndAccountInfosMap.entrySet()) {
            newMap.put(entry.getKey(), entry.getValue().stream().map(v -> {
                LoopRouteAccountEntity loopRouteAccountEntity = new LoopRouteAccountEntity();
                loopRouteAccountEntity.setLoopType(v.getLoopType());
                loopRouteAccountEntity.setLoopTypeValue(v.getLoopTypeValue());
                loopRouteAccountEntity.setAccountIds(v.getAccountIds());
                return loopRouteAccountEntity;
            }).collect(Collectors.toList()));
        }
        return newMap;
    }
}
