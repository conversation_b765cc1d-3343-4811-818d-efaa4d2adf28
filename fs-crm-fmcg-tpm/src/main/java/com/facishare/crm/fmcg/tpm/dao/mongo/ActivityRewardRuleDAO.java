package com.facishare.crm.fmcg.tpm.dao.mongo;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDetailEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardNodeEntity;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import org.bson.types.ObjectId;
import org.mongodb.morphia.query.Query;

import java.util.List;
import java.util.Objects;

/**
 * Author: linmj
 * Date: 2023/9/14 16:59
 */
public class ActivityRewardRuleDAO extends UniqueIdBaseDAO<ActivityRewardRulePO> {


    protected ActivityRewardRuleDAO(Class<ActivityRewardRulePO> clazz) {
        super(clazz);
    }

    public List<ActivityRewardRulePO> queryByRelatedObject(String tenantId, String relatedObjectApiName, List<String> relatedObjectId) {
        Query<ActivityRewardRulePO> query = mongoContext.createQuery(ActivityRewardRulePO.class);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_API_NAME).equal(relatedObjectApiName);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_ID).in(relatedObjectId);
        query.field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(MongoPO.F_IS_DELETED).equal(false);

        return query.asList();
    }

    public List<ActivityRewardRulePO> queryByRelatedObjectAndAction(String tenantId, String relatedObjectApiName, List<String> relatedObjectId, String action) {
        Query<ActivityRewardRulePO> query = mongoContext.createQuery(ActivityRewardRulePO.class);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_API_NAME).equal(relatedObjectApiName);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_ID).in(relatedObjectId);
        query.field(ActivityRewardRulePO.F_TRIGGER_TYPE).equal(action);
        query.field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(MongoPO.F_IS_DELETED).equal(false);

        return query.asList();
    }


    public ActivityRewardRulePO getByRelatedObject(String tenantId, String relatedObjectApiName, String relatedObjectId) {
        Query<ActivityRewardRulePO> query = mongoContext.createQuery(ActivityRewardRulePO.class);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_API_NAME).equal(relatedObjectApiName);
        query.field(ActivityRewardRulePO.F_RELATED_OBJECT_ID).equal(relatedObjectId);
        query.field(MongoPO.F_TENANT_ID).equal(tenantId);
        query.field(MongoPO.F_IS_DELETED).equal(false);

        return query.get();
    }

    public List<ActivityRewardRulePO> queryByTriggerTypeAndDimension(String tenantId, String rewardType, String triggerType, String dimension) {
        Query<ActivityRewardRulePO> query = mongoContext.createQuery(ActivityRewardRulePO.class);
        query.field(ActivityRewardRulePO.F_TRIGGER_TYPE).equal(triggerType);
        if (!"all".equals(dimension)) {
            query.field(ActivityRewardRulePO.F_TRIGGER_DIMENSION).equal(dimension);
        }
        query.field(ActivityRewardRulePO.F_RULE_TYPE).equal(rewardType);
        query.field(ActivityRewardRulePO.F_TENANT_ID).equal(tenantId);
        query.field(MongoPO.F_IS_DELETED).equal(false);
        query.order(MongoPO.F_CREATE_TIME);
        return query.asList();
    }

    public String save(ActivityRewardRulePO t) {
        if (t.getCreator() != null) {
            t.setCreator(-10000);
        }
        if (Objects.isNull(t.getCreateTime())) {
            t.setCreateTime(System.currentTimeMillis());
        }
        if (Strings.isNullOrEmpty(t.getUniqueId())) {
            if (t.getId() != null) {
                t.setUniqueId(t.getId().toString());
            } else {
                String uniqueId = IdGenerator.get();
                t.setId(new ObjectId(uniqueId));
                t.setUniqueId(uniqueId);
            }
        }
        t.getRewardDetails().forEach(rewardDetailEntity -> {
            if (Strings.isNullOrEmpty(rewardDetailEntity.getDetailId())) {
                rewardDetailEntity.setDetailId(IdGenerator.get());
            }
        });
        return mongoContext.save(t).getId().toString();
    }

/*    public void edit(String tenantId, String uniqueId, ActivityRewardRulePO po) {
        Query<ActivityRewardRulePO> query = mongoContext.createQuery(ActivityRewardRulePO.class)
                .field(MongoPO.F_TENANT_ID).equal(tenantId)
                .field(MongoPO.F_UNIQUE_ID).equal(uniqueId);

        po.setLastUpdater(-10000);
        po.setLastUpdateTime(System.currentTimeMillis());

        UpdateOperations<ActivityRewardRulePO> updateOperations = mongoContext.createUpdateOperations(ActivityRewardRulePO.class);
        updateOperations.set(ActivityRewardRulePO.F_REWARD_DETAILS, po.getRewardDetails());
        updateOperations.set(ActivityRewardRulePO.F_LAST_UPDATER, po.getLastUpdater());
        updateOperations.set(ActivityRewardRulePO.F_LAST_UPDATE_TIME, po.getLastUpdateTime());
        updateOperations.set(ActivityRewardRulePO.F_TRIGGER_TYPE, po.getTriggerType());
        updateOperations.set(ActivityRewardRulePO.F_RULE_TYPE, po.getRuleType());

        mongoContext.updateFirst(query, updateOperations, false);
    }*/

}
