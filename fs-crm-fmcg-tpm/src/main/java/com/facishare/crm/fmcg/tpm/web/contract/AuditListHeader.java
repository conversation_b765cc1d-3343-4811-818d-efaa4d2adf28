package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.OptionVO;
import com.facishare.paas.I18N;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/26 03:35
 */
public interface AuditListHeader {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_type_id")
        @JsonProperty(value = "activity_type_id")
        @SerializedName("activity_type_id")
        private String activityTypeId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "header_list")
        @JsonProperty(value = "header_list")
        @SerializedName("header_list")
        private List<HeaderVO> headerList;

        @JSONField(name = "audit_action_list")
        @JsonProperty(value = "audit_action_list")
        @SerializedName("audit_action_list")
        private List<AuditActionVO> auditActionList;

        @JSONField(name = "have_activity_write_off")
        @JsonProperty(value = "have_activity_write_off")
        @SerializedName("have_activity_write_off")
        private boolean haveActivityWriteOff;
    }

    @Data
    @ToString
    @Builder
    class AuditActionVO implements Serializable {

        @JSONField(name = "audit_action_name")
        @JsonProperty(value = "audit_action_name")
        @SerializedName("audit_action_name")
        private String auditActionName;

        @JSONField(name = "object_record_type")
        @JsonProperty(value = "object_record_type")
        @SerializedName("object_record_type")
        private String objectRecordType;

        @JSONField(name = "is_default")
        @JsonProperty(value = "is_default")
        @SerializedName("is_default")
        private boolean isDefault;
    }

    @Data
    @ToString
    @Builder
    class HeaderVO implements Serializable {

        public static List<HeaderVO> getDefaultHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("dealer_id").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_DEALER)).type("object_reference").referenceObjectApiName("AccountObj").build(),
                    HeaderVO.builder().key("activity_id").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_PLAN)).type("object_reference").referenceObjectApiName("TPMActivityObj").build(),
                    HeaderVO.builder().key("begin_date").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_START_DATE)).type("date").build(),
                    HeaderVO.builder().key("end_date").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_END_DATE)).type("date").build(),
                    HeaderVO.builder().key("create_time").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_CREATE_TIME)).type("date").build(),
                    HeaderVO.builder().key("owner").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_OWNER)).type("employee").build(),
                    HeaderVO.builder().key("activity_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AMOUNT)).type("number").build(),
                    HeaderVO.builder().key("activity_status").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_STATUS)).type("select_one").options(
                            Lists.newArrayList(
                                    OptionVO.builder().value(TPMActivityFields.ACTIVITY_STATUS__SCHEDULE).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_STATUS_SCHEDULE)).build(),
                                    OptionVO.builder().value(TPMActivityFields.ACTIVITY_STATUS__IN_PROGRESS).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_STATUS_IN_PROGRESS)).build(),
                                    OptionVO.builder().value(TPMActivityFields.ACTIVITY_STATUS__END).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_STATUS_END)).build(),
                                    OptionVO.builder().value(TPMActivityFields.ACTIVITY_STATUS__CLOSED).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_STATUS_CLOSE)).build()
                            )
                    ).build(),
                    HeaderVO.builder().key("closed_status").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_CLOSED_STATUS)).type("select_one").options(
                            Lists.newArrayList(
                                    OptionVO.builder().value(TPMActivityFields.CLOSE_STATUS__CLOSED).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_CLOSED_STATUS_CLOSED)).build(),
                                    OptionVO.builder().value(TPMActivityFields.CLOSE_STATUS__UNCLOSED).label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_CLOSED_STATUS_UNCLOSED)).build()
                            )
                    ).build());
        }

        public static List<HeaderVO> getProofAmountHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("proof_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_PROOF_AMOUNT)).type("number").build()
            );
        }

        public static List<HeaderVO> getAuditHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("audited_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AUDITED_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("unaudited_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_UNAUDITED_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("pass_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_PASS_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("reject_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_REJECT_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("audited_proof_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AUDITED_PROOF_AMOUNT)).type("number").build()
            );
        }

        public static List<HeaderVO> getRandomAuditHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("random_audit_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_RANDOM_AUDIT_COUNT)).type("number").build(),
                    HeaderVO.builder().key("random_audit_rate").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_RANDOM_AUDIT_RATE)).type("percentile").build(),
                    HeaderVO.builder().key("auto_audit_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AUTO_AUDIT_COUNT)).type("number").build()
            );
        }

        public static List<HeaderVO> getCustomAuditHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("audited_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AUDITED_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("unaudited_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_UNAUDITED_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("pass_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_PASS_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("reject_proof_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_REJECT_PROOF_COUNT)).type("number").build(),
                    HeaderVO.builder().key("audited_proof_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_AUDITED_PROOF_AMOUNT)).type("number").build()
            );
        }

        public static List<HeaderVO> getWriteOffHeaderList() {
            return Lists.newArrayList(
                    HeaderVO.builder().key("confirmed_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_CONFIRMED_AMOUNT)).type("number").build(),
                    HeaderVO.builder().key("unconfirmed_amount").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_UNCONFIRMED_AMOUNT)).type("number").build(),
                    HeaderVO.builder().key("confirmed_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_CONFIRMED_COUNT)).type("number").build(),
                    HeaderVO.builder().key("unconfirmed_count").label(I18N.text(I18NKeys.ACTIVITY_AUDIT_LIST_HEADER_ACTIVITY_UNCONFIRMED_COUNT)).type("number").build()
            );
        }


        private String key;

        private String label;

        @JSONField(name = "type")
        @JsonProperty(value = "type")
        @SerializedName("type")
        private String type;

        @JSONField(name = "reference_object_api_name")
        @JsonProperty(value = "reference_object_api_name")
        @SerializedName("reference_object_api_name")
        private String referenceObjectApiName;

        private boolean hide;

        private List<OptionVO> options;
    }
}
