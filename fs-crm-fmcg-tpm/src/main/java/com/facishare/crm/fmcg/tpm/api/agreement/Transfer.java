package com.facishare.crm.fmcg.tpm.api.agreement;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 12/3/20 7:58 PM
 */
public interface Transfer {

    @Data
    @ToString
    class Arg implements Serializable {

        private String id;

        private Integer departmentId;
    }

    @Data
    @ToString
    class DistinctArg implements Serializable {

        private String flag;
    }

    @Data
    @ToString
    class Result implements Serializable {
    }
}
