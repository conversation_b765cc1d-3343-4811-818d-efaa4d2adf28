<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.crm.fmcg.tpm.dao.pg.ActivityProofMapper">
    <select id="queryAuditSummaryGroupByAccount"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.AuditSummaryGroupByAccountPO">
        select
        *
        from (
        select
        t_account.id as id,
        t_account.name as name,
        t_proof_statistics.proof_count,
        t_proof_statistics.unaudited_proof_count,
        t_proof_statistics.pass_proof_count,
        t_proof_statistics.reject_proof_count,
        t_proof_statistics.proof_id_list,
        count(1) over () as total_row_count
        from (
        select
        t_proof.tenant_id,
        <if test="proof_table_account_id_field_is_extend == false">
            t_proof.${proof_table_account_id_field} as account_id,
        </if>
        <if test="proof_table_account_id_field_is_extend == true">
            t_proof_mt_data.${proof_table_account_id_field} as account_id,
        </if>
        count(1) as proof_count,
        <if test="view_mode == 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            when
            t_audit.audit_status is not null and
            t_audit.random_audit_status = 'unchecked'
            then 1
            end
            ) as unaudited_proof_count,
        </if>
        <if test="view_mode != 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            end
            ) as unaudited_proof_count,
        </if>

        count(
        case
        when t_audit.audit_status = 'pass' then 1
        end
        ) as pass_proof_count,
        count(
        case
        when t_audit.audit_status = 'reject' then 1
        end
        ) as reject_proof_count,
        max(t_proof.create_time) as create_time,
        <if test="view_mode == 'audit'">
            array_agg(
            case
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                when t_audit.audit_status is null then t_proof.id
                when
                t_audit.audit_status is not null
                and t_audit.random_audit_status = 'unchecked'
                then t_proof.id
                else ''
            </if>
            <if test="audit_mode == 'all'">
                when
                t_audit.audit_status is null
                and t_audit.random_audit_status is null
                then t_proof.id
                else ''
            </if>
            end
            ) as proof_id_list
        </if>
        <if test="view_mode == 'view'">
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                array_agg(t_proof.id) as proof_id_list
            </if>
            <if test="audit_mode == 'all'">
                array_agg(
                case
                when t_audit.random_audit_status is null then t_proof.id
                else ''
                end
                ) as proof_id_list
            </if>
        </if>
        <if test="view_mode != 'audit' and view_mode != 'view'">
            array_agg(t_proof.id) as proof_id_list
        </if>
        from ${proof_table_name} t_proof
        <if test="proof_table_is_mt_data == false and proof_table_has_extend_field == true">
            left join mt_data t_proof_mt_data
            on t_proof.tenant_id = t_proof_mt_data.tenant_id
            and t_proof.id = t_proof_mt_data.udf_obj_id
            and t_proof_mt_data.object_describe_api_name = #{proof_object_api_name}
        </if>

        <if test="audit_table_proof_id_field_is_extend == false">
            left join fmcg_tpm_activity_proof_audit t_audit
            on t_proof.tenant_id = t_audit.tenant_id
            and t_proof.id = t_audit.${audit_table_proof_id_field}
            and t_audit.is_deleted = 0
            and t_audit.record_type = #{audit_object_record_type}
            and t_audit.life_status = 'normal'
        </if>

        <if test="audit_table_proof_id_field_is_extend == true">
            left join (select table_audit.tenant_id as t_audit_tenant_id,
            table_audit.audit_status,
            table_audit.audit_total,
            table_audit.random_audit_status,
            table_audit_mt_data.${audit_table_proof_id_field}
            from fmcg_tpm_activity_proof_audit table_audit
            left join mt_data table_audit_mt_data
            on table_audit.tenant_id = table_audit_mt_data.tenant_id
            and table_audit.id = table_audit_mt_data.udf_obj_id
            and table_audit_mt_data.object_describe_api_name = 'TPMActivityProofAuditObj'
            and table_audit.is_deleted = 0
            and table_audit.record_type = #{audit_object_record_type}
            ) t_audit
            on t_proof.tenant_id = t_audit.t_audit_tenant_id
            and t_audit.${audit_table_proof_id_field} = t_proof.id
        </if>
        where t_proof.is_deleted = 0
        and t_proof.tenant_id = #{tenant_id}
        and t_proof.object_describe_api_name = #{proof_object_api_name}
        and t_proof.create_time between ${begin_date} and ${end_date}
        <if test="proof_table_life_status_field_is_extend == false">
            and t_proof.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_life_status_field_is_extend == true">
            and t_proof_mt_data.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_dealer_id_field_is_extend == false">
            <if test="dealer_id != null and dealer_id != ''">
                and t_proof.${proof_table_dealer_id_field} = #{dealer_id}
            </if>
            <if test="dealer_id == null">
                and t_proof.${proof_table_dealer_id_field} IS NULL
            </if>
        </if>
        <if test="proof_table_dealer_id_field_is_extend == true">
            <if test="dealer_id != null and dealer_id != ''">
                and t_proof_mt_data.${proof_table_dealer_id_field} = #{dealer_id}
            </if>
            <if test="dealer_id == null">
                and t_proof_mt_data.${proof_table_dealer_id_field} IS NULL
            </if>
        </if>
        <if test="proof_table_activity_id_field_is_extend == false">
            and t_proof.${proof_table_activity_id_field} = #{activity_id}
        </if>
        <if test="proof_table_activity_id_field_is_extend == true">
            and t_proof_mt_data.${proof_table_activity_id_field} = #{activity_id}
        </if>
        group by t_proof.tenant_id,
        <if test="proof_table_account_id_field_is_extend == false">
            t_proof.${proof_table_account_id_field}
        </if>
        <if test="proof_table_account_id_field_is_extend == true">
            t_proof_mt_data.${proof_table_account_id_field}
        </if>
        ) t_proof_statistics
        inner join biz_account t_account
        on t_account.tenant_id = t_proof_statistics.tenant_id
        and t_account.id = t_proof_statistics.account_id
        where t_proof_statistics.proof_count >= #{minimum_proof_count}
        <if test="view_mode == 'audit'">
            and t_proof_statistics.unaudited_proof_count > 0
        </if>
        <if test="store_name != null and store_name != ''">
            and t_account.name = #{store_name}
        </if>
        order by t_proof_statistics.create_time
        <if test="is_asc == false">
            desc
        </if>
        ) t_outer
        where '1' = '1'
        order by proof_count desc,
                 id desc
        limit #{limit} offset #{offset};
    </select>

    <select id="queryAuditSummaryGroupByAccountWithOutDealer"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.AuditSummaryGroupByAccountPO">
        select
        *
        from (
        select
        t_account.id as id,
        t_account.name as name,
        t_proof_statistics.proof_count,
        t_proof_statistics.unaudited_proof_count,
        t_proof_statistics.pass_proof_count,
        t_proof_statistics.reject_proof_count,
        t_proof_statistics.proof_id_list,
        count(1) over () as total_row_count
        from (
        select
        t_proof.tenant_id,
        <if test="proof_table_account_id_field_is_extend == false">
            t_proof.${proof_table_account_id_field} as account_id,
        </if>
        <if test="proof_table_account_id_field_is_extend == true">
            t_proof_mt_data.${proof_table_account_id_field} as account_id,
        </if>
        count(1) as proof_count,
        <if test="view_mode == 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            when
            t_audit.audit_status is not null and
            t_audit.random_audit_status = 'unchecked'
            then 1
            end
            ) as unaudited_proof_count,
        </if>
        <if test="view_mode != 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            end
            ) as unaudited_proof_count,
        </if>
        count(
        case
        when t_audit.audit_status = 'pass' then 1
        end
        ) as pass_proof_count,
        count(
        case
        when t_audit.audit_status = 'reject' then 1
        end
        ) as reject_proof_count,
        max(t_proof.create_time) as create_time,
        <if test="view_mode == 'audit'">
            array_agg(
            case
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                when t_audit.audit_status is null then t_proof.id
                when
                t_audit.audit_status is not null
                and t_audit.random_audit_status = 'unchecked'
                then t_proof.id
            </if>
            <if test="audit_mode == 'all'">
                when
                t_audit.audit_status is null
                and t_audit.random_audit_status is null
                then t_proof.id
            </if>
            end
            ) as proof_id_list
        </if>
        <if test="view_mode == 'view'">
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                array_agg(t_proof.id) as proof_id_list
            </if>
            <if test="audit_mode == 'all'">
                array_agg(
                case
                when
                t_audit.random_audit_status is null then t_proof.id
                else ''
                end
                ) as proof_id_list
            </if>
        </if>
        <if test="view_mode != 'audit' and  view_mode != 'view'">
            array_agg(t_proof.id) as proof_id_list
        </if>
        from ${proof_table_name} t_proof
        <if test="proof_table_is_mt_data == false and proof_table_has_extend_field == true">
            left join mt_data t_proof_mt_data
            on t_proof.tenant_id = t_proof_mt_data.tenant_id
            and t_proof.id = t_proof_mt_data.udf_obj_id
            and t_proof_mt_data.object_describe_api_name = #{proof_object_api_name}
        </if>

        <if test="audit_table_proof_id_field_is_extend == false">
            left join fmcg_tpm_activity_proof_audit t_audit
            on t_proof.tenant_id = t_audit.tenant_id
            and t_proof.id = t_audit.${audit_table_proof_id_field}
            and t_audit.is_deleted = 0
            and t_audit.record_type = #{audit_object_record_type}
            and t_audit.life_status = 'normal'
        </if>

        <if test="audit_table_proof_id_field_is_extend == true">
            left join (select table_audit.tenant_id as t_audit_tenant_id,
            table_audit.audit_status,
            table_audit.audit_total,
            table_audit.random_audit_status,
            table_audit_mt_data.${audit_table_proof_id_field}
            from fmcg_tpm_activity_proof_audit table_audit
            left join mt_data table_audit_mt_data
            on table_audit.tenant_id = table_audit_mt_data.tenant_id
            and table_audit.id = table_audit_mt_data.udf_obj_id
            and table_audit_mt_data.object_describe_api_name = 'TPMActivityProofAuditObj'
            and table_audit.is_deleted = 0
            and table_audit.record_type = #{audit_object_record_type}
            ) t_audit
            on t_proof.tenant_id = t_audit.t_audit_tenant_id
            and t_audit.${audit_table_proof_id_field} = t_proof.id
        </if>
        where t_proof.is_deleted = 0
        and t_proof.tenant_id = #{tenant_id}
        and t_proof.object_describe_api_name = #{proof_object_api_name}
        and t_proof.create_time between ${begin_date} and ${end_date}
        <if test="proof_table_life_status_field_is_extend == false">
            and t_proof.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_life_status_field_is_extend == true">
            and t_proof_mt_data.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_activity_id_field_is_extend == false">
            and t_proof.${proof_table_activity_id_field} = #{activity_id}
        </if>
        <if test="proof_table_activity_id_field_is_extend == true">
            and t_proof_mt_data.${proof_table_activity_id_field} = #{activity_id}
        </if>
        group by t_proof.tenant_id,
        <if test="proof_table_account_id_field_is_extend == false">
            t_proof.${proof_table_account_id_field}
        </if>
        <if test="proof_table_account_id_field_is_extend == true">
            t_proof_mt_data.${proof_table_account_id_field}
        </if>
        ) t_proof_statistics
        inner join biz_account t_account
        on t_account.tenant_id = t_proof_statistics.tenant_id
        and t_account.id = t_proof_statistics.account_id
        where t_proof_statistics.proof_count >= #{minimum_proof_count}
        <if test="view_mode == 'audit'">
            and t_proof_statistics.unaudited_proof_count > 0
        </if>
        <if test="store_name != null and store_name != ''">
            and t_account.name = #{store_name}
        </if>
        order by t_proof_statistics.create_time
        <if test="is_asc == false">
            desc
        </if>
        ) t_outer
        where '1' = '1'
        order by proof_count desc,
                 id desc
        limit #{limit} offset #{offset};
    </select>


    <select id="queryAuditSummaryWithOutStore"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.AuditSummaryGroupByAccountPO">
        select
        t_proof_statistics.proof_count,
        t_proof_statistics.unaudited_proof_count,
        t_proof_statistics.pass_proof_count,
        t_proof_statistics.reject_proof_count,
        t_proof_statistics.proof_id_list,
        count(1) over () as total_row_count
        from (
        select
        count(1) as proof_count,
        <if test="view_mode == 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            when
            t_audit.audit_status is not null and
            t_audit.random_audit_status = 'unchecked'
            then 1
            end
            ) as unaudited_proof_count,
        </if>
        <if test="view_mode != 'audit'">
            count(
            case
            when t_audit.audit_status is null then 1
            end
            ) as unaudited_proof_count,
        </if>
        count(
        case
        when t_audit.audit_status = 'pass' then 1
        end
        ) as pass_proof_count,
        count(
        case
        when t_audit.audit_status = 'reject' then 1
        end
        ) as reject_proof_count,
        max(t_proof.create_time) as create_time,
        <if test="view_mode == 'audit'">
            array_agg(
            case
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                when t_audit.audit_status is null then t_proof.id
                when
                t_audit.audit_status is not null
                and t_audit.random_audit_status = 'unchecked'
                then t_proof.id
            </if>
            <if test="audit_mode == 'all'">
                when
                t_audit.audit_status is null
                and t_audit.random_audit_status is null
                then t_proof.id
            </if>
            end
            ) as proof_id_list
        </if>
        <if test="view_mode == 'view'">
            <if test="audit_mode == 'random' or audit_mode == null or audit_mode == ''">
                array_agg(t_proof.id) as proof_id_list
            </if>
            <if test="audit_mode == 'all'">
                array_agg(
                case
                when
                t_audit.random_audit_status is null then t_proof.id
                else ''
                end
                ) as proof_id_list
            </if>
        </if>
        <if test="view_mode != 'audit' and  view_mode != 'view'">
            array_agg(t_proof.id) as proof_id_list
        </if>
        from ${proof_table_name} t_proof
        <if test="proof_table_is_mt_data == false and proof_table_has_extend_field == true">
            left join mt_data t_proof_mt_data
            on t_proof.tenant_id = t_proof_mt_data.tenant_id
            and t_proof.id = t_proof_mt_data.udf_obj_id
            and t_proof_mt_data.object_describe_api_name = #{proof_object_api_name}
        </if>

        <if test="audit_table_proof_id_field_is_extend == false">
            left join fmcg_tpm_activity_proof_audit t_audit
            on t_proof.tenant_id = t_audit.tenant_id
            and t_proof.id = t_audit.${audit_table_proof_id_field}
            and t_audit.is_deleted = 0
            and t_audit.record_type = #{audit_object_record_type}
            and t_audit.life_status = 'normal'
        </if>

        <if test="audit_table_proof_id_field_is_extend == true">
            left join (select table_audit.tenant_id as t_audit_tenant_id,
            table_audit.audit_status,
            table_audit.audit_total,
            table_audit.random_audit_status,
            table_audit_mt_data.${audit_table_proof_id_field}
            from fmcg_tpm_activity_proof_audit table_audit
            left join mt_data table_audit_mt_data
            on table_audit.tenant_id = table_audit_mt_data.tenant_id
            and table_audit.id = table_audit_mt_data.udf_obj_id
            and table_audit_mt_data.object_describe_api_name = 'TPMActivityProofAuditObj'
            and table_audit.is_deleted = 0
            and table_audit.record_type = #{audit_object_record_type}
            ) t_audit
            on t_proof.tenant_id = t_audit.t_audit_tenant_id
            and t_audit.${audit_table_proof_id_field} = t_proof.id
        </if>
        where t_proof.is_deleted = 0
        and t_proof.tenant_id = #{tenant_id}
        and t_proof.object_describe_api_name = #{proof_object_api_name}
        and t_proof.create_time between ${begin_date} and ${end_date}
        <if test="proof_table_life_status_field_is_extend == false">
            and t_proof.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_life_status_field_is_extend == true">
            and t_proof_mt_data.${proof_table_life_status_field} = 'normal'
        </if>
        <if test="proof_table_activity_id_field_is_extend == false">
            and t_proof.${proof_table_activity_id_field} = #{activity_id}
        </if>
        <if test="proof_table_activity_id_field_is_extend == true">
            and t_proof_mt_data.${proof_table_activity_id_field} = #{activity_id}
        </if>
        ) t_proof_statistics
        where t_proof_statistics.proof_count >= #{minimum_proof_count}
        <if test="view_mode == 'audit'">
            and t_proof_statistics.unaudited_proof_count > 0
        </if>
        and t_proof_statistics.proof_id_list is not null
        order by
        t_proof_statistics.proof_count desc,
        t_proof_statistics.create_time
        <if test="is_asc == false">
            desc
        </if>
        limit #{limit} offset #{offset};
    </select>

    <select id="countByVisitAction" resultType="java.lang.Long">




                                        select count(id)
                                        from fmcg_tpm_activity_proof
                                        where tenant_id = #{tenant_id}
                                          and is_deleted = 0
                                          and life_status = 'normal'
                                          and store_id = #{store_id}
                                          and visit_id = #{visit_id}
                                          and action_id = #{action_id}




    </select>

    <select id="countByActivity" resultType="java.lang.Long">




                                        select count(id)
                                        from fmcg_tpm_activity_proof
                                        where tenant_id = #{tenant_id}
                                          and is_deleted = 0
                                          and life_status = 'normal'
                                          and activity_id = #{activity_id}




    </select>

    <select id="queryActivityTypeStatisticsData"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO">
        select
            table_01.audit_status as status,
            count(*) as count
        from
            fmcg_tpm_activity_proof table_01
        where
            table_01.tenant_id = #{tenant_id}
            and table_01.life_status = 'normal'
            and table_01.is_deleted = 0
            and table_01.activity_id in (
                select
                    table_02.id
                from
                    fmcg_tpm_activity table_02
                where
                    table_02.tenant_id = #{tenant_id}
                    and table_02.is_deleted = 0
                    and table_02.life_status = 'normal'
                    and table_02.activity_type = #{activity_type_id}
                    and table_02.activity_status = 'in_progress'
            )
        group by table_01.audit_status;
    </select>


    <select id="queryCustomActivityTypeStatisticsData"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO">

        <![CDATA[

                            select table_01.${audit_status_field_num} as status,
                                   count(*) as count
                            from mt_data table_01
                                inner join fmcg_tpm_activity table_02
                            on table_01.tenant_id = table_02.tenant_id

                              and table_01.${activity_id_field_num} = table_02.id
                              and table_01.object_describe_api_name = #{object_apiName}
                            where table_01.tenant_id = #{tenant_id}
                              and table_01.${life_status} = 'normal'
                              and table_01.is_deleted = 0
                              and table_02.activity_type = #{activity_type_id}
                            group by table_01.${audit_status_field_num}

                          ]]>;

</select>
</mapper>
