package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityStoreWriteOffSourceConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;


@Data
@ToString
public class ActivityStoreWriteOffSourceConfigVO implements Serializable {

    @JSONField(name = "template_id")
    @JsonProperty(value = "template_id")
    @SerializedName("template_id")
    private String templateId;

    @JSONField(name = "calculate_type")
    @JsonProperty(value = "calculate_type")
    @SerializedName("calculate_type")
    private String calculateType;

    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    @SerializedName("api_name")
    private String apiName;

    @JSONField(name = "record_type")
    @JsonProperty(value = "record_type")
    @SerializedName("record_type")
    private String recordType;

    @JSONField(name = "ref_store_write_off_field_api_name")
    @JsonProperty(value = "ref_store_write_off_field_api_name")
    @SerializedName("ref_store_write_off_field_api_name")
    private String referenceStoreWriteOffFieldApiName;

    @JSONField(name = "reference_activity_field_api_name")
    @JsonProperty(value = "reference_activity_field_api_name")
    @SerializedName("reference_activity_field_api_name")
    private String referenceActivityFieldApiName;

    @JSONField(name = "dealer_field_api_name")
    @JsonProperty(value = "dealer_field_api_name")
    @SerializedName("dealer_field_api_name")
    private String dealerFieldApiName;

    @JSONField(name = "account_field_api_name")
    @JsonProperty(value = "account_field_api_name")
    @SerializedName("account_field_api_name")
    private String accountFieldApiName;

    /**
     * 活动协议字段
     */
    @JSONField(name = "agreement_field_api_name")
    @JsonProperty(value = "agreement_field_api_name")
    @SerializedName("agreement_field_api_name")
    private String agreementFieldApiName;

    @JSONField(name = "cost_field_api_name")
    @JsonProperty(value = "cost_field_api_name")
    @SerializedName("cost_field_api_name")
    private String costFieldApiName;

    @JSONField(name = "display_field_api_names")
    @JsonProperty(value = "display_field_api_names")
    @SerializedName("display_field_api_names")
    private List<String> displayFieldApiNames;

    @JSONField(name = "activity_display_field_api_names")
    @JsonProperty(value = "activity_display_field_api_names")
    @SerializedName("activity_display_field_api_names")
    private List<String> activityDisplayFieldApiNames;

    @JSONField(name = "agreement_display_field_api_names")
    @JsonProperty(value = "agreement_display_field_api_names")
    @SerializedName("agreement_display_field_api_names")
    private List<String> agreementDisplayFieldApiNames;

    @JSONField(name = "store_cost_display_field_api_names")
    @JsonProperty(value = "store_cost_display_field_api_names")
    @SerializedName("store_cost_display_field_api_names")
    private List<String> storeCostDisplayFieldApiNames;

    @JSONField(name = "cashing_product_display_field_api_names")
    @JsonProperty(value = "cashing_product_display_field_api_names")
    @SerializedName("cashing_product_display_field_api_names")
    private List<String> cashingProductDisplayFieldApiNames;

    public static ActivityStoreWriteOffSourceConfigVO fromPO(ActivityStoreWriteOffSourceConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityStoreWriteOffSourceConfigVO vo = new ActivityStoreWriteOffSourceConfigVO();
        vo.setTemplateId(po.getTemplateId());
        vo.setCalculateType(po.getCalculateType());
        vo.setRecordType(po.getRecordType());
        vo.setReferenceStoreWriteOffFieldApiName(po.getReferenceStoreWriteOffFieldApiName());
        vo.setReferenceActivityFieldApiName(po.getReferenceActivityFieldApiName());
        vo.setApiName(po.getApiName());
        vo.setAgreementFieldApiName(po.getAgreementFieldApiName());
        vo.setDealerFieldApiName(po.getDealerFieldApiName());
        vo.setAccountFieldApiName(po.getAccountFieldApiName());
        vo.setCostFieldApiName(po.getCostFieldApiName());
        vo.setDisplayFieldApiNames(po.getDisplayFieldApiNames());
        vo.setActivityDisplayFieldApiNames(po.getActivityDisplayFieldApiNames());
        vo.setAgreementDisplayFieldApiNames(po.getAgreementDisplayFieldApiNames());
        vo.setStoreCostDisplayFieldApiNames(po.getStoreCostDisplayFieldApiNames());
        vo.setCashingProductDisplayFieldApiNames(po.getCashingProductDisplayFieldApiNames());
        return vo;
    }
}
