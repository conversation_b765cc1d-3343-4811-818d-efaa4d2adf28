package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofDisplayImgFields;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import groovy.lang.Tuple2;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public interface TPMProofPeriodTime {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "activity_id")
        @JsonProperty(value = "activity_id")
        @SerializedName("activity_id")
        private String activityId;

        @JSONField(name = "activity_agreement_id")
        @JsonProperty(value = "activity_agreement_id")
        @SerializedName("activity_agreement_id")
        private String activityAgreementId;

        @JSONField(name = "begin_date")
        @JsonProperty(value = "begin_date")
        @SerializedName("begin_date")
        private long beginDate;

        @JSONField(name = "end_date")
        @JsonProperty(value = "end_date")
        @SerializedName("end_date")
        private long endDate;

        @JSONField(name = "exclude_proof_ids")
        @JsonProperty(value = "exclude_proof_ids")
        @SerializedName("exclude_proof_ids")
        private List<String> excludeProofIds;


        private Integer page;

        private Integer size;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {
        private Integer total;

        @JSONField(name = "proof_periods")
        @JsonProperty(value = "proof_periods")
        @SerializedName("proof_periods")
        private List<ProofPeriod> proofPeriods;

        private Integer size;
    }

    @Data
    @ToString
    @Builder
    class ProofResult implements Serializable {
        private Integer total;

        @JSONField(name = "proof_list")
        @JsonProperty(value = "proof_list")
        @SerializedName("proof_list")
        private List<TPMActivityProof> proofList;
    }

    @Data
    @ToString
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ProofPeriod {

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;

        @JSONField(name = "stage")
        @JsonProperty(value = "stage")
        @SerializedName("stage")
        private int stage;

        @JSONField(name = "begin_date")
        @JsonProperty(value = "begin_date")
        @SerializedName("begin_date")
        private long beginDate;

        @JSONField(name = "end_date")
        @JsonProperty(value = "end_date")
        @SerializedName("end_date")
        private long endDate;

        @JSONField(name = "achievement_status")
        @JsonProperty(value = "achievement_status")
        @SerializedName("achievement_status")
        private String achievementStatus;

        @JSONField(name = "achievement_status_vo")
        @JsonProperty(value = "achievement_status_vo")
        @SerializedName("achievement_status_vo")
        private StatusVO achievementStatusVO;

        @JSONField(name = "current_period")
        @JsonProperty(value = "current_period")
        @SerializedName("current_period")
        private boolean currentPeriod = false;

        @JSONField(name = "proof_list")
        @JsonProperty(value = "proof_list")
        @SerializedName("proof_list")
        private List<TPMActivityProof> proofList;
    }

    @Data
    @ToString
    @Builder
    class TPMActivityProof {

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;

        @JSONField(name = "checkin_id")
        @JsonProperty(value = "checkin_id")
        @SerializedName("checkin_id")
        private String checkinId;

        @JSONField(name = "status")
        @JsonProperty(value = "status")
        @SerializedName("status")
        private StatusVO statusVO;

        @JSONField(name = "is_display_activity")
        @JsonProperty(value = "is_display_activity")
        @SerializedName("is_display_activity")
        private Boolean isDisplayActivity;

        @JSONField(name = "is_show_report_link")
        @JsonProperty(value = "is_show_report_link")
        @SerializedName("is_show_report_link")
        private Boolean isShowReportLink;

        @JSONField(name = "display_status")
        @JsonProperty(value = "display_status")
        @SerializedName("display_status")
        private StatusVO displayStatusVO;

        @JSONField(name = "group_ids")
        @JsonProperty(value = "group_ids")
        @SerializedName("group_ids")
        private List<String> groupIds;

        @JSONField(name = "create_time")
        @JsonProperty(value = "create_time")
        @SerializedName("create_time")
        private long createTime;

        @JSONField(name = "proof_item_list")
        @JsonProperty(value = "proof_item_list")
        @SerializedName("proof_item_list")
        private List<TPMActivityProofDisplayImg> proofItemList;

    }

    @Data
    @ToString
    @Builder
    class TPMActivityProofDisplayImg {

        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;

        @JSONField(name = "image")
        @JsonProperty(value = "image")
        @SerializedName("image")
        private List<ImageVO> image;

        @JSONField(name = "display_form_id")
        @JsonProperty(value = "display_form_id")
        @SerializedName("display_form_id")
        private String displayFormId;

        @JSONField(name = "display_form_name")
        @JsonProperty(value = "display_form_name")
        @SerializedName("display_form_name")
        private String displayFormName;

        @JSONField(name = "audit_status")
        @JsonProperty(value = "audit_status")
        @SerializedName("audit_status")
        private StatusVO auditStatus;

        @JSONField(name = "activity_proof_detail")
        @JsonProperty(value = "activity_proof_detail")
        @SerializedName("activity_proof_detail")
        private List<TPMActivityProofDetailData> activityProofDetail;

    }

    @Data
    @ToString
    @Builder
    class TPMActivityProofDetailData {
        private String label;

        @JSONField(name = "value")
        @JsonProperty(value = "value")
        @SerializedName("value")
        private String value;

        @JSONField(name = "standard_value")
        @JsonProperty(value = "standard_value")
        @SerializedName("standard_value")
        private String standardValue;

        private String id;

        //小数为0只保留整数部分
        public static String stripTrailingZeros(BigDecimal bd) {
            BigDecimal stripped = bd.stripTrailingZeros();
            if (stripped.scale() <= 0) {
                return stripped.toBigInteger().toString();
            } else {
                return stripped.toString();
            }
        }
    }


    @Data
    @ToString
    @Builder
    class ImageVO {


        private String path;

        @JSONField(name = "file_name")
        @JsonProperty(value = "file_name")
        @SerializedName("file_name")
        private String fileName;

        @JSONField(name = "ext")
        @JsonProperty(value = "ext")
        @SerializedName("ext")
        private String ext;

        public static List<TPMProofPeriodTime.ImageVO> extractImageVO(IObjectData imageData) {
            if (imageData == null || imageData.get(TPMActivityProofDisplayImgFields.IMAGE) == null) {
                return Collections.emptyList();
            }

            Object imgObj = imageData.get(TPMActivityProofDisplayImgFields.IMAGE);
            if (!(imgObj instanceof List<?>)) {
                return Collections.emptyList();
            }

            return ((List<?>) imgObj).stream()
                    .filter(Map.class::isInstance)
                    .map(item -> {
                        Map<?, ?> imgMap = (Map<?, ?>) item;
                        return TPMProofPeriodTime.ImageVO.builder()
                                .path(Objects.toString(imgMap.get("path"), ""))
                                .ext(Objects.toString(imgMap.get("ext"), ""))
                                .fileName(Objects.toString(imgMap.get("filename"), ""))
                                .build();
                    })
                    .filter(vo -> StringUtils.isNotBlank(vo.getPath()))
                    .collect(Collectors.toList());
        }
    }

    @Data
    @ToString
    class StatusVO {

        @JSONField(name = "font_color")
        @JsonProperty(value = "font_color")
        @SerializedName("font_color")
        private String fontColor;

        @JSONField(name = "label")
        @JsonProperty(value = "label")
        @SerializedName("label")
        private String label;

        @JSONField(name = "value")
        @JsonProperty(value = "value")
        @SerializedName("value")
        private String value;

        public static TPMProofPeriodTime.StatusVO of(IObjectDescribe describe, String fieldName, IObjectData data) {
            if (Objects.isNull(describe)) {
                return new TPMProofPeriodTime.StatusVO();
            }
            TPMProofPeriodTime.StatusVO statusVO = new TPMProofPeriodTime.StatusVO();
            String value = data.get(fieldName, String.class);
            if (StringUtils.isNotBlank(value)) {
                Tuple2<String, String> labelAndColor = getLabelAndColor(describe, fieldName, value);
                statusVO.setLabel(labelAndColor.getFirst());
                statusVO.setValue(value);
                statusVO.setFontColor(labelAndColor.getSecond());
            }
            return statusVO;
        }

        private static Tuple2<String, String> getLabelAndColor(IObjectDescribe describe, String fieldName, String value) {
            String label = "";
            String color = "";
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) describe.getFieldDescribe(fieldName);
            if (selectOneFieldDescribe != null) {
                for (ISelectOption selectOption : selectOneFieldDescribe.getSelectOptions()) {
                    if (Objects.equals(selectOption.getValue(), value)) {
                        label = selectOption.getLabel();
                        color = selectOption.get("font_color", String.class);
                        break;
                    }
                }
            }
            return new Tuple2<>(label, color);
        }

    }
}
