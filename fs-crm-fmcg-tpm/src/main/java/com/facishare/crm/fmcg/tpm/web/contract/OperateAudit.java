package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/28 12:26
 */
public interface OperateAudit {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "agreement_id")
        @JsonProperty(value = "agreement_id")
        @SerializedName("agreement_id")
        private String agreementId;

        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        private String apiName;

        @JSONField(name = "out_tenant_id")
        @JsonProperty(value = "out_tenant_id")
        @SerializedName("out_tenant_id")
        private String outTenantId;

        @JSONField(name = "dealer_id")
        @JsonProperty(value = "dealer_id")
        @SerializedName("dealer_id")
        private String dealerId;

        @JSONField(name = "audit_status")
        @JsonProperty(value = "audit_status")
        @SerializedName("audit_status")
        private String auditStatus;

        @JSONField(name = "audit_opinion")
        @JsonProperty(value = "audit_opinion")
        @SerializedName("audit_opinion")
        private String auditOpinion;

        @JSONField(name = "audit_advice")
        @JsonProperty(value = "audit_advice")
        @SerializedName("audit_advice")
        private String auditAdvice;

        @JSONField(name = "update_agreement_items")
        @JsonProperty(value = "update_agreement_items")
        @SerializedName("update_agreement_items")
        private List<AgreementItemData> updateAgreementItems;
    }

    @Data
    @ToString
    class AgreementItemData implements Serializable {
        private String id;

        @JSONField(name = "field_name")
        @JsonProperty(value = "field_name")
        @SerializedName("field_name")
        private String fieldName;

        private BigDecimal amount;

        @JSONField(name = "modify_amount")
        @JsonProperty(value = "modify_amount")
        @SerializedName("modify_amount")
        private BigDecimal modifyAmount;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {


    }

}
