package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 18:08
 */
@Data
@ToString
public class ActivityProofConfigEntity implements Serializable {

    @Embedded("frequency_config")
    private ActivityProofFrequencyConfigEntity frequencyConfig;

    @Embedded("cost_calculate_config")
    private ActivityProofCostCalculateConfigEntity costCalculateConfig;

    @Embedded("ai_config")
    private ActivityProofAiConfigEntity aiConfig;

    public static ActivityProofConfigEntity fromVO(ActivityProofConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityProofConfigEntity po = new ActivityProofConfigEntity();
        po.setFrequencyConfig(ActivityProofFrequencyConfigEntity.fromVO(vo.getFrequencyConfig()));
        po.setCostCalculateConfig(ActivityProofCostCalculateConfigEntity.fromVO(vo.getCostCalculateConfig()));
        po.setAiConfig(ActivityProofAiConfigEntity.fromVO(vo.getAiConfig()));
        return po;
    }
}
