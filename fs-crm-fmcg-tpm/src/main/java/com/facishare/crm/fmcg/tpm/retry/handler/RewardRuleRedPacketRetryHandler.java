package com.facishare.crm.fmcg.tpm.retry.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.RedPacketService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskStatusEnum;
import com.facishare.crm.fmcg.tpm.retry.annotation.RetryHandlerType;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRuleRedPacketSetter;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/8/10 11:29
 */
@Component
@Slf4j
@RetryHandlerType(name = RetryHandlerEnum.REWARD_RULE_RED_PACKET_UPDATE_HANDLER)
public class RewardRuleRedPacketRetryHandler extends BaseHandler implements RetryHandler {

    @Resource
    private RedPacketService redPacketService;
    @Resource
    private RewardRuleRedPacketSetter rewardRuleRedPacketSetter;

    @Override
    public void mainDo(RetryTaskPO retryTaskPO) {
        if (Strings.isNullOrEmpty(retryTaskPO.getParams())) {
            rewardRuleRedPacketSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
            return;
        }
        JSONObject params = JSON.parseObject(retryTaskPO.getParams());
        String rewardId = params.getString("redPacketId");
        String tenantId = params.getString("tenantId");

        IObjectData rewardRecord = serviceFacade.findObjectData(User.systemUser(tenantId), rewardId, ApiNames.RED_PACKET_RECORD_OBJ);
        if (redPacketService.refreshRewardStatus(rewardRecord)) {
            rewardRuleRedPacketSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
        } else {
            rewardRuleRedPacketSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.SUCCESS.code());
        }
    }

    @Override
    public void handleException(RetryTaskPO retryTaskPO, Exception e) {
        super.handleException(retryTaskPO, e);
        if (retryTaskPO == null) {
            log.info("task is null");
            return;
        }
        rewardRuleRedPacketSetter.setNewStatus(retryTaskPO.getId().toString(), RetryTaskStatusEnum.FAIL.code());
    }
}