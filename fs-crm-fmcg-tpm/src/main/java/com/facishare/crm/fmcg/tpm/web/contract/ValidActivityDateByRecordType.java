package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 17:36
 */
public interface ValidActivityDateByRecordType {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;

        @JSONField(name = "slave_api_name")
        @JsonProperty(value = "slave_api_name")
        @SerializedName("slave_api_name")
        private String slaveApiName;

        @JSONField(name = "record_type")
        @JsonProperty(value = "record_type")
        @SerializedName("record_type")
        private String recordType;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "is_exists_date")
        @JsonProperty(value = "is_exists_date")
        @SerializedName("is_exists_date")
        private Boolean isExistsDate;

        @JSONField(name = "is_exists_slave")
        @JsonProperty(value = "is_exists_slave")
        @SerializedName("is_exists_slave")
        private Boolean isExistsSlave;
    }
}
