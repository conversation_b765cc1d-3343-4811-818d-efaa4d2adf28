package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface StoreWriteOffBatchData {

    @Data
    @ToString
    class Arg implements Serializable {

        // 更新的数据
        @SerializedName("data")
        @JSONField(name = "data")
        @JsonProperty("data")
        private JSONObject data;

        // 批量核销 idList
        // why not [id_list]
        private List<String> idList;

        //操作人
        // why not [write_off_owner]
        private List<String> writeOffOwner;

        //当前核销所选id
        @JSONField(name = "id")
        @JsonProperty(value = "id")
        @SerializedName("id")
        private String id;
    }

    @Data
    @ToString
    class Result implements Serializable {
        private Map<String, Object> objectData;
    }

}
