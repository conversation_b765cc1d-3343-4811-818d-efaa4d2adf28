package com.facishare.crm.fmcg.tpm.web.contract.kk;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/11/14 11:38
 */
public interface Freeze {

    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 业务对象的 api name
         */
        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        @SerializedName("api_name")
        private String apiName;

        /**
         * 业务对象的 id
         */
        @JSONField(name = "data_id")
        @JsonProperty(value = "data_id")
        @SerializedName("data_id")
        private String dataId;

        /**
         * 冻结信息
         */
        @JSONField(name = "freeze_data")
        @JsonProperty(value = "freeze_data")
        @SerializedName("freeze_data")
        private List<FreezeItemVO> freezeData;
    }

    @Data
    @ToString
    class FreezeItemVO implements Serializable {

        /**
         * 预算表 id
         */
        @JSONField(name = "account_id")
        @JsonProperty(value = "account_id")
        @SerializedName("account_id")
        private String accountId;

        /**
         * 金额
         */
        @JSONField(name = "amount")
        @JsonProperty(value = "amount")
        @SerializedName("amount")
        private BigDecimal amount;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "business_trace_id")
        @JsonProperty(value = "business_trace_id")
        @SerializedName("business_trace_id")
        private String businessTraceId;

        @JSONField(name = "approval_trace_id")
        @JsonProperty(value = "approval_trace_id")
        @SerializedName("approval_trace_id")
        private String approvalTraceId;
    }
}