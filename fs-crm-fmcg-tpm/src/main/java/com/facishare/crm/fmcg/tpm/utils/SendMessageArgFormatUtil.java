package com.facishare.crm.fmcg.tpm.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.model.User;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.model.*;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.google.common.collect.Lists;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
//IgnoreI18nFile
public class SendMessageArgFormatUtil {

    //todo 改为预算的APPID
    private static final String BUDGET_APP_ID = "BUDGET";

    public static List<KeyValueItem> buildFormMessage(Map<String, String> formMap) {
        List<KeyValueItem> keyValueItems = Lists.newArrayList();
        for (Map.Entry<String, String> entry : formMap.entrySet()) {
            keyValueItems.add(new KeyValueItem(new TextCardElement(entry.getKey(), "", ""), new TextCardElement(entry.getValue(), "", "")));
        }
        return keyValueItems;
    }

    public static TextCardMessageHead buildMessageHead(String headText) {
        TextCardMessageHead head = new TextCardMessageHead();
        head.setTitleElement(new TextCardElement(headText, "", ""));
        return head;
    }

    public static TextCardMessageBody buildMessageBody(String contentHeadMessage, Map<String, String> formMap) {
        TextCardMessageBody body = new TextCardMessageBody();
        body.setContentElement(new TextCardElement(contentHeadMessage, "", ""));
        body.setForm(buildFormMessage(formMap));
        return body;
    }

    public static SendTextCardMessageArg buildTextCardArgForAppNotify(User user, String apiName, String dataId, TextCardMessageHead head, TextCardMessageBody body) {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(Integer.parseInt(user.getTenantId()));
        arg.setUuid(UUID.randomUUID().toString());
        //应用通知
        arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        arg.setObjectId(dataId);
        arg.setObjectApiName(apiName);
        arg.setGenerateUrlType(1);
        //APPID
        arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData(BUDGET_APP_ID));
        arg.setReceiverIds(Lists.newArrayList(user.getUserIdInt()));

        TextCardMessage textCardMessage = new TextCardMessage();

        textCardMessage.setHead(head);

        textCardMessage.setBody(body);
        TextCardMessageFoot foot = new TextCardMessageFoot();
        foot.setFootElement(new TextCardElement("点击查看详情", "", ""));
        textCardMessage.setFoot(foot);
        String url = linkUrl(apiName, dataId);
        textCardMessage.setInnerPlatformMobileUrl(url);
        textCardMessage.setInnerPlatformWebUrl(url);
        textCardMessage.setOutPlatformUrl("");
        arg.setTextCardMessage(textCardMessage);
        return arg;
    }

    private static String linkUrl(String apiName, String objectId) {
        String url = "fs://CRM/udobj?%s";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("objDescApiName", apiName);
        jsonObject.put("objDataId", objectId);
        return String.format(url, JSON.toJSONString(jsonObject));
    }

    public static String formatDateStr(Long timestamp) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return format.format(new Date(timestamp));

    }

}
