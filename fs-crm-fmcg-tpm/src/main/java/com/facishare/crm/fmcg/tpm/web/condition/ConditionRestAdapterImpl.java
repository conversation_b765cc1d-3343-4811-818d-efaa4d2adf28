package com.facishare.crm.fmcg.tpm.web.condition;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.condition.model.ConditionDto;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.fmcg.framework.http.PaasRuleProxy;
import com.fmcg.framework.http.contract.paas.data.*;
import com.fmcg.framework.http.contract.paas.rule.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import groovy.lang.Tuple2;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
@Component
public class ConditionRestAdapterImpl implements ConditionAdapter {

    @Resource
    private PaasRuleProxy paasRuleProxy;

    private static final Logger logger = LoggerFactory.getLogger(ConditionRestAdapterImpl.class);

    private static final String APP_ID = "TPM";
    private static final String SCENE = "TPM";

    private static final String EMPLOYEE_ID = "-10000";

    @Override
    public Tuple2<String, List<ConditionDto>> find(Integer tenantId, Integer userId, String apiName, String code) {
        PaasRuleFindWithRules.Arg arg = new PaasRuleFindWithRules.Arg();

        PaasRuleContext context = new PaasRuleContext();
        context.setTenantId(tenantId.toString());
        context.setAppId(APP_ID);
        context.setUserId(EMPLOYEE_ID);
        context.setScene(SCENE);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(100);
        pageInfo.setCurrentPage(1);

        arg.setContext(context);
        arg.setRuleCodes(Sets.newHashSet(code));
        arg.setPageInfo(pageInfo);

        logger.info("[rest condition] find_rule_group_with_rules arg : {}", arg);
        PaasRuleFindWithRules.Result withRulesResult = paasRuleProxy.findWithRules(tenantId, userId, arg);

        if (withRulesResult == null) {
            return null;
        }

        if (withRulesResult.getErrCode() != 0) {
            logger.error("[rest condition] create error : {}", withRulesResult.getErrMessage());
            return null;
        }

        RuleGroupPageContent ruleGroupList = withRulesResult.getResult();

        PaasRuleGroupPojo paasRuleGroupPojo = ruleGroupList.getContent().get(0);
        List<ConditionDto> conditions = new ArrayList<>();
        for (PaasRulePojo rule : paasRuleGroupPojo.getRules()) {
            ConditionDto conditionDto = new ConditionDto();
            conditionDto.setRowNo(rule.getRuleOrder());
            conditionDto.setFieldName(rule.getFieldName());
            conditionDto.setFieldType(rule.getFieldType());
            conditionDto.setQuoteFieldType(rule.getQuoteFieldType());
            conditionDto.setOperator(rule.getOperate());
            conditionDto.setValues(rule.getFieldValue());
            conditions.add(conditionDto);
        }
        return new Tuple2<>(paasRuleGroupPojo.getRuleParse(), conditions);
    }

    @Override
    public boolean isDataMatchRuleCodes(Integer tenantId, Integer userId, String apiName, String ruleCode, Map<String, Object> dataMap) {
        if (Strings.isNullOrEmpty(ruleCode)) {
            return true;
        }
        logger.info("start isDataMatchRuleCodes");
        PaasRuleFindDataMatchRuleCodes.Arg arg = new PaasRuleFindDataMatchRuleCodes.Arg();
        PaasRuleContext paasRuleContext = new PaasRuleContext();
        paasRuleContext.setTenantId(String.valueOf(tenantId));
        paasRuleContext.setUserId(String.valueOf(userId));
        paasRuleContext.setScene(SCENE);
        paasRuleContext.setAppId(APP_ID);
        arg.setContext(paasRuleContext);
        arg.setEntityId(apiName);
        arg.setRuleCodes(Sets.newHashSet(ruleCode));
        Map<String, Map<String, Object>> dataMaps = new HashMap<>();
        String validateId = "validateId996";
        dataMaps.put(validateId, dataMap);
        arg.setDataMaps(dataMaps);
        PaasRuleFindDataMatchRuleCodes.Result result = paasRuleProxy.findDataMatchRuleCodes(tenantId, userId, arg);
        if (result.getErrCode() == null || result.getErrCode() != 0) {
            logger.info("validate data fail.msg:{}", result);
            throw new ValidateException(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_0));
        }
        boolean b = !CollectionUtils.isEmpty(result.getResult().get(validateId));
        logger.info("end isDataMatchRuleCodes , isDataMatchRuleCodes result:{}", b);
        return b;
    }

    @Override
    public Map<String, Boolean> isDataMatchRuleCodes(Integer tenantId, Integer userId, String apiName, String dataId, Set<String> ruleCodes) {
        PaasDataRuleExpPattern.Arg arg = new PaasDataRuleExpPattern.Arg();
        PaasRuleContext paasRuleContext = new PaasRuleContext();
        paasRuleContext.setTenantId(String.valueOf(tenantId));
        paasRuleContext.setUserId(String.valueOf(userId));
        paasRuleContext.setScene(SCENE);
        paasRuleContext.setAppId(APP_ID);
        arg.setContext(paasRuleContext);
        arg.setEntityId(apiName);
        arg.setRuleCodes(ruleCodes);
        arg.setDataIds(Sets.newHashSet(dataId));
        PaasDataRuleExpPattern.Result result = paasRuleProxy.dataRuleExpPattern(tenantId, userId, arg);
        if (result.getErrCode() != 0) {
            throw new ValidateException(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_1));
        }
        try {
            logger.info("end isDataMatchRuleCodes , isDataMatchRuleCodes result:{}", JSON.toJSONString(result));
        } catch (Exception ignore) {
        }
        return result.getResult().get(dataId);
    }

    @Override
    public String publish(Integer tenantId, Integer userId, String apiName, String conditionPattern, List<ConditionDto> conditions) {
        PaasRuleCreateRule.Arg arg = new PaasRuleCreateRule.Arg();

        PaasRuleContext context = new PaasRuleContext();
        context.setTenantId(tenantId.toString());
        context.setAppId(APP_ID);
        context.setUserId(EMPLOYEE_ID);
        context.setScene(SCENE);
        PaasRuleGroupPojo ruleGroup = new PaasRuleGroupPojo();

        ruleGroup.setTenantId(tenantId.toString());
        ruleGroup.setAppId(APP_ID);
        ruleGroup.setEntityId(apiName);
        String ruleName = apiName + UUID.randomUUID().toString();
        ruleGroup.setRuleName(ruleName.substring(0, Math.min(ruleName.length(), 64)));
        ruleGroup.setRuleParse(conditionPattern);
        ruleGroup.setStatus(1);
        ruleGroup.setRules(Lists.newArrayList());

        conditions.forEach(condition -> {
            PaasRulePojo rulePojo = new PaasRulePojo();
            rulePojo.setRuleOrder(condition.getRowNo());
            rulePojo.setFieldName(condition.getFieldName());
            rulePojo.setFieldType(condition.getFieldType());
            rulePojo.setQuoteFieldType(condition.getQuoteFieldType());
            rulePojo.setOperate(condition.getOperator());
            rulePojo.setFieldValue(condition.getValues());
            ruleGroup.getRules().add(rulePojo);
        });

        ruleGroup.setActions(Lists.newArrayList());
        ruleGroup.setCreatedBy(EMPLOYEE_ID);
        ruleGroup.setCreateTime(System.currentTimeMillis());
        ruleGroup.setIsDeleted(0);

        arg.setContext(context);
        arg.setRuleGroup(ruleGroup);
        logger.info("[rest condition] create arg : {}", arg);

        PaasRuleCreateRule.Result ruleResult = paasRuleProxy.createRule(tenantId, userId, arg);
        if (ruleResult == null) {
            throw new ValidateException(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_2));
        }

        if (ruleResult.getErrCode() != 0) {
            logger.error("[rest condition] create error : {}", ruleResult.getErrMessage());
            throw new ValidateException(String.format(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_3), ruleResult.getErrMessage()));
        }
        logger.info("[rest condition] create result : {}", ruleResult.getResult());

        return ruleResult.getResult();
    }

    @Override
    public String update(Integer tenantId, Integer userId, String apiName, String ruleCode, String conditionPattern, List<ConditionDto> conditions) {
        PaasRuleUpdateRule.Arg arg = new PaasRuleUpdateRule.Arg();

        PaasRuleContext context = new PaasRuleContext();
        context.setTenantId(tenantId.toString());
        context.setAppId(APP_ID);
        context.setUserId(EMPLOYEE_ID);
        context.setScene(SCENE);
        PaasRuleGroupPojo ruleGroup = new PaasRuleGroupPojo();

        ruleGroup.setRuleCode(ruleCode);
        ruleGroup.setTenantId(tenantId.toString());
        ruleGroup.setAppId(APP_ID);
        ruleGroup.setEntityId(apiName);
        String ruleName = apiName + UUID.randomUUID().toString();
        ruleGroup.setRuleName(ruleName.substring(0, Math.min(ruleName.length(), 64)));
        ruleGroup.setRuleParse(conditionPattern);
        ruleGroup.setStatus(1);
        ruleGroup.setRules(Lists.newArrayList());

        conditions.forEach(condition -> {
            PaasRulePojo rulePojo = new PaasRulePojo();
            rulePojo.setRuleOrder(condition.getRowNo());
            rulePojo.setFieldName(condition.getFieldName());
            rulePojo.setFieldType(condition.getFieldType());
            rulePojo.setQuoteFieldType(condition.getQuoteFieldType());
            rulePojo.setOperate(condition.getOperator());
            rulePojo.setFieldValue(condition.getValues());
            ruleGroup.getRules().add(rulePojo);
        });

        ruleGroup.setActions(Lists.newArrayList());
        ruleGroup.setCreatedBy(EMPLOYEE_ID);
        ruleGroup.setCreateTime(System.currentTimeMillis());
        ruleGroup.setIsDeleted(0);

        arg.setContext(context);
        arg.setRuleGroup(ruleGroup);
        logger.info("[rest condition] create arg : {}", JSON.toJSONString(arg));

        PaasRuleUpdateRule.Result ruleResult = paasRuleProxy.updateRule(tenantId, userId, arg);
        if (ruleResult == null) {
            throw new ValidateException(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_4));
        }

        if (ruleResult.getErrCode() != 0) {
            logger.error("[rest condition] create error : {}", ruleResult.getErrMessage());
            throw new ValidateException(String.format(I18N.text(I18NKeys.CONDITION_REST_ADAPTER_IMPL_5), ruleResult.getErrMessage()));
        }
        logger.info("[rest condition] create result : {}", ruleResult.getResult());

        return ruleResult.getResult();
    }


}
