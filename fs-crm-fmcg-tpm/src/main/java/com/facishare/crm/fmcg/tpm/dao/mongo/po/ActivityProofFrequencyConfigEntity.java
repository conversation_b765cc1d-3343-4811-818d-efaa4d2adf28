package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofFrequencyConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 18:09
 */
@Data
@ToString
public class ActivityProofFrequencyConfigEntity implements Serializable {

    @Property("frequency_type")
    private String frequencyType;

    @Property("frequency_limit")
    private String frequencyLimit;

    @Embedded("limit_days")
    private List<LimitSpanEntity> limitDays;

    @Embedded("limit_hours")
    private List<LimitSpanEntity> limitHours;

    public static ActivityProofFrequencyConfigEntity fromVO(ActivityProofFrequencyConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityProofFrequencyConfigEntity po = new ActivityProofFrequencyConfigEntity();
        po.setFrequencyType(vo.getFrequencyType());
        po.setFrequencyLimit(vo.getFrequencyLimit());
        po.setLimitDays(
                vo.getLimitDays().stream().map(LimitSpanEntity::fromVO).collect(Collectors.toList())
        );
        po.setLimitHours(
                vo.getLimitHours().stream().map(LimitSpanEntity::fromVO).collect(Collectors.toList())
        );
        return po;
    }
}
