<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.facishare.crm.fmcg.tpm.dao.pg.ActivityAuditMapper">

    <select id="queryActivityTypeStatisticsData"
            resultType="com.facishare.crm.fmcg.tpm.dao.pg.po.ActivityTypeStatisticsDatumPO">
        select
            table_01.audit_status as status,
            count(*) as count
        from
            fmcg_tpm_activity_proof_audit table_01
        where
            table_01.tenant_id = #{tenant_id}
            and table_01.life_status = 'normal'
            and table_01.is_deleted = 0
            and table_01.activity_id in (
                select
                    table_02.id
                from
                    fmcg_tpm_activity table_02
                where
                    table_02.tenant_id = #{tenant_id}
                    and table_02.is_deleted = 0
                    and table_02.life_status = 'normal'
                    and table_02.activity_type = #{activity_type_id}
                    and table_02.activity_status = 'in_progress'
            )
        group by table_01.audit_status;
    </select>

    <select id="countByActivity"
            resultType="java.lang.Long">
        select count(id)
        from fmcg_tpm_activity_proof_audit
        where tenant_id = #{tenant_id}
          and is_deleted = 0
          and life_status = 'normal'
          and activity_id = #{activity_id}
    </select>

</mapper>