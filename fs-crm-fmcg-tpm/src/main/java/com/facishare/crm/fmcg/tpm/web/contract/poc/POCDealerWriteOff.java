package com.facishare.crm.fmcg.tpm.web.contract.poc;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface POCDealerWriteOff {
    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Arg extends POCBase.Arg implements Serializable {
        private String tenantId;
        private IObjectData activity;
        private List<IObjectData> activityDetails;
        /**
         * 处理月份的开始时间
         */
        private Long begin;
        private Long end;



    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString
    class Result extends POCBase.Result implements Serializable {


    }
}
