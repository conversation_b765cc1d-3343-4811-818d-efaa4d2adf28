package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.IScopePO;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IUseScopeManager;
import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.departmentmember.MainDepartment;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.BatchGetEmployeesDtoByDepartmentIdArg;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 15:17
 */
//IgnoreI18nFile
@Component
public class UseScopeManager implements IUseScopeManager {

    @Resource
    private EmployeeProviderService employeeProviderService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    @Override
    public String calculateScopeDescription(String tenantId, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roleIds) {
        StringBuilder scopeBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            BatchGetEmployeeDtoArg arg = new BatchGetEmployeeDtoArg();
            arg.setEmployeeIds(employeeIds);
            arg.setRunStatus(RunStatus.ALL);
            arg.setEnterpriseId(Integer.parseInt(tenantId));
            List<EmployeeDto> employees = employeeProviderService.batchGetEmployeeDto(arg).getEmployeeDtos();

            if (CollectionUtils.isNotEmpty(employees)) {
                scopeBuilder.append("员工：").append(employees.stream().map(EmployeeDto::getName).collect(Collectors.joining(","))).append(";");
            }
        }
        if (CollectionUtils.isNotEmpty(departmentIds)) {

            BatchGetDepartmentDtoArg batchGetDepartmentDtoArg = new BatchGetDepartmentDtoArg();
            batchGetDepartmentDtoArg.setDepartmentIds(departmentIds);
            batchGetDepartmentDtoArg.setEnterpriseId(Integer.parseInt(tenantId));
            List<DepartmentDto> departments = departmentProviderService.batchGetDepartmentDto(batchGetDepartmentDtoArg).getDepartments();

            if (CollectionUtils.isNotEmpty(departments)) {
                scopeBuilder.append("部门：").append(departments.stream().map(DepartmentDto::getName).collect(Collectors.joining(","))).append(";");
            }
        }
        return scopeBuilder.toString();
    }

    @Override
    public List<Integer> calculateScope(String tenantId, List<Integer> employeeIds, List<Integer> departmentIds, List<String> roleIds) {
        List<Integer> allEmployeeIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            allEmployeeIds.addAll(employeeIds);
        }
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            BatchGetEmployeesDtoByDepartmentIdArg batchGetEmployeesDtoByDepartmentId = new BatchGetEmployeesDtoByDepartmentIdArg();
            batchGetEmployeesDtoByDepartmentId.setDepartmentIds(departmentIds);
            batchGetEmployeesDtoByDepartmentId.setIncludeLowDepartment(true);
            batchGetEmployeesDtoByDepartmentId.setRunStatus(RunStatus.ACTIVE);
            batchGetEmployeesDtoByDepartmentId.setMainDepartment(MainDepartment.MAIN);
            batchGetEmployeesDtoByDepartmentId.setEnterpriseId(Integer.parseInt(tenantId));

            List<Integer> departmentEmployeeIds = employeeProviderService.batchGetEmployeesByDepartmentId(batchGetEmployeesDtoByDepartmentId)
                    .getEmployeeDtos()
                    .stream()
                    .map(EmployeeDto::getEmployeeId)
                    .collect(Collectors.toList());
            allEmployeeIds.addAll(departmentEmployeeIds);
        }
        return allEmployeeIds;
    }

    public <A extends IScopePO> void fillScopeInformation(String tenantId, A po) {
        po.setAllEmployeeIds(calculateScope(tenantId, po.getEmployeeIds(), po.getDepartmentIds(), po.getRoleIds()));
        po.setScopeDescription(calculateScopeDescription(tenantId, po.getEmployeeIds(), po.getDepartmentIds(), po.getRoleIds()));
    }
}
