package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 *@author: wuyx
 *@description:
 *@createTime: 2022/1/5 19:52
 */
public interface ActivityTask {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "tenant_id")
        @JsonProperty(value = "tenant_id")
        @SerializedName("tenant_id")
        private String tenantId;
    }
}