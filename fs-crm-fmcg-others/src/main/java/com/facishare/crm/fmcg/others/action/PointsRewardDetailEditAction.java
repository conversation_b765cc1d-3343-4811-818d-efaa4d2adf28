package com.facishare.crm.fmcg.others.action;

import com.facishare.crm.fmcg.others.i18n.I18NKeys;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.fxiaoke.common.release.GrayRelease;
import de.lab4inf.math.util.Strings;


public class PointsRewardDetailEditAction extends StandardEditAction {

    @Override
    protected void before(Arg arg) {
        if (!GrayRelease.isAllow("fmcg", "ALLOW_EDIT_POINTS_REWARD_DETAIL_OBJ", actionContext.getTenantId())) {
            arg.getObjectData().keySet().forEach(key -> {
                if (!Strings.isNullOrEmpty(key) && !key.endsWith("__c") && !key.equals("_id"))
                    throw new ValidateException(I18N.text(I18NKeys.POINTS_REWARD_DETAIL_EDIT_ACTION_0));
            });
        }
        super.before(arg);

    }
}
