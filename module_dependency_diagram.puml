@startuml 模块依赖关系图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle

title CRM FMCG TPM 模块依赖关系图

package "fs-crm-fmcg-service" as ServiceModule {
    package "web.facade" {
        [RewardController] : 奖励控制器\n处理奖励规则的CRUD\n扫码奖励功能
        [POCActivityController] : POC活动控制器\n处理POC活动的初始化\n和举证管理
        [WithdrawalController] : 提现控制器\n处理红包提现业务\n提现记录查询
        [MengNiuRewardController] : 蒙牛奖励控制器\n专门处理蒙牛客户\n的消费者扫码奖励
    }

    package "web.inner" {
        [InnerDMSController] : 内部DMS控制器\n处理分销管理系统\n的内部接口调用
        [InnerRewardController] : 内部奖励控制器\n处理系统内部的\n奖励相关操作
        [InnerYLTPMController] : 内部伊利TPM控制器\n处理伊利客户专用的\nTPM功能
    }

    package "web.task" {
        [POCTaskController] : POC任务控制器\n处理POC相关的\n定时任务和批处理
    }

    [CRMInitService] : CRM初始化服务\n系统启动时的初始化\n预定义对象和配置
}

package "fs-crm-fmcg-tpm" as TPMModule {
    package "web.service" {
        [BudgetNewConsumeRuleService] : 预算消费规则服务\n管理预算的消费规则\n处理预算分配和消费
        [BudgetClosureService] : 预算结算服务\n处理预算的结算和关闭\n确保预算数据一致性
        [LicenseService] : 许可证服务\n管理系统功能模块的\n许可证和权限控制
        [PhysicalRewardService] : 实物奖励服务\n处理实物奖品的发放\n核销和物流信息管理
        [ScanCodeService] : 扫码服务\n处理二维码扫描业务\n包括扫码奖励和验证
        [UnlockOuterCodeService] : 外部码解锁服务\n处理外部二维码的\n解锁和验证功能
        [WechatService] : 微信服务\n处理微信相关功能\n包括小程序码生成等
    }

    package "web.manager" {
        [ActivityTypeManager] : 活动类型管理器\n管理活动类型的定义\n和活动模板配置
        [BudgetAccrualRuleManager] : 预算计提规则管理器\n管理预算的计提规则\n处理预算的自动计提
        [ProofPeriodManager] : 举证期间管理器\n管理活动举证的时间期间\n处理举证数据的汇总
    }

    package "business" {
        [ActivityService] : 活动业务服务\n处理活动的核心业务逻辑\n包括活动创建、审批等
        [AsyncBudgetDisassemblyService] : 异步预算拆解服务\n异步处理预算的拆解\n包括预算冻结和解冻
        [BudgetService] : 预算业务服务\n处理预算的核心业务逻辑\n包括预算计算和分配
        [StoreBusiness] : 门店业务服务\n处理门店相关的业务逻辑\n包括门店信息管理
        [TPMDisplayReportService] : TPM陈列报告服务\n处理陈列数据的业务逻辑\n包括陈列检测和AI分析
        [TPMTriggerActionService] : TPM触发动作服务\n处理TPM系统的触发动作\n如数据变更后的自动处理
    }

    package "dao.mongo" {
        [BaseDAO] : 基础数据访问对象\n提供通用的CRUD操作\n包括增删改查基础功能
        [UniqueIdBaseDAO] : 唯一ID基础DAO\n提供唯一ID的数据访问\n管理唯一标识的生成
        [ActivityTypeDAO] : 活动类型数据访问对象\n处理活动类型数据\n的持久化操作
        [BudgetNewConsumeRuleDAO] : 预算消费规则DAO\n处理预算消费规则\n数据的存储和查询
        [POCRecordDAO] : POC记录数据访问对象\n处理POC活动记录\n的数据存储和查询
        [ActivityRewardRuleDAO] : 活动奖励规则DAO\n处理活动奖励规则\n数据的持久化操作
    }

    package "dao.mongo.po" {
        [MongoPO] : MongoDB基础实体\n所有MongoDB实体的基类\n包含公共字段和方法
        [ActivityTypePO] : 活动类型实体\n存储活动类型的数据\n包括活动配置和规则
        [BudgetNewConsumeRulePO] : 预算消费规则实体\n存储预算消费规则\n的配置和参数
        [POCRecordPO] : POC记录实体\n存储POC活动的记录\n包括活动数据和统计
        [ConfigPO] : 配置实体\n存储系统的配置信息\n包括各种参数设置
    }

    package "service" {
        [OrganizationServiceImpl] : 组织架构服务实现\n处理组织架构相关逻辑\n包括部门和员工信息
        [ScriptServiceImpl] : 脚本服务实现\n处理系统脚本和批处理\n包括数据初始化和迁移
        [TPMRoleService] : TPM角色服务\n处理TPM系统的角色管理\n包括权限和角色分配
        [TransactionProxy] : 事务代理\n提供事务管理功能\n确保数据一致性
    }
}

package "fs-crm-fmcg-mengniu" as MengNiuModule {
    [NewRewardService] : 新奖励服务\n处理蒙牛客户的\n新奖励业务逻辑
    [RedPacketEventDistributor] : 红包事件分发器\n处理红包事件的\n分发和路由
    [ConsumerRewardHandler] : 消费者奖励处理器\n处理消费者扫码\n奖励的业务逻辑
    package "dao" {
        [MengNiuBaseDAO] : 蒙牛基础DAO\n蒙牛模块的数据访问\n基础类和通用操作
    }
}

package "fs-crm-fmcg-ocr" as OCRModule {
    [WinePriceService] : 酒类价格识别服务\n使用OCR技术识别\n酒类产品的价格信息
    [OcrAdapter] : OCR适配器\n封装第三方OCR服务\n提供统一的调用接口
    [Analyzer] : 分析器\n对OCR识别结果\n进行分析和处理
}

package "fs-crm-fmcg-fesco" as FescoModule {
    [FescoService] : Fesco服务\n集成Fesco系统\n处理相关业务逻辑
}

package "fs-crm-fmcg-common" as CommonModule {
    package "http" {
        [ApiContextManager] : API上下文管理器\n管理请求上下文\n包括租户和用户信息
        [ApiContext] : API上下文\n存储当前请求的\n上下文信息
    }

    package "adapter" {
        [ReceiveMoneyService] : 收款服务\n处理收款相关的\n业务逻辑和集成
        [IFMCGTokenService] : FMCG令牌服务\nFMCG系统的令牌\n管理和验证服务
    }

    package "utils" {
        [EncryptionService] : 加密服务\n提供数据加密解密\n和签名验证功能
        [QueryDataUtil] : 查询数据工具\n提供数据查询的\n通用工具方法
        [SearchQueryUtil] : 搜索查询工具\n提供搜索查询的\n通用工具方法
    }

    package "gray" {
        [TPMGrayUtils] : TPM灰度发布工具\n处理TPM系统的\n灰度发布和功能开关
    }
}

package "外部框架依赖" as ExternalFramework {
    [Spring Framework] : Spring框架\n提供依赖注入、AOP\n和事务管理等功能
    [MongoDB] : MongoDB数据库\n主要的数据存储\n支持文档型数据
    [Redis] : Redis缓存\n分布式缓存和锁\n提高系统性能
    [PaaS平台] : PaaS平台服务\n提供元数据管理\n和权限控制服务
    [微信API] : 微信开放平台\n微信小程序和公众号\nAPI服务集成
    [支付宝API] : 支付宝开放平台\n支付和转账服务\nAPI服务集成
}

' 模块间依赖关系
ServiceModule --> TPMModule : 依赖
ServiceModule --> MengNiuModule : 依赖
ServiceModule --> CommonModule : 依赖

TPMModule --> CommonModule : 依赖
TPMModule --> ExternalFramework : 依赖

MengNiuModule --> CommonModule : 依赖
MengNiuModule --> TPMModule : 依赖

OCRModule --> CommonModule : 依赖

FescoModule --> CommonModule : 依赖

' 内部依赖关系
[RewardController] --> [BudgetNewConsumeRuleService]
[RewardController] --> [ScanCodeService]
[RewardController] --> [PhysicalRewardService]

[POCActivityController] --> [ActivityService]

[BudgetNewConsumeRuleService] --> [BudgetAccrualRuleManager]
[BudgetNewConsumeRuleService] --> [AsyncBudgetDisassemblyService]

[ActivityTypeManager] --> [ActivityService]
[ActivityTypeManager] --> [TPMTriggerActionService]

[ActivityService] --> [ActivityTypeDAO]
[BudgetService] --> [BudgetNewConsumeRuleDAO]

[ActivityTypeDAO] --> [ActivityTypePO]
[BudgetNewConsumeRuleDAO] --> [BudgetNewConsumeRulePO]

[BaseDAO] --> [MongoPO]
[UniqueIdBaseDAO] --> [MongoPO]

' 外部依赖
[ScanCodeService] --> [微信API]
[PhysicalRewardService] --> [支付宝API]
[BaseDAO] --> [MongoDB]
[BudgetService] --> [Redis]

note top of ServiceModule
  <b>Web层模块 (fs-crm-fmcg-service)</b>
  • 提供RESTful API接口服务
  • 处理HTTP请求路由和参数校验
  • 分为对外接口(facade)和内部接口(inner)
  • 包含定时任务和批处理功能
end note

note top of TPMModule
  <b>核心业务模块 (fs-crm-fmcg-tpm)</b>
  • TPM(贸易促销管理)的核心业务逻辑
  • 包含活动管理、预算管理、奖励管理
  • 提供完整的数据访问层和业务服务层
  • 支持异步处理和事务管理
end note

note top of MengNiuModule
  <b>蒙牛专用模块 (fs-crm-fmcg-mengniu)</b>
  • 专门为蒙牛客户定制的业务功能
  • 处理消费者扫码奖励业务
  • 红包事件分发和处理机制
  • 集成蒙牛特有的业务规则和流程
end note

note right of OCRModule
  <b>OCR识别模块 (fs-crm-fmcg-ocr)</b>
  • 提供图像识别和分析功能
  • 主要用于酒类价格识别
  • 封装第三方OCR服务接口
end note

note right of FescoModule
  <b>Fesco集成模块 (fs-crm-fmcg-fesco)</b>
  • 集成Fesco系统的业务功能
  • 处理特定的业务集成需求
end note

note top of CommonModule
  <b>公共模块 (fs-crm-fmcg-common)</b>
  • 提供通用的工具类和公共服务
  • HTTP上下文管理和请求处理
  • 加密、查询、灰度发布等工具
  • 被所有其他模块共同依赖
end note

note bottom of ExternalFramework
  <b>外部框架和服务</b>
  • Spring框架提供依赖注入和事务管理
  • MongoDB作为主要的数据存储
  • Redis提供缓存和分布式锁功能
  • 集成微信、支付宝等第三方服务
end note

@enduml
