package com.facishare.crm.fmcg.integral.controller;

import com.facishare.crm.fmcg.integral.business.BusinessTradingIndustryUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;


@SuppressWarnings("unused")
public class PointsExchangeRecordListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        BusinessTradingIndustryUtil.filterObjButtons(arg.getApiName(), result);
        BusinessTradingIndustryUtil.filterHeaderButtons(arg.getApiName(), result);
        return super.after(arg, result);
    }
}
