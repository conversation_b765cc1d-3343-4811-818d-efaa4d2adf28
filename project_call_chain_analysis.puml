@startuml CRM-FMCG-TPM项目调用链分析图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title CRM FMCG TPM 项目调用链分析图

' 定义颜色
skinparam component {
    BackgroundColor<<Controller>> #E1F5FE
    BackgroundColor<<Service>> #F3E5F5
    BackgroundColor<<Manager>> #E8F5E8
    BackgroundColor<<Business>> #FFF3E0
    BackgroundColor<<DAO>> #FFEBEE
    BackgroundColor<<Proxy>> #F1F8E9
    BackgroundColor<<Entity>> #FFF8E1
}

package "Web层 (Controller)" {
    component [TPMActivityObjEnableListController] <<Controller>> : TPM活动对象启用列表控制器\n处理活动对象的启用状态管理
    component [RewardController] <<Controller>> : 奖励控制器\n处理奖励规则的增删改查\n扫码奖励等功能
    component [POCActivityController] <<Controller>> : POC活动控制器\n处理POC(概念验证)活动\n的初始化和管理
    component [WithdrawalController] <<Controller>> : 提现控制器\n处理红包提现、查询\n提现记录等功能
    component [MengNiuRewardController] <<Controller>> : 蒙牛奖励控制器\n专门处理蒙牛客户的\n消费者扫码奖励
    component [InnerDMSController] <<Controller>> : 内部DMS控制器\n处理分销管理系统\n的内部接口调用
    component [InnerRewardController] <<Controller>> : 内部奖励控制器\n处理系统内部的\n奖励相关操作
    component [InnerYLTPMController] <<Controller>> : 内部伊利TPM控制器\n处理伊利客户专用的\nTPM功能
}

package "服务层 (Service)" {
    component [IActivityRewardRuleService] <<Service>> : 活动奖励规则服务\n管理活动的奖励规则\n包括规则的增删改查
    component [IPOCActivityService] <<Service>> : POC活动服务\n处理POC活动的业务逻辑\n包括活动初始化和举证
    component [IPhysicalRewardService] <<Service>> : 实物奖励服务\n处理实物奖品的发放\n核销和物流信息管理
    component [WithdrawService] <<Service>> : 提现服务\n处理红包提现业务\n包括提现申请和记录查询
    component [ScanCodeService] <<Service>> : 扫码服务\n处理二维码扫描业务\n包括扫码奖励和验证
    component [BudgetNewConsumeRuleService] <<Service>> : 预算消费规则服务\n管理预算的消费规则\n处理预算分配和消费
    component [BudgetClosureService] <<Service>> : 预算结算服务\n处理预算的结算和关闭\n确保预算数据一致性
    component [LicenseService] <<Service>> : 许可证服务\n管理系统功能模块的\n许可证和权限控制
    component [PhysicalRewardService] <<Service>> : 实物奖励服务实现\n实物奖励服务的具体实现\n处理奖品发放流程
    component [UnlockOuterCodeService] <<Service>> : 外部码解锁服务\n处理外部二维码的\n解锁和验证功能
    component [WechatService] <<Service>> : 微信服务\n处理微信相关功能\n包括小程序码生成等
}

package "管理层 (Manager)" {
    component [IRewardRuleManager] <<Manager>> : 奖励规则管理器\n统一管理奖励规则\n协调多个奖励服务
    component [ActivityTypeManager] <<Manager>> : 活动类型管理器\n管理活动类型的定义\n和活动模板配置
    component [BudgetAccrualRuleManager] <<Manager>> : 预算计提规则管理器\n管理预算的计提规则\n处理预算的自动计提
    component [ProofPeriodManager] <<Manager>> : 举证期间管理器\n管理活动举证的时间期间\n处理举证数据的汇总
}

package "业务逻辑层 (Business)" {
    component [ActivityService] <<Business>> : 活动业务服务\n处理活动的核心业务逻辑\n包括活动创建、审批等
    component [StoreBusiness] <<Business>> : 门店业务服务\n处理门店相关的业务逻辑\n包括门店信息管理
    component [TPMTriggerActionService] <<Business>> : TPM触发动作服务\n处理TPM系统的触发动作\n如数据变更后的自动处理
    component [IRangeFieldBusiness] <<Business>> : 范围字段业务服务\n处理范围字段的业务逻辑\n如地区、部门范围等
    component [AsyncBudgetDisassemblyService] <<Business>> : 异步预算拆解服务\n异步处理预算的拆解\n包括预算冻结和解冻
    component [TPMDisplayReportService] <<Business>> : TPM陈列报告服务\n处理陈列数据的业务逻辑\n包括陈列检测和AI分析
    component [IBudgetService] <<Business>> : 预算业务服务\n处理预算的核心业务逻辑\n包括预算计算和分配
    component [IBudgetProvisionService] <<Business>> : 预算预提服务\n处理预算的预提业务\n包括预算占用和释放
    component [IRedPacketService] <<Business>> : 红包业务服务\n处理红包的业务逻辑\n包括红包发放和管理
    component [FmcgSerialNumberService] <<Business>> : FMCG序列号服务\n处理快消品的序列号\n生成和管理
}

package "数据访问层 (DAO)" {
    component [BaseDAO] <<DAO>> : 基础数据访问对象\n提供通用的CRUD操作\n包括增删改查基础功能
    component [UniqueIdBaseDAO] <<DAO>> : 唯一ID基础DAO\n提供唯一ID的数据访问\n管理唯一标识的生成
    component [ActivityTypeDAO] <<DAO>> : 活动类型数据访问对象\n处理活动类型数据\n的持久化操作
    component [BudgetNewConsumeRuleDAO] <<DAO>> : 预算消费规则DAO\n处理预算消费规则\n数据的存储和查询
    component [BudgetAccrualRuleDAO] <<DAO>> : 预算计提规则DAO\n处理预算计提规则\n数据的持久化
    component [POCRecordDAO] <<DAO>> : POC记录数据访问对象\n处理POC活动记录\n的数据存储和查询
    component [ActivityRewardRuleDAO] <<DAO>> : 活动奖励规则DAO\n处理活动奖励规则\n数据的持久化操作
    component [ConfigDAO] <<DAO>> : 配置数据访问对象\n处理系统配置数据\n的存储和管理
    component [CommonDAO] <<DAO>> : 通用数据访问对象\n提供通用的数据操作\n支持多种数据类型
}

package "实体层 (Entity/PO)" {
    component [MongoPO] <<Entity>> : MongoDB基础实体\n所有MongoDB实体的基类\n包含公共字段和方法
    component [ActivityTypePO] <<Entity>> : 活动类型实体\n存储活动类型的数据\n包括活动配置和规则
    component [BudgetNewConsumeRulePO] <<Entity>> : 预算消费规则实体\n存储预算消费规则\n的配置和参数
    component [POCRecordPO] <<Entity>> : POC记录实体\n存储POC活动的记录\n包括活动数据和统计
    component [ActivityRewardRulePO] <<Entity>> : 活动奖励规则实体\n存储活动奖励规则\n的配置和参数
    component [ConfigPO] <<Entity>> : 配置实体\n存储系统的配置信息\n包括各种参数设置
    component [BizCodePO] <<Entity>> : 业务码实体\n存储业务码的信息\n包括码的状态和数据
}

package "外部服务代理 (Proxy)" {
    component [FmcgServiceProxy] <<Proxy>> : FMCG服务代理\n调用FMCG相关的外部服务\n包括数据报告和分析
    component [FmcgCrmProxy] <<Proxy>> : FMCG CRM代理\n调用CRM系统的外部接口\n处理客户关系管理
    component [CheckinProxy] <<Proxy>> : 签到服务代理\n调用签到系统的接口\n处理门店签到相关功能
    component [WechatProxy] <<Proxy>> : 微信服务代理\n调用微信相关的API\n包括小程序和公众号
    component [PaasDataProxy] <<Proxy>> : PaaS数据代理\n调用PaaS平台的数据服务\n处理元数据和权限
    component [CrmWorkflowProxy] <<Proxy>> : CRM工作流代理\n调用CRM工作流系统\n处理审批流程
    component [TransactionProxy] <<Proxy>> : 事务代理\n提供事务管理功能\n确保数据一致性
}

package "契约层 (Contract/API)" {
    component [POCBase] <<Contract>> : POC基础契约\n定义POC活动的\n请求和响应结构
    component [GetConsumeObject] <<Contract>> : 获取消费对象契约\n定义消费对象查询\n的接口规范
    component [Freeze/Unfreeze] <<Contract>> : 冻结/解冻契约\n定义预算冻结和解冻\n的接口规范
    component [EndConsume] <<Contract>> : 结束消费契约\n定义预算消费结束\n的接口规范
    component [GetConfig] <<Contract>> : 获取配置契约\n定义系统配置查询\n的接口规范
    component [InnerApiResult] <<Contract>> : 内部API结果契约\n定义内部接口\n的统一响应格式
}

package "基础设施层" {
    component [MongoDB] as mongo : MongoDB数据库\n主要的数据存储\n存储业务数据
    component [Redis] as redis : Redis缓存\n缓存和分布式锁\n提高系统性能
    component [外部API] as external : 外部第三方服务\n微信、支付宝等\n外部服务接口
}

' 调用关系 - Controller到Service
[TPMActivityObjEnableListController] --> [IActivityRewardRuleService]
[RewardController] --> [IActivityRewardRuleService]
[RewardController] --> [IRewardRuleManager]
[RewardController] --> [ScanCodeService]
[RewardController] --> [IPhysicalRewardService]
[POCActivityController] --> [IPOCActivityService]
[WithdrawalController] --> [WithdrawService]
[MengNiuRewardController] --> [ScanCodeService]
[InnerDMSController] --> [IPhysicalRewardService]

' Service到Manager
[IActivityRewardRuleService] --> [IRewardRuleManager]
[BudgetNewConsumeRuleService] --> [BudgetAccrualRuleManager]

' Service到Business
[IPhysicalRewardService] --> [IRedPacketService]
[IPhysicalRewardService] --> [FmcgSerialNumberService]
[BudgetNewConsumeRuleService] --> [IBudgetProvisionService]
[BudgetClosureService] --> [AsyncBudgetDisassemblyService]
[ScanCodeService] --> [ActivityService]
[UnlockOuterCodeService] --> [StoreBusiness]

' Manager到Business
[ActivityTypeManager] --> [ActivityService]
[ActivityTypeManager] --> [TPMTriggerActionService]
[ProofPeriodManager] --> [TPMDisplayReportService]
[BudgetAccrualRuleManager] --> [IBudgetService]

' Business到DAO
[ActivityService] --> [ActivityTypeDAO]
[StoreBusiness] --> [BaseDAO]
[TPMTriggerActionService] --> [UniqueIdBaseDAO]
[IBudgetService] --> [BudgetNewConsumeRuleDAO]
[IBudgetProvisionService] --> [BudgetAccrualRuleDAO]
[TPMDisplayReportService] --> [POCRecordDAO]
[IRedPacketService] --> [ActivityRewardRuleDAO]

' DAO到Entity
[BaseDAO] --> [MongoPO]
[UniqueIdBaseDAO] --> [MongoPO]
[ActivityTypeDAO] --> [ActivityTypePO]
[BudgetNewConsumeRuleDAO] --> [BudgetNewConsumeRulePO]
[POCRecordDAO] --> [POCRecordPO]
[ActivityRewardRuleDAO] --> [ActivityRewardRulePO]
[ConfigDAO] --> [ConfigPO]

' Service到Proxy
[ScanCodeService] --> [FmcgServiceProxy]
[ScanCodeService] --> [CheckinProxy]
[WechatService] --> [WechatProxy]
[LicenseService] --> [PaasDataProxy]
[PhysicalRewardService] --> [CrmWorkflowProxy]
[BudgetNewConsumeRuleService] --> [TransactionProxy]

' DAO到基础设施
[BaseDAO] --> mongo
[UniqueIdBaseDAO] --> mongo
[ActivityTypeDAO] --> mongo
[BudgetNewConsumeRuleDAO] --> mongo

' Service到基础设施
[ScanCodeService] --> redis
[LicenseService] --> redis

' Proxy到外部服务
[FmcgServiceProxy] --> external
[FmcgCrmProxy] --> external
[CheckinProxy] --> external
[WechatProxy] --> external
[PaasDataProxy] --> external
[CrmWorkflowProxy] --> external

' 契约层关系
[TPMActivityObjEnableListController] ..> [POCBase] : uses
[RewardController] ..> [GetConsumeObject] : uses
[InnerDMSController] ..> [InnerApiResult] : uses
[BudgetClosureService] ..> [EndConsume] : uses

note top of [TPMActivityObjEnableListController]
  <b>主要功能:</b>
  • TPM活动对象的启用管理
  • 活动对象列表查询和筛选
  • 活动状态的批量操作
end note

note top of [RewardController]
  <b>主要功能:</b>
  • 奖励规则的CRUD操作
  • 扫码奖励的处理流程
  • 奖励规则的校验和审批
  • 实物奖励的管理
end note

note top of [AsyncBudgetDisassemblyService]
  <b>主要功能:</b>
  • 异步处理预算拆解任务
  • 预算的冻结和解冻操作
  • 分布式锁的管理
  • 预算拆解状态的跟踪
end note

note top of [TPMDisplayReportService]
  <b>主要功能:</b>
  • 陈列数据的收集和处理
  • AI图像识别和分析
  • 陈列报告的生成
  • 陈列数据的校验和审核
end note

note right of [ScanCodeService]
  <b>核心功能:</b>
  • 二维码的扫描和解析
  • 扫码奖励的计算和发放
  • 微信小程序集成
  • 红包和实物奖励的处理
end note

note bottom of mongo
  <b>MongoDB数据库</b>
  • 主要的业务数据存储
  • 活动、预算、奖励等数据
  • 支持复杂查询和聚合操作
end note

note bottom of redis
  <b>Redis缓存</b>
  • 系统缓存和会话管理
  • 分布式锁的实现
  • 异步任务的状态管理
end note

note bottom of external
  <b>外部服务集成</b>
  • 微信小程序和公众号API
  • 支付宝支付和转账服务
  • PaaS平台的元数据服务
  • 第三方AI和数据分析服务
end note

@enduml
