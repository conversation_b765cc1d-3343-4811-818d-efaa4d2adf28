package com.facishare.crm.fmcg.tongfu.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class TongFuAuthInfo implements Serializable {

    @JSONField(name = "tenant_id")
    @JsonProperty(value = "tenant_id")
    @SerializedName("tenant_id")
    private String tenantId;

    @JSONField(name = "task_home_page_url")
    @JsonProperty(value = "task_home_page_url")
    @SerializedName("task_home_page_url")
    private String url;

    @JSONField(name = "task_home_page_source")
    @JsonProperty(value = "task_home_page_source")
    @SerializedName("task_home_page_source")
    private String source;

    @JSONField(name = "task_home_page_key")
    @JsonProperty(value = "task_home_page_key")
    @SerializedName("task_home_page_key")
    private String appKey;

    @JSONField(name = "task_home_page_salt")
    @JsonProperty(value = "task_home_page_salt")
    @SerializedName("task_home_page_salt")
    private String salt;

    @JSONField(name = "task_home_page_tenant_id")
    @JsonProperty(value = "task_home_page_tenant_id")
    @SerializedName("task_home_page_tenant_id")
    private int authTenantId;

    @JSONField(name = "director")
    @JsonProperty(value = "director")
    @SerializedName("director")
    private String director;

    @JSONField(name = "war_zone")
    @JsonProperty(value = "war_zone")
    @SerializedName("war_zone")
    private String warZone;


}
